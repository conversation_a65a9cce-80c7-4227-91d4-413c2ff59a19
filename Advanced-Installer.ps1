# مثبت مؤسسة وقود المستقبل المتقدم - PowerShell
# Advanced Future Fuel Corporation Installer - PowerShell Edition

# تعيين ترميز UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

# متغيرات النظام
$AppName = "مؤسسة وقود المستقبل"
$Version = "2.3.0"
$Company = "Future Fuel Corporation"
$InstallDir = "$env:USERPROFILE\Desktop\CFGPLProgram"
$LogFile = "$env:TEMP\CFGPLProgram_Install.log"

# دالة كتابة السجل
function Write-Log {
    param([string]$Message)
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "[$Timestamp] $Message" | Out-File -FilePath $LogFile -Append -Encoding UTF8
    Write-Host $Message
}

# دالة عرض العنوان
function Show-Header {
    Clear-Host
    Write-Host ""
    Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Cyan
    Write-Host "║                                                              ║" -ForegroundColor Cyan
    Write-Host "║    🏢 مثبت مؤسسة وقود المستقبل المتقدم                      ║" -ForegroundColor Yellow
    Write-Host "║    Advanced Future Fuel Corporation Installer               ║" -ForegroundColor Yellow
    Write-Host "║                                                              ║" -ForegroundColor Cyan
    Write-Host "║    الإصدار: $Version                                        ║" -ForegroundColor Green
    Write-Host "║    Version: $Version                                        ║" -ForegroundColor Green
    Write-Host "║                                                              ║" -ForegroundColor Cyan
    Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Cyan
    Write-Host ""
}

# دالة فحص الصلاحيات
function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# دالة فحص المساحة المتاحة
function Test-DiskSpace {
    param([string]$Path, [long]$RequiredSpace = 500MB)
    
    $Drive = (Get-Item $Path).PSDrive
    $FreeSpace = $Drive.Free
    
    return $FreeSpace -gt $RequiredSpace
}

# دالة إنشاء الاختصارات
function New-Shortcut {
    param(
        [string]$ShortcutPath,
        [string]$TargetPath,
        [string]$WorkingDirectory,
        [string]$Description,
        [string]$IconLocation
    )
    
    try {
        $WshShell = New-Object -ComObject WScript.Shell
        $Shortcut = $WshShell.CreateShortcut($ShortcutPath)
        $Shortcut.TargetPath = $TargetPath
        $Shortcut.WorkingDirectory = $WorkingDirectory
        $Shortcut.Description = $Description
        if ($IconLocation) { $Shortcut.IconLocation = $IconLocation }
        $Shortcut.Save()
        return $true
    }
    catch {
        Write-Log "خطأ في إنشاء الاختصار: $($_.Exception.Message)"
        return $false
    }
}

# بدء التثبيت
Write-Log "بدء عملية التثبيت"
Show-Header

Write-Host "🔧 مثبت ذكي مع ميزات متقدمة:" -ForegroundColor Yellow
Write-Host "   ✓ فحص النظام والتوافق" -ForegroundColor Green
Write-Host "   ✓ نسخ احتياطية تلقائية" -ForegroundColor Green
Write-Host "   ✓ إصلاح الأخطاء التلقائي" -ForegroundColor Green
Write-Host "   ✓ تحسين الأداء" -ForegroundColor Green
Write-Host "   ✓ دعم Windows 8/10/11" -ForegroundColor Green
Write-Host ""

# فحص الصلاحيات
Write-Host "🔐 فحص الصلاحيات..." -ForegroundColor Yellow
if (Test-AdminRights) {
    Write-Host "✅ تم تشغيل المثبت بصلاحيات المدير" -ForegroundColor Green
    Write-Log "صلاحيات المدير: متوفرة"
} else {
    Write-Host "⚠️  تحذير: يُنصح بتشغيل المثبت بصلاحيات المدير" -ForegroundColor Yellow
    Write-Host "   للحصول على أفضل أداء وتوافق" -ForegroundColor Yellow
    Write-Log "صلاحيات المدير: غير متوفرة"
    
    $Continue = Read-Host "هل تريد المتابعة؟ (Y/N)"
    if ($Continue -ne "Y" -and $Continue -ne "y") {
        Write-Host "❌ تم إلغاء التثبيت" -ForegroundColor Red
        exit
    }
}

# فحص نظام التشغيل
Write-Host ""
Write-Host "🖥️  فحص نظام التشغيل..." -ForegroundColor Yellow
$OSVersion = [System.Environment]::OSVersion.Version
$OSName = (Get-WmiObject -Class Win32_OperatingSystem).Caption
Write-Host "   نظام التشغيل: $OSName" -ForegroundColor Cyan
Write-Log "نظام التشغيل: $OSName"

# فحص المساحة المتاحة
Write-Host ""
Write-Host "💾 فحص المساحة المتاحة..." -ForegroundColor Yellow
if (Test-DiskSpace -Path $env:USERPROFILE) {
    Write-Host "✅ المساحة المتاحة كافية" -ForegroundColor Green
} else {
    Write-Host "❌ مساحة غير كافية على القرص" -ForegroundColor Red
    Write-Host "   المطلوب: 500 ميجابايت على الأقل" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit
}

# فحص التثبيت السابق
Write-Host ""
Write-Host "🔍 فحص التثبيت السابق..." -ForegroundColor Yellow
if (Test-Path $InstallDir) {
    Write-Host "⚠️  تم العثور على تثبيت سابق" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "اختر العملية المطلوبة:" -ForegroundColor Cyan
    Write-Host "[1] تحديث التثبيت الحالي (يحتفظ بالبيانات)" -ForegroundColor Green
    Write-Host "[2] إعادة تثبيت كاملة (يحذف البيانات القديمة)" -ForegroundColor Yellow
    Write-Host "[3] إلغاء التثبيت" -ForegroundColor Red
    Write-Host ""
    
    $Choice = Read-Host "اختر (1-3)"
    
    switch ($Choice) {
        "1" {
            Write-Host "🔄 سيتم تحديث التثبيت الحالي..." -ForegroundColor Green
            $UpdateMode = $true
        }
        "2" {
            Write-Host "🗑️  سيتم حذف التثبيت السابق..." -ForegroundColor Yellow
            Remove-Item -Path $InstallDir -Recurse -Force -ErrorAction SilentlyContinue
            $UpdateMode = $false
        }
        default {
            Write-Host "❌ تم إلغاء التثبيت" -ForegroundColor Red
            exit
        }
    }
} else {
    Write-Host "✅ لا يوجد تثبيت سابق" -ForegroundColor Green
    $UpdateMode = $false
}

# إنشاء نسخة احتياطية
if ($UpdateMode) {
    Write-Host ""
    Write-Host "💾 إنشاء نسخة احتياطية..." -ForegroundColor Yellow
    $BackupName = "backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    $BackupDir = "$InstallDir\backups\$BackupName"
    
    if (Test-Path "$InstallDir\data") {
        try {
            New-Item -Path $BackupDir -ItemType Directory -Force | Out-Null
            Copy-Item -Path "$InstallDir\data" -Destination "$BackupDir\data" -Recurse -Force
            Write-Host "✅ تم إنشاء نسخة احتياطية: $BackupName" -ForegroundColor Green
            Write-Log "نسخة احتياطية: $BackupName"
        }
        catch {
            Write-Host "⚠️  تعذر إنشاء نسخة احتياطية" -ForegroundColor Yellow
        }
    }
}

# إنشاء مجلد التطبيق
Write-Host ""
Write-Host "📁 إعداد مجلد التطبيق..." -ForegroundColor Yellow
try {
    if (-not (Test-Path $InstallDir)) {
        New-Item -Path $InstallDir -ItemType Directory -Force | Out-Null
    }
    Write-Host "✅ تم إعداد المجلد: $InstallDir" -ForegroundColor Green
}
catch {
    Write-Host "❌ خطأ في إنشاء المجلد" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit
}

# إنشاء مجلدات فرعية
Write-Host "📂 إنشاء المجلدات الفرعية..." -ForegroundColor Yellow
$SubFolders = @("data", "logs", "backups", "temp", "assets", "scripts", "styles", "src")
foreach ($Folder in $SubFolders) {
    $FolderPath = Join-Path $InstallDir $Folder
    if (-not (Test-Path $FolderPath)) {
        New-Item -Path $FolderPath -ItemType Directory -Force | Out-Null
    }
}
Write-Host "✅ تم إنشاء جميع المجلدات الفرعية" -ForegroundColor Green

# نسخ الملفات
Write-Host ""
Write-Host "📋 نسخ ملفات التطبيق..." -ForegroundColor Yellow
Write-Host "   ⏳ جاري النسخ... يرجى الانتظار" -ForegroundColor Cyan

try {
    $SourcePath = Get-Location
    $ExcludeList = @("Enhanced-Installer.bat", "Advanced-Installer.ps1", "Quick-Install.bat", "exclude_list.txt", "*.log", "*.tmp")
    
    Get-ChildItem -Path $SourcePath -Recurse | Where-Object {
        $Exclude = $false
        foreach ($Pattern in $ExcludeList) {
            if ($_.Name -like $Pattern) {
                $Exclude = $true
                break
            }
        }
        -not $Exclude
    } | ForEach-Object {
        $DestPath = $_.FullName.Replace($SourcePath, $InstallDir)
        $DestDir = Split-Path $DestPath -Parent
        
        if (-not (Test-Path $DestDir)) {
            New-Item -Path $DestDir -ItemType Directory -Force | Out-Null
        }
        
        if (-not $_.PSIsContainer) {
            Copy-Item -Path $_.FullName -Destination $DestPath -Force
        }
    }
    
    Write-Host "✅ تم نسخ جميع الملفات بنجاح" -ForegroundColor Green
    Write-Log "نسخ الملفات: نجح"
}
catch {
    Write-Host "❌ خطأ في نسخ الملفات: $($_.Exception.Message)" -ForegroundColor Red
    Write-Log "نسخ الملفات: فشل - $($_.Exception.Message)"
    Read-Host "اضغط Enter للخروج"
    exit
}

# إنشاء الاختصارات
Write-Host ""
Write-Host "🔗 إنشاء الاختصارات..." -ForegroundColor Yellow

# اختصار سطح المكتب
Write-Host "   📌 اختصار سطح المكتب..." -ForegroundColor Cyan
$DesktopShortcut = "$env:USERPROFILE\Desktop\$AppName.lnk"
if (New-Shortcut -ShortcutPath $DesktopShortcut -TargetPath "$InstallDir\CFGPLProgram.exe" -WorkingDirectory $InstallDir -Description "نظام إدارة مؤسسة وقود المستقبل") {
    Write-Host "✅ تم إنشاء اختصار سطح المكتب" -ForegroundColor Green
} else {
    Write-Host "⚠️  تعذر إنشاء اختصار سطح المكتب" -ForegroundColor Yellow
}

# اختصار قائمة ابدأ
Write-Host "   📋 اختصار قائمة ابدأ..." -ForegroundColor Cyan
$StartMenuDir = "$env:APPDATA\Microsoft\Windows\Start Menu\Programs\$AppName"
if (-not (Test-Path $StartMenuDir)) {
    New-Item -Path $StartMenuDir -ItemType Directory -Force | Out-Null
}
$StartMenuShortcut = "$StartMenuDir\$AppName.lnk"
New-Shortcut -ShortcutPath $StartMenuShortcut -TargetPath "$InstallDir\CFGPLProgram.exe" -WorkingDirectory $InstallDir -Description "نظام إدارة مؤسسة وقود المستقبل" | Out-Null

Write-Host "✅ تم إنشاء جميع الاختصارات" -ForegroundColor Green

# تسجيل التطبيق في النظام
Write-Host ""
Write-Host "📝 تسجيل التطبيق في النظام..." -ForegroundColor Yellow
try {
    $RegPath = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Uninstall\CFGPLProgram"
    New-Item -Path $RegPath -Force | Out-Null
    Set-ItemProperty -Path $RegPath -Name "DisplayName" -Value $AppName
    Set-ItemProperty -Path $RegPath -Name "DisplayVersion" -Value $Version
    Set-ItemProperty -Path $RegPath -Name "Publisher" -Value $Company
    Set-ItemProperty -Path $RegPath -Name "InstallLocation" -Value $InstallDir
    Set-ItemProperty -Path $RegPath -Name "UninstallString" -Value "$InstallDir\uninstall.bat"
    Set-ItemProperty -Path $RegPath -Name "InstallDate" -Value (Get-Date -Format "yyyyMMdd")
    Write-Host "✅ تم تسجيل التطبيق في النظام" -ForegroundColor Green
}
catch {
    Write-Host "⚠️  تعذر تسجيل التطبيق في النظام" -ForegroundColor Yellow
}

# إنشاء ملف إلغاء التثبيت
Write-Host ""
Write-Host "🗑️  إنشاء ملف إلغاء التثبيت..." -ForegroundColor Yellow
$UninstallScript = @"
@echo off
chcp 65001 >nul
title إلغاء تثبيت $AppName

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║    🗑️  إلغاء تثبيت $AppName                               ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo هل أنت متأكد من رغبتك في إلغاء تثبيت التطبيق؟
echo سيتم حذف جميع الملفات والإعدادات.
echo.
echo [Y] نعم، احذف التطبيق
echo [N] لا، إلغاء العملية
echo.
set /p confirm=اختر (Y/N):

if /i "%confirm%"=="Y" (
    echo.
    echo 🔄 جاري إلغاء التثبيت...

    REM إيقاف التطبيق إذا كان يعمل
    taskkill /f /im CFGPLProgram.exe >nul 2>&1

    REM حذف الاختصارات
    del "%USERPROFILE%\Desktop\$AppName.lnk" >nul 2>&1
    rmdir /s /q "%APPDATA%\Microsoft\Windows\Start Menu\Programs\$AppName" >nul 2>&1

    REM حذف التسجيل من النظام
    reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\CFGPLProgram" /f >nul 2>&1

    REM حذف ملفات التطبيق
    cd /d "%USERPROFILE%\Desktop"
    rmdir /s /q "CFGPLProgram" >nul 2>&1

    echo ✅ تم إلغاء التثبيت بنجاح
    echo.
    echo شكراً لاستخدام $AppName!
) else (
    echo ❌ تم إلغاء العملية
)

echo.
pause
"@

$UninstallScript | Out-File -FilePath "$InstallDir\uninstall.bat" -Encoding UTF8
Write-Host "✅ تم إنشاء ملف إلغاء التثبيت" -ForegroundColor Green

# إنشاء ملف معلومات التثبيت
Write-Host ""
Write-Host "📄 إنشاء ملف معلومات التثبيت..." -ForegroundColor Yellow
$InfoContent = @"
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    📋 معلومات التثبيت - Installation Information             ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝

🏢 اسم التطبيق: $AppName
📦 الإصدار: $Version
🏭 الشركة: $Company
📅 تاريخ التثبيت: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
📁 مسار التثبيت: $InstallDir
🖥️  نظام التشغيل: $OSName

═══════════════════════════════════════════════════════════════
🚀 طرق تشغيل التطبيق:
═══════════════════════════════════════════════════════════════

1️⃣  من سطح المكتب:
   👆 انقر مرتين على اختصار "$AppName"

2️⃣  من قائمة ابدأ:
   🔍 ابحث عن "$AppName" في قائمة ابدأ

3️⃣  من مجلد التطبيق:
   📂 افتح المجلد: $InstallDir
   🖱️  انقر مرتين على CFGPLProgram.exe

═══════════════════════════════════════════════════════════════
🛠️  الدعم والمساعدة:
═══════════════════════════════════════════════════════════════

📧 البريد الإلكتروني: <EMAIL>
🌐 الموقع الإلكتروني: https://futurefuel.sa
📞 الهاتف: +966-XX-XXX-XXXX

═══════════════════════════════════════════════════════════════
🔧 استكشاف الأخطاء وإصلاحها:
═══════════════════════════════════════════════════════════════

❓ إذا واجهت مشاكل في التشغيل:
   1. تأكد من تشغيل التطبيق كمدير
   2. تحقق من إعدادات مكافح الفيروسات
   3. أعد تشغيل الكمبيوتر
   4. أعد تثبيت التطبيق

🔄 للحصول على آخر التحديثات:
   قم بزيارة موقعنا الإلكتروني أو تواصل معنا
"@

$InfoContent | Out-File -FilePath "$InstallDir\معلومات-التثبيت.txt" -Encoding UTF8
Write-Host "✅ تم إنشاء ملف معلومات التثبيت" -ForegroundColor Green

# إنهاء التثبيت
Write-Log "انتهاء عملية التثبيت بنجاح"

Clear-Host
Write-Host ""
Write-Host "╔══════════════════════════════════════════════════════════════╗" -ForegroundColor Green
Write-Host "║                                                              ║" -ForegroundColor Green
Write-Host "║    🎉 تم تثبيت التطبيق بنجاح!                              ║" -ForegroundColor Yellow
Write-Host "║    Installation Completed Successfully!                     ║" -ForegroundColor Yellow
Write-Host "║                                                              ║" -ForegroundColor Green
Write-Host "╚══════════════════════════════════════════════════════════════╝" -ForegroundColor Green
Write-Host ""

Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "📋 ملخص التثبيت:" -ForegroundColor Yellow
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""
Write-Host "🏢 التطبيق: $AppName" -ForegroundColor White
Write-Host "📦 الإصدار: $Version" -ForegroundColor White
Write-Host "📁 المسار: $InstallDir" -ForegroundColor White
Write-Host "📅 التاريخ: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor White
Write-Host ""

Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "✅ تم إنشاء:" -ForegroundColor Yellow
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""
Write-Host "🖥️  اختصار سطح المكتب" -ForegroundColor Green
Write-Host "📋 اختصار قائمة ابدأ" -ForegroundColor Green
Write-Host "🗑️  ملف إلغاء التثبيت" -ForegroundColor Green
Write-Host "📄 ملف معلومات التثبيت" -ForegroundColor Green
Write-Host "💾 نظام النسخ الاحتياطية" -ForegroundColor Green
Write-Host ""

Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "🚀 هل تريد تشغيل التطبيق الآن؟" -ForegroundColor Yellow
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""
Write-Host "[Y] نعم، شغل التطبيق" -ForegroundColor Green
Write-Host "[N] لا، إنهاء المثبت" -ForegroundColor Red
Write-Host "[I] عرض معلومات التثبيت" -ForegroundColor Cyan
Write-Host ""

$LaunchChoice = Read-Host "اختر (Y/N/I)"

switch ($LaunchChoice.ToUpper()) {
    "Y" {
        Write-Host ""
        Write-Host "🚀 تشغيل التطبيق..." -ForegroundColor Yellow
        try {
            Start-Process -FilePath "$InstallDir\CFGPLProgram.exe" -WorkingDirectory $InstallDir
            Write-Host "✅ تم تشغيل التطبيق بنجاح" -ForegroundColor Green
        }
        catch {
            Write-Host "❌ خطأ في تشغيل التطبيق: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    "I" {
        Write-Host ""
        Write-Host "📄 فتح ملف معلومات التثبيت..." -ForegroundColor Yellow
        try {
            Start-Process -FilePath "notepad.exe" -ArgumentList "$InstallDir\معلومات-التثبيت.txt"
        }
        catch {
            Write-Host "❌ خطأ في فتح ملف المعلومات" -ForegroundColor Red
        }
    }
    default {
        Write-Host ""
        Write-Host "✅ تم إنهاء المثبت" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host "🙏 شكراً لاستخدام مؤسسة وقود المستقبل!" -ForegroundColor Yellow
Write-Host "Thank you for using Future Fuel Corporation!" -ForegroundColor Yellow
Write-Host "═══════════════════════════════════════════════════════════════" -ForegroundColor Cyan
Write-Host ""
Write-Host "💡 نصائح مهمة:" -ForegroundColor Yellow
Write-Host "   • احتفظ بنسخة احتياطية من بياناتك" -ForegroundColor White
Write-Host "   • تحقق من التحديثات بانتظام" -ForegroundColor White
Write-Host "   • تواصل معنا للدعم الفني" -ForegroundColor White
Write-Host ""
Write-Host "📧 الدعم: <EMAIL>" -ForegroundColor Cyan
Write-Host "🌐 الموقع: https://futurefuel.sa" -ForegroundColor Cyan
Write-Host ""

Read-Host "اضغط Enter للخروج"
