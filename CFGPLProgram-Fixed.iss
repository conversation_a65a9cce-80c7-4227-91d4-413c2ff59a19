[Setup]
; معلومات التطبيق
AppName=CNG Cards Management System
AppVersion=1.0.0
AppVerName=CNG Cards Management System 1.0.0
AppPublisher=CNG Company
AppPublisherURL=https://cfgpl.com
AppSupportURL=https://cfgpl.com/support
AppUpdatesURL=https://cfgpl.com/updates
AppCopyright=Copyright 2024 CNG Company

; مجلد التثبيت
DefaultDirName={pf}\CFGPLProgram
DefaultGroupName=CNG Cards Management
AllowNoIcons=yes

; ملف الإخراج
OutputDir=installer
OutputBaseFilename=CFGPLProgram-Setup-v1.0.0
Compression=lzma
SolidCompression=yes

; متطلبات النظام
MinVersion=6.1
ArchitecturesAllowed=x64 x86
PrivilegesRequired=admin

; خيارات التثبيت
DisableProgramGroupPage=yes
DisableWelcomePage=no
DisableFinishedPage=no
DisableDirPage=no
DisableReadyPage=no

; اللغة
ShowLanguageDialog=auto

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "Create a desktop shortcut"; GroupDescription: "Additional shortcuts:"
Name: "startmenu"; Description: "Add to Start Menu"; GroupDescription: "Additional shortcuts:"

[Files]
; الملف التنفيذي الرئيسي
Source: "-.exe"; DestDir: "{app}"; DestName: "CFGPLProgram.exe"; Flags: ignoreversion

; الملفات الأساسية
Source: "*.dll"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "*.pak"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "*.dat"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "*.bin"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "*.json"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "version"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "LICENSE"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
Source: "LICENSES.chromium.html"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist

; المجلدات
Source: "resources\*"; DestDir: "{app}\resources"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist
Source: "locales\*"; DestDir: "{app}\locales"; Flags: ignoreversion recursesubdirs createallsubdirs skipifsourcedoesntexist

[Icons]
; اختصار في قائمة البرامج
Name: "{group}\CNG Cards Management"; Filename: "{app}\CFGPLProgram.exe"; WorkingDir: "{app}"; Tasks: startmenu
Name: "{group}\Uninstall CNG Cards Management"; Filename: "{uninstallexe}"; Tasks: startmenu

; اختصار على سطح المكتب
Name: "{autodesktop}\CNG Cards Management"; Filename: "{app}\CFGPLProgram.exe"; WorkingDir: "{app}"; Tasks: desktopicon

[Registry]
; تسجيل التطبيق في النظام
Root: HKLM; Subkey: "Software\CFGPLProgram"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "Software\CFGPLProgram"; ValueType: string; ValueName: "Version"; ValueData: "1.0.0"; Flags: uninsdeletekey

[Run]
; تشغيل التطبيق بعد التثبيت
Filename: "{app}\CFGPLProgram.exe"; Description: "Launch CNG Cards Management System"; Flags: nowait postinstall skipifsilent

[UninstallDelete]
; حذف الملفات المؤقتة عند إلغاء التثبيت
Type: filesandordirs; Name: "{app}\temp"
Type: filesandordirs; Name: "{app}\logs"
Type: files; Name: "{app}\*.log"

[Code]
// دالة للتحقق من متطلبات النظام
function InitializeSetup(): Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  
  // التحقق من إصدار Windows
  if Version.Major < 6 then
  begin
    MsgBox('This application requires Windows 7 or later.', mbError, MB_OK);
    Result := False;
    Exit;
  end;
  
  Result := True;
end;

// دالة للتحقق من وجود إصدار سابق
function PrepareToInstall(var NeedsRestart: Boolean): String;
var
  UninstallString: String;
  ResultCode: Integer;
begin
  Result := '';
  
  // البحث عن إصدار سابق
  if RegQueryStringValue(HKLM, 'Software\Microsoft\Windows\CurrentVersion\Uninstall\CFGPLProgram_is1', 'UninstallString', UninstallString) then
  begin
    if MsgBox('A previous version was found. Do you want to uninstall it first?', mbConfirmation, MB_YESNO) = IDYES then
    begin
      Exec(RemoveQuotes(UninstallString), '/SILENT', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
    end;
  end;
end;
