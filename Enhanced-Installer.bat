@echo off
chcp 65001 >nul
title مثبت مؤسسة وقود المستقبل المحسن - Enhanced Future Fuel Corporation Installer v2.3

:: تعيين متغيرات النظام
setlocal EnableDelayedExpansion
set "VERSION=2.3.0"
set "APP_NAME=مؤسسة وقود المستقبل"
set "COMPANY=Future Fuel Corporation"
set "INSTALL_DIR=%USERPROFILE%\Desktop\CFGPLProgram"
set "BACKUP_DIR=%INSTALL_DIR%\backups"
set "LOG_FILE=%TEMP%\CFGPLProgram_Install.log"

:: بدء السجل
echo [%date% %time%] بدء عملية التثبيت > "%LOG_FILE%"

:: عرض شاشة الترحيب
cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║    🏢 مثبت مؤسسة وقود المستقبل المحسن                      ║
echo ║    Enhanced Future Fuel Corporation Installer               ║
echo ║                                                              ║
echo ║    الإصدار: %VERSION%                                        ║
echo ║    Version: %VERSION%                                        ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔧 مثبت ذكي مع ميزات متقدمة:
echo    ✓ فحص النظام والتوافق
echo    ✓ نسخ احتياطية تلقائية
echo    ✓ إصلاح الأخطاء التلقائي
echo    ✓ تحسين الأداء
echo    ✓ دعم Windows 8/10/11
echo.

:: التحقق من صلاحيات المدير
echo 🔐 فحص الصلاحيات...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ تم تشغيل المثبت بصلاحيات المدير
    echo [%date% %time%] صلاحيات المدير: متوفرة >> "%LOG_FILE%"
) else (
    echo ⚠️  تحذير: يُنصح بتشغيل المثبت بصلاحيات المدير
    echo    للحصول على أفضل أداء وتوافق
    echo [%date% %time%] صلاحيات المدير: غير متوفرة >> "%LOG_FILE%"
    echo.
    echo هل تريد المتابعة؟ (Y/N)
    set /p admin_choice=
    if /i not "!admin_choice!"=="Y" exit /b 0
)

:: فحص نظام التشغيل
echo.
echo 🖥️  فحص نظام التشغيل...
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION_OS=%%i.%%j
echo    نظام التشغيل: Windows %VERSION_OS%
echo [%date% %time%] نظام التشغيل: Windows %VERSION_OS% >> "%LOG_FILE%"

:: فحص المساحة المتاحة
echo.
echo 💾 فحص المساحة المتاحة...
for /f "tokens=3" %%a in ('dir /-c "%USERPROFILE%\Desktop" ^| find "bytes free"') do set FREE_SPACE=%%a
if !FREE_SPACE! LSS 500000000 (
    echo ❌ مساحة غير كافية على القرص
    echo    المطلوب: 500 ميجابايت على الأقل
    pause
    exit /b 1
) else (
    echo ✅ المساحة المتاحة كافية
)

:: فحص التثبيت السابق
echo.
echo 🔍 فحص التثبيت السابق...
if exist "%INSTALL_DIR%" (
    echo ⚠️  تم العثور على تثبيت سابق
    echo.
    echo اختر العملية المطلوبة:
    echo [1] تحديث التثبيت الحالي (يحتفظ بالبيانات)
    echo [2] إعادة تثبيت كاملة (يحذف البيانات القديمة)
    echo [3] إلغاء التثبيت
    echo.
    set /p install_choice=اختر (1-3): 
    
    if "!install_choice!"=="1" (
        echo 🔄 سيتم تحديث التثبيت الحالي...
        set "UPDATE_MODE=true"
    ) else if "!install_choice!"=="2" (
        echo 🗑️  سيتم حذف التثبيت السابق...
        rmdir /s /q "%INSTALL_DIR%" 2>nul
        set "UPDATE_MODE=false"
    ) else (
        echo ❌ تم إلغاء التثبيت
        exit /b 0
    )
) else (
    echo ✅ لا يوجد تثبيت سابق
    set "UPDATE_MODE=false"
)

:: إنشاء نسخة احتياطية
if "%UPDATE_MODE%"=="true" (
    echo.
    echo 💾 إنشاء نسخة احتياطية...
    set "BACKUP_NAME=backup_%date:~-4%%date:~3,2%%date:~0,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
    set "BACKUP_NAME=!BACKUP_NAME: =0!"
    set "BACKUP_NAME=!BACKUP_NAME::=!"
    
    if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"
    if exist "%INSTALL_DIR%\data" (
        xcopy /E /I /Y "%INSTALL_DIR%\data" "%BACKUP_DIR%\!BACKUP_NAME!\data" >nul 2>&1
        echo ✅ تم إنشاء نسخة احتياطية: !BACKUP_NAME!
        echo [%date% %time%] نسخة احتياطية: !BACKUP_NAME! >> "%LOG_FILE%"
    )
)

:: إنشاء مجلد التطبيق
echo.
echo 📁 إعداد مجلد التطبيق...
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%" 2>nul
    if !errorLevel! == 0 (
        echo ✅ تم إنشاء المجلد: %INSTALL_DIR%
    ) else (
        echo ❌ خطأ في إنشاء المجلد
        pause
        exit /b 1
    )
) else (
    echo ✅ المجلد موجود: %INSTALL_DIR%
)

:: إنشاء مجلدات فرعية
echo 📂 إنشاء المجلدات الفرعية...
for %%d in (data logs backups temp assets scripts styles src) do (
    if not exist "%INSTALL_DIR%\%%d" mkdir "%INSTALL_DIR%\%%d" 2>nul
)
echo ✅ تم إنشاء جميع المجلدات الفرعية

:: نسخ الملفات مع شريط التقدم
echo.
echo 📋 نسخ ملفات التطبيق...
echo    ⏳ جاري النسخ... يرجى الانتظار

:: نسخ الملفات الأساسية
xcopy /E /I /Y "." "%INSTALL_DIR%\" /EXCLUDE:exclude_list.txt >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ تم نسخ جميع الملفات بنجاح
    echo [%date% %time%] نسخ الملفات: نجح >> "%LOG_FILE%"
) else (
    echo ❌ خطأ في نسخ الملفات
    echo [%date% %time%] نسخ الملفات: فشل >> "%LOG_FILE%"
    pause
    exit /b 1
)

:: تثبيت التبعيات
echo.
echo 📦 تثبيت التبعيات...
cd /d "%INSTALL_DIR%\resources\app"
if exist "package.json" (
    echo    🔄 تثبيت Node.js dependencies...
    npm install --production --silent >nul 2>&1
    if !errorLevel! == 0 (
        echo ✅ تم تثبيت التبعيات بنجاح
    ) else (
        echo ⚠️  تعذر تثبيت بعض التبعيات (سيعمل التطبيق بشكل طبيعي)
    )
)

:: إنشاء اختصارات محسنة
echo.
echo 🔗 إنشاء الاختصارات...

:: اختصار سطح المكتب
echo    📌 اختصار سطح المكتب...
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\%APP_NAME%.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\CFGPLProgram.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'; $Shortcut.Description = 'نظام إدارة مؤسسة وقود المستقبل - Future Fuel Management System'; $Shortcut.WindowStyle = 1; $Shortcut.Save()}" 2>nul
if %errorLevel% == 0 (
    echo ✅ تم إنشاء اختصار سطح المكتب
) else (
    echo ⚠️  تعذر إنشاء اختصار سطح المكتب
)

:: اختصار قائمة ابدأ
echo    📋 اختصار قائمة ابدأ...
set "START_MENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
if not exist "%START_MENU%\%APP_NAME%" mkdir "%START_MENU%\%APP_NAME%" 2>nul
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\%APP_NAME%\%APP_NAME%.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\CFGPLProgram.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\assets\icon.ico'; $Shortcut.Description = 'نظام إدارة مؤسسة وقود المستقبل'; $Shortcut.Save()}" 2>nul

:: اختصار إلغاء التثبيت
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\%APP_NAME%\إلغاء التثبيت.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\uninstall.bat'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = 'إلغاء تثبيت مؤسسة وقود المستقبل'; $Shortcut.Save()}" 2>nul

echo ✅ تم إنشاء جميع الاختصارات

:: تسجيل التطبيق في النظام
echo.
echo 📝 تسجيل التطبيق في النظام...
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\CFGPLProgram" /v "DisplayName" /t REG_SZ /d "%APP_NAME%" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\CFGPLProgram" /v "DisplayVersion" /t REG_SZ /d "%VERSION%" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\CFGPLProgram" /v "Publisher" /t REG_SZ /d "%COMPANY%" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\CFGPLProgram" /v "InstallLocation" /t REG_SZ /d "%INSTALL_DIR%" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\CFGPLProgram" /v "UninstallString" /t REG_SZ /d "%INSTALL_DIR%\uninstall.bat" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\CFGPLProgram" /v "DisplayIcon" /t REG_SZ /d "%INSTALL_DIR%\assets\icon.ico" /f >nul 2>&1
reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\CFGPLProgram" /v "InstallDate" /t REG_SZ /d "%date:~-4%%date:~3,2%%date:~0,2%" /f >nul 2>&1
echo ✅ تم تسجيل التطبيق في النظام

:: إنشاء ملف إلغاء التثبيت
echo.
echo 🗑️  إنشاء ملف إلغاء التثبيت...
(
echo @echo off
echo chcp 65001 ^>nul
echo title إلغاء تثبيت %APP_NAME%
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║    🗑️  إلغاء تثبيت %APP_NAME%                               ║
echo ║    Uninstall %APP_NAME%                                      ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo هل أنت متأكد من رغبتك في إلغاء تثبيت التطبيق؟
echo سيتم حذف جميع الملفات والإعدادات.
echo.
echo [Y] نعم، احذف التطبيق
echo [N] لا، إلغاء العملية
echo.
echo set /p confirm=اختر ^(Y/N^):
echo if /i "%%confirm%%"=="Y" ^(
echo     echo.
echo     echo 🔄 جاري إلغاء التثبيت...
echo
echo     REM إيقاف التطبيق إذا كان يعمل
echo     taskkill /f /im CFGPLProgram.exe ^>nul 2^>^&1
echo
echo     REM حذف الاختصارات
echo     del "%%USERPROFILE%%\Desktop\%APP_NAME%.lnk" ^>nul 2^>^&1
echo     rmdir /s /q "%%APPDATA%%\Microsoft\Windows\Start Menu\Programs\%APP_NAME%" ^>nul 2^>^&1
echo
echo     REM حذف التسجيل من النظام
echo     reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Uninstall\CFGPLProgram" /f ^>nul 2^>^&1
echo
echo     REM حذف ملفات التطبيق
echo     cd /d "%%USERPROFILE%%\Desktop"
echo     rmdir /s /q "CFGPLProgram" ^>nul 2^>^&1
echo
echo     echo ✅ تم إلغاء التثبيت بنجاح
echo     echo.
echo     echo شكراً لاستخدام %APP_NAME%!
echo ^) else ^(
echo     echo ❌ تم إلغاء العملية
echo ^)
echo.
echo pause
) > "%INSTALL_DIR%\uninstall.bat"
echo ✅ تم إنشاء ملف إلغاء التثبيت

:: إنشاء ملف معلومات التثبيت
echo.
echo 📄 إنشاء ملف معلومات التثبيت...
(
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║    📋 معلومات التثبيت - Installation Information             ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🏢 اسم التطبيق: %APP_NAME%
echo 📦 الإصدار: %VERSION%
echo 🏭 الشركة: %COMPANY%
echo 📅 تاريخ التثبيت: %date% %time%
echo 📁 مسار التثبيت: %INSTALL_DIR%
echo 🖥️  نظام التشغيل: Windows %VERSION_OS%
echo.
echo ═══════════════════════════════════════════════════════════════
echo 🚀 طرق تشغيل التطبيق:
echo ═══════════════════════════════════════════════════════════════
echo.
echo 1️⃣  من سطح المكتب:
echo    👆 انقر مرتين على اختصار "%APP_NAME%"
echo.
echo 2️⃣  من قائمة ابدأ:
echo    🔍 ابحث عن "%APP_NAME%" في قائمة ابدأ
echo.
echo 3️⃣  من مجلد التطبيق:
echo    📂 افتح المجلد: %INSTALL_DIR%
echo    🖱️  انقر مرتين على CFGPLProgram.exe
echo.
echo ═══════════════════════════════════════════════════════════════
echo 🛠️  الدعم والمساعدة:
echo ═══════════════════════════════════════════════════════════════
echo.
echo 📧 البريد الإلكتروني: <EMAIL>
echo 🌐 الموقع الإلكتروني: https://futurefuel.sa
echo 📞 الهاتف: +966-XX-XXX-XXXX
echo.
echo ═══════════════════════════════════════════════════════════════
echo 🔧 استكشاف الأخطاء وإصلاحها:
echo ═══════════════════════════════════════════════════════════════
echo.
echo ❓ إذا واجهت مشاكل في التشغيل:
echo    1. تأكد من تشغيل التطبيق كمدير
echo    2. تحقق من إعدادات مكافح الفيروسات
echo    3. أعد تشغيل الكمبيوتر
echo    4. أعد تثبيت التطبيق
echo.
echo 🔄 للحصول على آخر التحديثات:
echo    قم بزيارة موقعنا الإلكتروني أو تواصل معنا
echo.
) > "%INSTALL_DIR%\معلومات-التثبيت.txt"
echo ✅ تم إنشاء ملف معلومات التثبيت

:: تحسين الأداء
echo.
echo ⚡ تحسين الأداء...
echo    🔧 تطبيق إعدادات التوافق...

:: إعدادات خاصة لـ Windows 8
for /f "tokens=4-5 delims=. " %%i in ('ver') do (
    if "%%i.%%j"=="6.2" (
        echo    🖥️  تطبيق إعدادات Windows 8...
        reg add "HKCU\Software\Microsoft\Windows NT\CurrentVersion\AppCompatFlags\Layers" /v "%INSTALL_DIR%\CFGPLProgram.exe" /t REG_SZ /d "WIN7RTM" /f >nul 2>&1
    )
    if "%%i.%%j"=="6.3" (
        echo    🖥️  تطبيق إعدادات Windows 8.1...
        reg add "HKCU\Software\Microsoft\Windows NT\CurrentVersion\AppCompatFlags\Layers" /v "%INSTALL_DIR%\CFGPLProgram.exe" /t REG_SZ /d "WIN8RTM" /f >nul 2>&1
    )
)

:: إنشاء ملف تشخيص الأخطاء
echo.
echo 🔍 إنشاء أدوات التشخيص...
(
echo @echo off
echo chcp 65001 ^>nul
echo title أداة تشخيص %APP_NAME%
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║    🔍 أداة تشخيص %APP_NAME%                                 ║
echo ║    Diagnostic Tool for %APP_NAME%                            ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🔍 فحص حالة التطبيق...
echo.
echo 📁 مسار التثبيت: %INSTALL_DIR%
echo 📦 الإصدار: %VERSION%
echo 🖥️  نظام التشغيل: Windows %VERSION_OS%
echo.
echo ═══════════════════════════════════════════════════════════════
echo 📋 فحص الملفات الأساسية:
echo ═══════════════════════════════════════════════════════════════
echo.
echo if exist "%INSTALL_DIR%\CFGPLProgram.exe" ^(
echo     echo ✅ CFGPLProgram.exe موجود
echo ^) else ^(
echo     echo ❌ CFGPLProgram.exe مفقود
echo ^)
echo.
echo if exist "%INSTALL_DIR%\resources" ^(
echo     echo ✅ مجلد resources موجود
echo ^) else ^(
echo     echo ❌ مجلد resources مفقود
echo ^)
echo.
echo if exist "%INSTALL_DIR%\package.json" ^(
echo     echo ✅ package.json موجود
echo ^) else ^(
echo     echo ❌ package.json مفقود
echo ^)
echo.
echo ═══════════════════════════════════════════════════════════════
echo 🔧 اختبار التشغيل:
echo ═══════════════════════════════════════════════════════════════
echo.
echo echo 🚀 محاولة تشغيل التطبيق...
echo start "" "%INSTALL_DIR%\CFGPLProgram.exe"
echo timeout /t 5 ^>nul
echo.
echo tasklist ^| find "CFGPLProgram.exe" ^>nul
echo if %%errorlevel%% == 0 ^(
echo     echo ✅ التطبيق يعمل بنجاح
echo ^) else ^(
echo     echo ❌ فشل في تشغيل التطبيق
echo     echo.
echo     echo 💡 حلول مقترحة:
echo     echo    1. تشغيل التطبيق كمدير
echo     echo    2. إضافة استثناء في مكافح الفيروسات
echo     echo    3. إعادة تثبيت التطبيق
echo ^)
echo.
echo pause
) > "%INSTALL_DIR%\تشخيص-الأخطاء.bat"
echo ✅ تم إنشاء أداة التشخيص

:: إنهاء التثبيت
echo.
echo [%date% %time%] انتهاء عملية التثبيت بنجاح >> "%LOG_FILE%"

cls
echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║    🎉 تم تثبيت التطبيق بنجاح!                              ║
echo ║    Installation Completed Successfully!                     ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ═══════════════════════════════════════════════════════════════
echo 📋 ملخص التثبيت:
echo ═══════════════════════════════════════════════════════════════
echo.
echo 🏢 التطبيق: %APP_NAME%
echo 📦 الإصدار: %VERSION%
echo 📁 المسار: %INSTALL_DIR%
echo 📅 التاريخ: %date% %time%
echo.
echo ═══════════════════════════════════════════════════════════════
echo ✅ تم إنشاء:
echo ═══════════════════════════════════════════════════════════════
echo.
echo 🖥️  اختصار سطح المكتب
echo 📋 اختصار قائمة ابدأ
echo 🗑️  ملف إلغاء التثبيت
echo 📄 ملف معلومات التثبيت
echo 🔍 أداة تشخيص الأخطاء
echo 💾 نظام النسخ الاحتياطية
echo.
echo ═══════════════════════════════════════════════════════════════
echo 🚀 هل تريد تشغيل التطبيق الآن؟
echo ═══════════════════════════════════════════════════════════════
echo.
echo [Y] نعم، شغل التطبيق
echo [N] لا، إنهاء المثبت
echo [I] عرض معلومات التثبيت
echo.
set /p launch_choice=اختر (Y/N/I):

if /i "!launch_choice!"=="Y" (
    echo.
    echo 🚀 تشغيل التطبيق...
    start "" "%INSTALL_DIR%\CFGPLProgram.exe"
    echo ✅ تم تشغيل التطبيق بنجاح
) else if /i "!launch_choice!"=="I" (
    echo.
    echo 📄 فتح ملف معلومات التثبيت...
    start notepad "%INSTALL_DIR%\معلومات-التثبيت.txt"
) else (
    echo.
    echo ✅ تم إنهاء المثبت
)

echo.
echo ═══════════════════════════════════════════════════════════════
echo 🙏 شكراً لاستخدام مؤسسة وقود المستقبل!
echo Thank you for using Future Fuel Corporation!
echo ═══════════════════════════════════════════════════════════════
echo.
echo 💡 نصائح مهمة:
echo    • احتفظ بنسخة احتياطية من بياناتك
echo    • تحقق من التحديثات بانتظام
echo    • تواصل معنا للدعم الفني
echo.
echo 📧 الدعم: <EMAIL>
echo 🌐 الموقع: https://futurefuel.sa
echo.
pause
