# دليل إنشاء ملف التثبيت

## المتطلبات المسبقة

### 1. تثبيت Inno Setup
- قم بتحميل Inno Setup من: https://jrsoftware.org/isinfo.php
- اختر الإصدار 6.x (الأحدث)
- قم بتثبيته في المسار الافتراضي: `C:\Program Files (x86)\Inno Setup 6\`

### 2. التحقق من الملفات المطلوبة
تأكد من وجود الملفات التالية في مجلد المشروع:
- `-.exe` (الملف التنفيذي الرئيسي)
- `resources/` (مجلد الموارد)
- `locales/` (مجلد اللغات)
- `CFGPLProgram-Setup.iss` (ملف إعدادات Inno Setup)

## طرق إنشاء ملف التثبيت

### الطريقة الأولى: استخدام الملف التلقائي
```bash
# تشغيل الملف التلقائي
create-installer.bat
```

### الطريقة الثانية: استخدام PowerShell
```powershell
# تشغيل PowerShell script
powershell -ExecutionPolicy Bypass -File "build-installer.ps1"
```

### الطريقة الثالثة: استخدام Batch Script
```bash
# تشغيل Batch script
build-installer.bat
```

### الطريقة الرابعة: يدوياً
1. افتح Inno Setup Compiler
2. افتح ملف `CFGPLProgram-Setup.iss`
3. انقر على "Build" أو اضغط F9
4. انتظر حتى اكتمال البناء

## ملف الإخراج

بعد نجاح العملية، ستجد ملف التثبيت في:
```
installer/CFGPLProgram-Setup-v1.0.0.exe
```

## مميزات ملف التثبيت

### الخيارات المتاحة أثناء التثبيت:
- ✅ اختيار مجلد التثبيت
- ✅ إنشاء اختصار على سطح المكتب
- ✅ إنشاء اختصار في شريط المهام السريع
- ✅ إضافة إلى قائمة ابدأ
- ✅ تشغيل تلقائي مع Windows (اختياري)

### المميزات التقنية:
- 🔧 فحص متطلبات النظام تلقائياً
- 🔧 إلغاء تثبيت الإصدارات السابقة تلقائياً
- 🔧 تسجيل التطبيق في النظام
- 🔧 إنشاء مفاتيح Registry مطلوبة
- 🔧 دعم إلغاء التثبيت الكامل

### الأمان:
- 🛡️ يتطلب صلاحيات المدير للتثبيت
- 🛡️ توقيع رقمي (يمكن إضافته لاحقاً)
- 🛡️ فحص سلامة الملفات

## استكشاف الأخطاء

### خطأ: "لم يتم العثور على Inno Setup"
**الحل:**
1. تأكد من تثبيت Inno Setup
2. تحقق من المسار في ملف `build-installer.ps1`
3. قم بتحديث المسار إذا لزم الأمر

### خطأ: "الملفات المطلوبة مفقودة"
**الحل:**
1. تأكد من وجود `-.exe` في المجلد الجذر
2. تأكد من وجود مجلد `resources/`
3. تأكد من وجود مجلد `locales/`

### خطأ: "فشل في البناء"
**الحل:**
1. افتح ملف `CFGPLProgram-Setup.iss` في Inno Setup
2. تحقق من رسائل الخطأ
3. تأكد من صحة مسارات الملفات

## تخصيص ملف التثبيت

### تغيير معلومات التطبيق:
قم بتعديل القسم `[Setup]` في ملف `CFGPLProgram-Setup.iss`:
```ini
AppName=اسم التطبيق الجديد
AppVersion=2.0.0
AppPublisher=اسم الشركة
```

### إضافة ملفات جديدة:
قم بتعديل القسم `[Files]`:
```ini
Source: "ملف-جديد.exe"; DestDir: "{app}"; Flags: ignoreversion
```

### تغيير الأيقونة:
```ini
SetupIconFile=مسار-الأيقونة-الجديدة.ico
```

## نصائح مهمة

1. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية من ملف `.iss` قبل التعديل
2. **الاختبار**: اختبر ملف التثبيت على أجهزة مختلفة
3. **التوقيع**: فكر في توقيع ملف التثبيت رقمياً للأمان
4. **التحديثات**: قم بتحديث رقم الإصدار مع كل إصدار جديد

## الدعم

إذا واجهت مشاكل في إنشاء ملف التثبيت:
1. تحقق من سجل الأخطاء في Inno Setup
2. راجع الوثائق الرسمية: https://jrsoftware.org/ishelp/
3. تأكد من صحة جميع المسارات في ملف `.iss`
