@echo off
chcp 65001 >nul
title التثبيت السريع - مؤسسة وقود المستقبل

cls
echo.
echo ╔════════════════════════════════════════════════════════╗
echo ║                                                        ║
echo ║    ⚡ التثبيت السريع - مؤسسة وقود المستقبل           ║
echo ║    Quick Install - Future Fuel Corporation            ║
echo ║                                                        ║
echo ╚════════════════════════════════════════════════════════╝
echo.
echo 🚀 تثبيت سريع وبسيط في 3 خطوات فقط!
echo.

:: المتغيرات
set "INSTALL_DIR=%USERPROFILE%\Desktop\CFGPLProgram"
set "APP_NAME=مؤسسة وقود المستقبل"

:: الخطوة 1: إنشاء المجلد
echo 📁 الخطوة 1/3: إنشاء مجلد التطبيق...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"
echo ✅ تم إنشاء المجلد

:: الخطوة 2: نسخ الملفات
echo.
echo 📋 الخطوة 2/3: نسخ الملفات...
xcopy /E /I /Y "." "%INSTALL_DIR%\" >nul 2>&1
echo ✅ تم نسخ الملفات

:: الخطوة 3: إنشاء الاختصارات
echo.
echo 🔗 الخطوة 3/3: إنشاء الاختصارات...

:: اختصار سطح المكتب
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\%APP_NAME%.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\CFGPLProgram.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()}" 2>nul

:: اختصار قائمة ابدأ
set "START_MENU=%APPDATA%\Microsoft\Windows\Start Menu\Programs"
if not exist "%START_MENU%\%APP_NAME%" mkdir "%START_MENU%\%APP_NAME%"
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\%APP_NAME%\%APP_NAME%.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\CFGPLProgram.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Save()}" 2>nul

echo ✅ تم إنشاء الاختصارات

:: إنهاء التثبيت
echo.
echo ╔════════════════════════════════════════════════════════╗
echo ║                                                        ║
echo ║    🎉 تم التثبيت بنجاح!                              ║
echo ║    Installation Completed!                            ║
echo ║                                                        ║
echo ╚════════════════════════════════════════════════════════╝
echo.
echo 📍 مسار التثبيت: %INSTALL_DIR%
echo 🖥️  اختصار سطح المكتب: ✅
echo 📋 قائمة ابدأ: ✅
echo.
echo هل تريد تشغيل التطبيق الآن؟ (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    echo.
    echo 🚀 تشغيل التطبيق...
    start "" "%INSTALL_DIR%\CFGPLProgram.exe"
)

echo.
echo شكراً لاستخدام مؤسسة وقود المستقبل! 🙏
pause
