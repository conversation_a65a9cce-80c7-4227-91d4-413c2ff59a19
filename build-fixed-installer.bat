@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    بناء ملف التثبيت المُصحح
echo ========================================
echo.

REM البحث عن Inno Setup في مسارات مختلفة
set "INNO_SETUP_PATH="

if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
    echo تم العثور على Inno Setup 6 (x86)
) else if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files\Inno Setup 6\ISCC.exe"
    echo تم العثور على Inno Setup 6 (x64)
) else if exist "C:\Program Files (x86)\Inno Setup 5\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 5\ISCC.exe"
    echo تم العثور على Inno Setup 5 (x86)
) else if exist "C:\Program Files\Inno Setup 5\ISCC.exe" (
    set "INNO_SETUP_PATH=C:\Program Files\Inno Setup 5\ISCC.exe"
    echo تم العثور على Inno Setup 5 (x64)
) else (
    echo خطأ: لم يتم العثور على Inno Setup
    echo يرجى تثبيت Inno Setup من: https://jrsoftware.org/isinfo.php
    pause
    exit /b 1
)

REM إنشاء مجلد installer إذا لم يكن موجوداً
if not exist "installer" (
    echo إنشاء مجلد installer...
    mkdir installer
)

REM التحقق من الملفات المطلوبة
echo التحقق من الملفات المطلوبة...

if not exist "-.exe" (
    echo خطأ: لم يتم العثور على -.exe
    pause
    exit /b 1
)

if not exist "resources" (
    echo خطأ: لم يتم العثور على مجلد resources
    pause
    exit /b 1
)

echo جميع الملفات المطلوبة موجودة ✓

REM بناء ملف التثبيت
echo.
echo بناء ملف التثبيت باستخدام الملف المُصحح...
echo.

"%INNO_SETUP_PATH%" "CFGPLProgram-Fixed.iss"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo تم بناء ملف التثبيت بنجاح!
    echo ========================================
    echo.
    echo ملف التثبيت متوفر في: installer\CFGPLProgram-Setup-v1.0.0.exe
    echo.
    
    REM فتح مجلد installer
    if exist "installer\CFGPLProgram-Setup-v1.0.0.exe" (
        echo فتح مجلد installer...
        explorer installer
    )
) else (
    echo.
    echo ========================================
    echo فشل في بناء ملف التثبيت!
    echo ========================================
    echo.
    echo جاري المحاولة مع الملف البسيط...
    echo.
    
    "%INNO_SETUP_PATH%" "CFGPLProgram-Simple.iss"
    
    if %ERRORLEVEL% EQU 0 (
        echo تم بناء الملف البسيط بنجاح!
        explorer installer
    ) else (
        echo فشل في بناء جميع الملفات
        echo يرجى مراجعة الأخطاء أعلاه
    )
)

echo.
pause
