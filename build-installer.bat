@echo off
echo ========================================
echo    بناء ملف التثبيت لنظام إدارة بطاقات الغاز
echo ========================================
echo.

REM التحقق من وجود Inno Setup
set "INNO_SETUP_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
if not exist "%INNO_SETUP_PATH%" (
    echo خطأ: لم يتم العثور على Inno Setup في المسار المتوقع
    echo يرجى تثبيت Inno Setup 6 أو تحديث المسار في هذا الملف
    echo المسار المتوقع: %INNO_SETUP_PATH%
    pause
    exit /b 1
)

REM إنشاء مجلد installer إذا لم يكن موجوداً
if not exist "installer" (
    echo إنشاء مجلد installer...
    mkdir installer
)

REM التحقق من وجود الملفات المطلوبة
echo التحقق من الملفات المطلوبة...

if not exist "CFGPLProgram.exe" (
    echo خطأ: لم يتم العثور على CFGPLProgram.exe
    pause
    exit /b 1
)

if not exist "resources" (
    echo خطأ: لم يتم العثور على مجلد resources
    pause
    exit /b 1
)

if not exist "CFGPLProgram-Setup.iss" (
    echo خطأ: لم يتم العثور على ملف CFGPLProgram-Setup.iss
    pause
    exit /b 1
)

REM بناء ملف التثبيت
echo.
echo بناء ملف التثبيت...
echo.

"%INNO_SETUP_PATH%" "CFGPLProgram-Setup.iss"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo تم بناء ملف التثبيت بنجاح!
    echo ========================================
    echo.
    echo ملف التثبيت متوفر في: installer\CFGPLProgram-Setup-v1.0.0.exe
    echo.
    
    REM فتح مجلد installer
    if exist "installer\CFGPLProgram-Setup-v1.0.0.exe" (
        echo فتح مجلد installer...
        explorer installer
    )
) else (
    echo.
    echo ========================================
    echo فشل في بناء ملف التثبيت!
    echo ========================================
    echo.
    echo يرجى مراجعة الأخطاء أعلاه وإصلاحها
)

echo.
pause
