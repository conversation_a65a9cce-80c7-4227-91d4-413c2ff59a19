# بناء ملف التثبيت لنظام إدارة بطاقات الغاز
# Build installer for CNG Cards Management System

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    بناء ملف التثبيت لنظام إدارة بطاقات الغاز" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# مسارات Inno Setup المحتملة
$InnoSetupPaths = @(
    "C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
    "C:\Program Files\Inno Setup 6\ISCC.exe",
    "C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
    "C:\Program Files\Inno Setup 5\ISCC.exe"
)

# البحث عن Inno Setup
$InnoSetupPath = $null
foreach ($path in $InnoSetupPaths) {
    if (Test-Path $path) {
        $InnoSetupPath = $path
        break
    }
}

if (-not $InnoSetupPath) {
    Write-Host "خطأ: لم يتم العثور على Inno Setup" -ForegroundColor Red
    Write-Host "يرجى تثبيت Inno Setup من: https://jrsoftware.org/isinfo.php" -ForegroundColor Yellow
    Write-Host "المسارات المفحوصة:" -ForegroundColor Gray
    foreach ($path in $InnoSetupPaths) {
        Write-Host "  - $path" -ForegroundColor Gray
    }
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "تم العثور على Inno Setup في: $InnoSetupPath" -ForegroundColor Green

# إنشاء مجلد installer
if (-not (Test-Path "installer")) {
    Write-Host "إنشاء مجلد installer..." -ForegroundColor Yellow
    New-Item -ItemType Directory -Name "installer" | Out-Null
}

# التحقق من الملفات المطلوبة
Write-Host "التحقق من الملفات المطلوبة..." -ForegroundColor Yellow

$RequiredFiles = @(
    "CFGPLProgram.exe",
    "resources",
    "CFGPLProgram-Setup.iss"
)

$MissingFiles = @()
foreach ($file in $RequiredFiles) {
    if (-not (Test-Path $file)) {
        $MissingFiles += $file
    }
}

if ($MissingFiles.Count -gt 0) {
    Write-Host "خطأ: الملفات التالية مفقودة:" -ForegroundColor Red
    foreach ($file in $MissingFiles) {
        Write-Host "  - $file" -ForegroundColor Red
    }
    Read-Host "اضغط Enter للخروج"
    exit 1
}

Write-Host "جميع الملفات المطلوبة موجودة ✓" -ForegroundColor Green

# بناء ملف التثبيت
Write-Host ""
Write-Host "بناء ملف التثبيت..." -ForegroundColor Yellow
Write-Host ""

try {
    $process = Start-Process -FilePath $InnoSetupPath -ArgumentList "CFGPLProgram-Setup.iss" -Wait -PassThru -NoNewWindow
    
    if ($process.ExitCode -eq 0) {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Green
        Write-Host "تم بناء ملف التثبيت بنجاح!" -ForegroundColor Green
        Write-Host "========================================" -ForegroundColor Green
        Write-Host ""
        
        $installerFile = "installer\CFGPLProgram-Setup-v1.0.0.exe"
        if (Test-Path $installerFile) {
            $fileSize = (Get-Item $installerFile).Length / 1MB
            Write-Host "ملف التثبيت: $installerFile" -ForegroundColor Cyan
            Write-Host "حجم الملف: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Cyan
            Write-Host ""
            
            # فتح مجلد installer
            Write-Host "فتح مجلد installer..." -ForegroundColor Yellow
            Start-Process "explorer" -ArgumentList "installer"
        }
    } else {
        Write-Host ""
        Write-Host "========================================" -ForegroundColor Red
        Write-Host "فشل في بناء ملف التثبيت!" -ForegroundColor Red
        Write-Host "========================================" -ForegroundColor Red
        Write-Host ""
        Write-Host "رمز الخطأ: $($process.ExitCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "خطأ في تشغيل Inno Setup: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "اضغط Enter للخروج"
