@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    إنشاء ملف التثبيت
echo ========================================
echo.

REM تشغيل PowerShell script
powershell -ExecutionPolicy Bypass -File "build-installer.ps1"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo فشل في تشغيل PowerShell script، جاري المحاولة مع Batch script...
    echo.
    call "build-installer.bat"
)

echo.
pause
