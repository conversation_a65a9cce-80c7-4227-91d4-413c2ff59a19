@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    إنشاء حزمة محمولة للتطبيق
echo ========================================
echo.

REM إنشاء مجلد الحزمة
set "PACKAGE_DIR=CFGPLProgram-Portable-v1.0.0"
if exist "%PACKAGE_DIR%" (
    echo حذف الحزمة السابقة...
    rmdir /s /q "%PACKAGE_DIR%"
)

echo إنشاء مجلد الحزمة...
mkdir "%PACKAGE_DIR%"

REM نسخ الملفات الأساسية
echo نسخ الملفات الأساسية...
copy "-.exe" "%PACKAGE_DIR%\CFGPLProgram.exe"
copy "*.dll" "%PACKAGE_DIR%\" 2>nul
copy "*.pak" "%PACKAGE_DIR%\" 2>nul
copy "*.dat" "%PACKAGE_DIR%\" 2>nul
copy "*.bin" "%PACKAGE_DIR%\" 2>nul
copy "*.json" "%PACKAGE_DIR%\" 2>nul
copy "version" "%PACKAGE_DIR%\" 2>nul
copy "LICENSE" "%PACKAGE_DIR%\" 2>nul
copy "LICENSES.chromium.html" "%PACKAGE_DIR%\" 2>nul

REM نسخ المجلدات
echo نسخ مجلد الموارد...
xcopy "resources" "%PACKAGE_DIR%\resources\" /e /i /h /y

echo نسخ مجلد اللغات...
xcopy "locales" "%PACKAGE_DIR%\locales\" /e /i /h /y

REM إنشاء ملفات إضافية
echo إنشاء ملفات التشغيل...

REM ملف تشغيل التطبيق
echo @echo off > "%PACKAGE_DIR%\تشغيل التطبيق.bat"
echo cd /d "%%~dp0" >> "%PACKAGE_DIR%\تشغيل التطبيق.bat"
echo start "" "CFGPLProgram.exe" >> "%PACKAGE_DIR%\تشغيل التطبيق.bat"

REM ملف README
echo # نظام إدارة بطاقات الغاز الطبيعي المضغوط - النسخة المحمولة > "%PACKAGE_DIR%\README.txt"
echo. >> "%PACKAGE_DIR%\README.txt"
echo ## طريقة التشغيل: >> "%PACKAGE_DIR%\README.txt"
echo 1. انقر نقراً مزدوجاً على "تشغيل التطبيق.bat" >> "%PACKAGE_DIR%\README.txt"
echo 2. أو انقر نقراً مزدوجاً على "CFGPLProgram.exe" مباشرة >> "%PACKAGE_DIR%\README.txt"
echo. >> "%PACKAGE_DIR%\README.txt"
echo ## ملاحظات: >> "%PACKAGE_DIR%\README.txt"
echo - هذه نسخة محمولة لا تحتاج تثبيت >> "%PACKAGE_DIR%\README.txt"
echo - يمكن تشغيلها من أي مكان >> "%PACKAGE_DIR%\README.txt"
echo - البيانات تُحفظ في مجلد resources/app/data/ >> "%PACKAGE_DIR%\README.txt"

REM إنشاء اختصار سطح المكتب (PowerShell)
echo إنشاء اختصار سطح المكتب...
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\نظام إدارة بطاقات الغاز.lnk'); $Shortcut.TargetPath = '%CD%\%PACKAGE_DIR%\CFGPLProgram.exe'; $Shortcut.WorkingDirectory = '%CD%\%PACKAGE_DIR%'; $Shortcut.IconLocation = '%CD%\%PACKAGE_DIR%\resources\app\assets\icon.ico'; $Shortcut.Description = 'نظام إدارة بطاقات الغاز الطبيعي المضغوط'; $Shortcut.Save()}" 2>nul

REM ضغط الحزمة (إذا كان PowerShell متاح)
echo ضغط الحزمة...
powershell -Command "& {Add-Type -AssemblyName System.IO.Compression.FileSystem; [System.IO.Compression.ZipFile]::CreateFromDirectory('%PACKAGE_DIR%', '%PACKAGE_DIR%.zip')}" 2>nul

if exist "%PACKAGE_DIR%.zip" (
    echo.
    echo ========================================
    echo تم إنشاء الحزمة بنجاح!
    echo ========================================
    echo.
    echo الحزمة المحمولة: %PACKAGE_DIR%\
    echo الحزمة المضغوطة: %PACKAGE_DIR%.zip
    echo اختصار سطح المكتب: تم إنشاؤه
) else (
    echo.
    echo ========================================
    echo تم إنشاء الحزمة المحمولة!
    echo ========================================
    echo.
    echo الحزمة المحمولة: %PACKAGE_DIR%\
    echo ملاحظة: فشل في ضغط الحزمة، لكن الملفات جاهزة للاستخدام
)

echo.
echo فتح مجلد الحزمة...
explorer "%PACKAGE_DIR%"

echo.
pause
