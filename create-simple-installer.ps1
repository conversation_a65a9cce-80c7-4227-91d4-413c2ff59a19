# إنشاء installer بسيط لنظام إدارة بطاقات الغاز
# Simple installer creator for CNG Cards Management System

param(
    [string]$Version = "1.0.0",
    [string]$OutputDir = "installer"
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    إنشاء installer بسيط للتطبيق" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# إنشاء مجلد الإخراج
if (-not (Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir | Out-Null
}

# إنشاء ملف installer PowerShell
$InstallerScript = @"
# نظام إدارة بطاقات الغاز الطبيعي المضغوط - Installer
# CNG Cards Management System - Installer

param(
    [string]`$InstallPath = "`$env:ProgramFiles\CFGPLProgram",
    [switch]`$CreateDesktopShortcut = `$true,
    [switch]`$CreateStartMenuShortcut = `$true,
    [switch]`$Silent = `$false
)

if (-not `$Silent) {
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "نظام إدارة بطاقات الغاز الطبيعي المضغوط" -ForegroundColor Yellow
    Write-Host "CNG Cards Management System" -ForegroundColor Yellow
    Write-Host "الإصدار: $Version" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
}

# التحقق من صلاحيات المدير
if (-not ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "خطأ: يتطلب هذا التثبيت صلاحيات المدير" -ForegroundColor Red
    Write-Host "يرجى تشغيل PowerShell كمدير وإعادة المحاولة" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit 1
}

# إنشاء مجلد التثبيت
if (-not (Test-Path `$InstallPath)) {
    Write-Host "إنشاء مجلد التثبيت: `$InstallPath" -ForegroundColor Yellow
    New-Item -ItemType Directory -Path `$InstallPath -Force | Out-Null
}

# نسخ الملفات
Write-Host "نسخ ملفات التطبيق..." -ForegroundColor Yellow

# الملف التنفيذي الرئيسي
Copy-Item "-.exe" "`$InstallPath\CFGPLProgram.exe" -Force

# الملفات الأساسية
`$BasicFiles = @("*.dll", "*.pak", "*.dat", "*.bin", "*.json", "version", "LICENSE", "LICENSES.chromium.html")
foreach (`$pattern in `$BasicFiles) {
    Get-ChildItem `$pattern -ErrorAction SilentlyContinue | ForEach-Object {
        Copy-Item `$_.FullName "`$InstallPath\" -Force
    }
}

# المجلدات
if (Test-Path "resources") {
    Write-Host "نسخ مجلد الموارد..." -ForegroundColor Yellow
    Copy-Item "resources" "`$InstallPath\" -Recurse -Force
}

if (Test-Path "locales") {
    Write-Host "نسخ مجلد اللغات..." -ForegroundColor Yellow
    Copy-Item "locales" "`$InstallPath\" -Recurse -Force
}

# إنشاء اختصار سطح المكتب
if (`$CreateDesktopShortcut) {
    Write-Host "إنشاء اختصار سطح المكتب..." -ForegroundColor Yellow
    `$WshShell = New-Object -comObject WScript.Shell
    `$Shortcut = `$WshShell.CreateShortcut("`$env:USERPROFILE\Desktop\نظام إدارة بطاقات الغاز.lnk")
    `$Shortcut.TargetPath = "`$InstallPath\CFGPLProgram.exe"
    `$Shortcut.WorkingDirectory = `$InstallPath
    `$Shortcut.IconLocation = "`$InstallPath\resources\app\assets\icon.ico"
    `$Shortcut.Description = "نظام إدارة بطاقات الغاز الطبيعي المضغوط"
    `$Shortcut.Save()
}

# إنشاء اختصار قائمة ابدأ
if (`$CreateStartMenuShortcut) {
    Write-Host "إنشاء اختصار قائمة ابدأ..." -ForegroundColor Yellow
    `$StartMenuPath = "`$env:ProgramData\Microsoft\Windows\Start Menu\Programs"
    `$WshShell = New-Object -comObject WScript.Shell
    `$Shortcut = `$WshShell.CreateShortcut("`$StartMenuPath\نظام إدارة بطاقات الغاز.lnk")
    `$Shortcut.TargetPath = "`$InstallPath\CFGPLProgram.exe"
    `$Shortcut.WorkingDirectory = `$InstallPath
    `$Shortcut.IconLocation = "`$InstallPath\resources\app\assets\icon.ico"
    `$Shortcut.Description = "نظام إدارة بطاقات الغاز الطبيعي المضغوط"
    `$Shortcut.Save()
}

# تسجيل في Registry
Write-Host "تسجيل التطبيق في النظام..." -ForegroundColor Yellow
`$RegPath = "HKLM:\SOFTWARE\CFGPLProgram"
if (-not (Test-Path `$RegPath)) {
    New-Item -Path `$RegPath -Force | Out-Null
}
Set-ItemProperty -Path `$RegPath -Name "InstallPath" -Value `$InstallPath
Set-ItemProperty -Path `$RegPath -Name "Version" -Value "$Version"
Set-ItemProperty -Path `$RegPath -Name "InstallDate" -Value (Get-Date -Format "yyyy-MM-dd")

# إنشاء uninstaller
Write-Host "إنشاء أداة إلغاء التثبيت..." -ForegroundColor Yellow
`$UninstallerScript = @'
# أداة إلغاء تثبيت نظام إدارة بطاقات الغاز

Write-Host "إلغاء تثبيت نظام إدارة بطاقات الغاز..." -ForegroundColor Yellow

# حذف الاختصارات
Remove-Item "`$env:USERPROFILE\Desktop\نظام إدارة بطاقات الغاز.lnk" -ErrorAction SilentlyContinue
Remove-Item "`$env:ProgramData\Microsoft\Windows\Start Menu\Programs\نظام إدارة بطاقات الغاز.lnk" -ErrorAction SilentlyContinue

# حذف مفاتيح Registry
Remove-Item "HKLM:\SOFTWARE\CFGPLProgram" -Recurse -ErrorAction SilentlyContinue

# حذف ملفات التطبيق
`$InstallPath = "`$PSScriptRoot"
Write-Host "حذف ملفات التطبيق من: `$InstallPath" -ForegroundColor Yellow
Start-Sleep -Seconds 2
Remove-Item "`$InstallPath" -Recurse -Force -ErrorAction SilentlyContinue

Write-Host "تم إلغاء التثبيت بنجاح" -ForegroundColor Green
Read-Host "اضغط Enter للخروج"
'@

`$UninstallerScript | Out-File "`$InstallPath\Uninstall.ps1" -Encoding UTF8

if (-not `$Silent) {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Green
    Write-Host "تم التثبيت بنجاح!" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "مسار التثبيت: `$InstallPath" -ForegroundColor Cyan
    Write-Host "لتشغيل التطبيق: انقر على اختصار سطح المكتب" -ForegroundColor Cyan
    Write-Host "لإلغاء التثبيت: تشغيل Uninstall.ps1 من مجلد التثبيت" -ForegroundColor Cyan
    Write-Host ""
    
    `$RunNow = Read-Host "هل تريد تشغيل التطبيق الآن؟ (y/n)"
    if (`$RunNow -eq "y" -or `$RunNow -eq "Y") {
        Start-Process "`$InstallPath\CFGPLProgram.exe"
    }
}
"@

# حفظ ملف الـ installer
$InstallerPath = "$OutputDir\CFGPLProgram-Installer.ps1"
$InstallerScript | Out-File $InstallerPath -Encoding UTF8

# إنشاء ملف batch لتشغيل الـ installer
$BatchInstaller = @"
@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    تثبيت نظام إدارة بطاقات الغاز
echo ========================================
echo.

REM التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% == 0 (
    echo تم التحقق من صلاحيات المدير ✓
) else (
    echo خطأ: يتطلب هذا التثبيت صلاحيات المدير
    echo يرجى تشغيل هذا الملف كمدير
    pause
    exit /b 1
)

echo.
echo بدء عملية التثبيت...
echo.

powershell -ExecutionPolicy Bypass -File "CFGPLProgram-Installer.ps1"

echo.
pause
"@

$BatchInstaller | Out-File "$OutputDir\تثبيت التطبيق.bat" -Encoding UTF8

# إنشاء ملف README للـ installer
$ReadmeContent = @"
# نظام إدارة بطاقات الغاز الطبيعي المضغوط - Installer

## طريقة التثبيت:

### الطريقة الأولى (مستحسنة):
1. انقر نقراً مزدوجاً على "تثبيت التطبيق.bat"
2. اتبع التعليمات على الشاشة

### الطريقة الثانية:
1. انقر بزر الماوس الأيمن على "CFGPLProgram-Installer.ps1"
2. اختر "Run with PowerShell"
3. اتبع التعليمات على الشاشة

## ملاحظات مهمة:
- يتطلب التثبيت صلاحيات المدير
- سيتم إنشاء اختصارات على سطح المكتب وقائمة ابدأ
- مسار التثبيت الافتراضي: C:\Program Files\CFGPLProgram

## إلغاء التثبيت:
- تشغيل ملف Uninstall.ps1 من مجلد التثبيت
- أو استخدام "إضافة أو إزالة البرامج" في Windows

## الدعم الفني:
- البريد الإلكتروني: <EMAIL>
- الموقع: https://cfgpl.com
"@

$ReadmeContent | Out-File "$OutputDir\README.txt" -Encoding UTF8

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "تم إنشاء installer بنجاح!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "الملفات المُنشأة:" -ForegroundColor Cyan
Write-Host "  - $InstallerPath" -ForegroundColor White
Write-Host "  - $OutputDir\تثبيت التطبيق.bat" -ForegroundColor White
Write-Host "  - $OutputDir\README.txt" -ForegroundColor White
Write-Host ""
Write-Host "لتوزيع التطبيق:" -ForegroundColor Yellow
Write-Host "1. انسخ جميع ملفات التطبيق إلى مجلد واحد" -ForegroundColor White
Write-Host "2. انسخ ملفات installer إلى نفس المجلد" -ForegroundColor White
Write-Host "3. وزع المجلد كاملاً" -ForegroundColor White
Write-Host ""

# فتح مجلد الإخراج
Start-Process "explorer" -ArgumentList $OutputDir
