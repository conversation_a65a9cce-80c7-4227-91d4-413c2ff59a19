@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    تجهيز التطبيق للتوزيع
echo ========================================
echo.

echo اختر طريقة التجهيز:
echo.
echo 1. إنشاء حزمة محمولة (Portable)
echo 2. إنشاء installer بسيط (PowerShell)
echo 3. إنشاء installer متقدم (Inno Setup)
echo 4. إنشاء جميع الأنواع
echo.

set /p choice="اختر رقم (1-4): "

if "%choice%"=="1" goto portable
if "%choice%"=="2" goto simple_installer
if "%choice%"=="3" goto inno_installer
if "%choice%"=="4" goto all_types

echo خيار غير صحيح!
pause
exit /b 1

:portable
echo.
echo ========================================
echo    إنشاء حزمة محمولة
echo ========================================
echo.
call create-portable-package.bat
goto end

:simple_installer
echo.
echo ========================================
echo    إنشاء installer بسيط
echo ========================================
echo.
powershell -ExecutionPolicy Bypass -File "create-simple-installer.ps1"
goto end

:inno_installer
echo.
echo ========================================
echo    إنشاء installer متقدم
echo ========================================
echo.
powershell -ExecutionPolicy Bypass -File "build-installer.ps1"
if %ERRORLEVEL% NEQ 0 (
    echo فشل في إنشاء installer متقدم، جاري المحاولة مع Batch...
    call build-installer.bat
)
goto end

:all_types
echo.
echo ========================================
echo    إنشاء جميع أنواع التوزيع
echo ========================================
echo.

echo 1/3 - إنشاء حزمة محمولة...
call create-portable-package.bat

echo.
echo 2/3 - إنشاء installer بسيط...
powershell -ExecutionPolicy Bypass -File "create-simple-installer.ps1"

echo.
echo 3/3 - إنشاء installer متقدم...
powershell -ExecutionPolicy Bypass -File "build-installer.ps1"
if %ERRORLEVEL% NEQ 0 (
    echo فشل في إنشاء installer متقدم، جاري المحاولة مع Batch...
    call build-installer.bat
)

echo.
echo ========================================
echo    تم إنشاء جميع أنواع التوزيع!
echo ========================================
echo.

:end
echo.
echo تم الانتهاء من تجهيز التطبيق للتوزيع
echo.
pause
