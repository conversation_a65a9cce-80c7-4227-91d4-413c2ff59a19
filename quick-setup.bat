@echo off
echo.
echo ========================================
echo    تثبيت سريع لنظام إدارة بطاقات الغاز
echo ========================================
echo.

REM إنشاء مجلد على سطح المكتب
set "DESKTOP_FOLDER=%USERPROFILE%\Desktop\نظام إدارة بطاقات الغاز"
echo إنشاء مجلد التطبيق على سطح المكتب...
if not exist "%DESKTOP_FOLDER%" mkdir "%DESKTOP_FOLDER%"

REM نسخ الملفات الأساسية
echo نسخ ملفات التطبيق...
copy "-.exe" "%DESKTOP_FOLDER%\CFGPLProgram.exe" >nul
copy "*.dll" "%DESKTOP_FOLDER%\" >nul 2>&1
copy "*.pak" "%DESKTOP_FOLDER%\" >nul 2>&1
copy "*.dat" "%DESKTOP_FOLDER%\" >nul 2>&1
copy "*.bin" "%DESKTOP_FOLDER%\" >nul 2>&1
copy "*.json" "%DESKTOP_FOLDER%\" >nul 2>&1
copy "version" "%DESKTOP_FOLDER%\" >nul 2>&1

REM نسخ المجلدات
echo نسخ مجلد الموارد...
xcopy "resources" "%DESKTOP_FOLDER%\resources\" /e /i /h /y /q >nul
echo نسخ مجلد اللغات...
xcopy "locales" "%DESKTOP_FOLDER%\locales\" /e /i /h /y /q >nul

REM إنشاء ملف تشغيل
echo @echo off > "%DESKTOP_FOLDER%\تشغيل.bat"
echo cd /d "%%~dp0" >> "%DESKTOP_FOLDER%\تشغيل.bat"
echo start "" "CFGPLProgram.exe" >> "%DESKTOP_FOLDER%\تشغيل.bat"

REM إنشاء ملف معلومات
echo نظام إدارة بطاقات الغاز الطبيعي المضغوط > "%DESKTOP_FOLDER%\معلومات.txt"
echo الإصدار: 1.0.0 >> "%DESKTOP_FOLDER%\معلومات.txt"
echo. >> "%DESKTOP_FOLDER%\معلومات.txt"
echo طريقة التشغيل: >> "%DESKTOP_FOLDER%\معلومات.txt"
echo 1. انقر نقراً مزدوجاً على "تشغيل.bat" >> "%DESKTOP_FOLDER%\معلومات.txt"
echo 2. أو انقر نقراً مزدوجاً على "CFGPLProgram.exe" >> "%DESKTOP_FOLDER%\معلومات.txt"

echo.
echo ========================================
echo تم التثبيت بنجاح!
echo ========================================
echo.
echo مجلد التطبيق: %DESKTOP_FOLDER%
echo.
echo لتشغيل التطبيق:
echo 1. اذهب إلى سطح المكتب
echo 2. افتح مجلد "نظام إدارة بطاقات الغاز"
echo 3. انقر نقراً مزدوجاً على "تشغيل.bat"
echo.

set /p open_folder="هل تريد فتح مجلد التطبيق الآن؟ (y/n): "
if /i "%open_folder%"=="y" (
    explorer "%DESKTOP_FOLDER%"
)

echo.
pause
