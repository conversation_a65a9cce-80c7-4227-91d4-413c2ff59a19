# نظام إدارة مؤسسة وقود المستقبل
## Future Fuel Corporation Management System

![Version](https://img.shields.io/badge/version-2.2.0-blue.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)
![Platform](https://img.shields.io/badge/platform-Windows-lightgrey.svg)

### 📋 نظرة عامة

نظام إدارة شامل ومتطور لمحطات الغاز يوفر حلولاً متكاملة لإدارة:
- 🆔 بطاقات الغاز والشهادات
- 📅 المواعيد والجدولة
- 👥 قاعدة بيانات الزبائن
- 🚛 إدارة الموردين
- 📦 المخزون والمبيعات
- 💰 نظام الديون والمدفوعات
- 📊 التقارير والإحصائيات
- 📋 جدول الإرسال للعمليات

### ✨ الميزات الجديدة في الإصدار 2.2.0

- 🔄 نسخ احتياطية تلقائية محسنة
- 📱 تكامل مع تيليجرام للنسخ الاحتياطية
- ⌨️ اختصارات لوحة المفاتيح
- 🌙 الوضع المظلم
- 📈 إحصائيات متقدمة مع الرسوم البيانية
- 🔔 نظام إشعارات محسن
- 🖨️ تحسينات في الطباعة والتصدير
- 🔐 نظام ترخيص محسن مع حقل كود الترخيص
- 🖥️ دعم محسن لـ Windows 8/8.1

### 🚀 التثبيت

#### التثبيت السريع
1. قم بتشغيل `install.bat` كمدير
2. اتبع التعليمات على الشاشة
3. سيتم إنشاء اختصار على سطح المكتب وقائمة ابدأ

#### التثبيت على Windows 8
1. شغل `setup-windows8.bat` كمدير
2. اتبع معالج الإعداد
3. استخدم `تشغيل-متوافق.bat` لتشغيل التطبيق

#### التثبيت اليدوي
```bash
# تثبيت المتطلبات
npm install

# تشغيل التطبيق
npm start

# أو تشغيل كتطبيق ويب
npm run web
```

### 📁 هيكل المشروع

```
Future Fuel Management/
├── assets/                 # الموارد والأيقونات
│   ├── icons/              # أيقونات التطبيق
│   └── images/             # الصور
├── scripts/                # ملفات JavaScript
│   ├── script.js           # الملف الرئيسي
│   ├── notifications.js    # نظام الإشعارات
│   ├── reports.js          # التقارير
│   ├── security.js         # الأمان
│   └── updater.js          # التحديثات
├── styles/                 # ملفات CSS
│   └── styles.css          # الأنماط الرئيسية
├── index.html              # الصفحة الرئيسية
├── main.js                 # ملف Electron الرئيسي
├── preload.js              # ملف Electron preload
├── server.js               # خادم التطوير
├── package.json            # إعدادات المشروع
└── uninstall.bat           # ملف إلغاء التثبيت
```

### 🔧 المتطلبات

- **نظام التشغيل:** Windows 8/8.1/10/11
- **المعالج:** Intel/AMD x64 أو x86
- **الذاكرة:** 4 GB RAM (الحد الأدنى)
- **التخزين:** 500 MB مساحة فارغة
- **الشبكة:** اتصال إنترنت (اختياري للنسخ الاحتياطية)
- **متطلبات إضافية لـ Windows 8:** .NET Framework 4.5 أو أحدث

### 📖 دليل الاستخدام

#### البدء السريع
1. **تسجيل الدخول:** أدخل بيانات المستخدم وكود الترخيص (راجع ملف أكواد-الترخيص-التجريبية.txt)
2. **إضافة زبون جديد:** انتقل إلى قسم الزبائن → إضافة زبون جديد → اختر نوع العملية
3. **جدول الإرسال:** قسم جدول الإرسال → عرض العمليات المسجلة تلقائياً
4. **إنشاء بطاقة غاز:** قسم بطاقات الغاز → إضافة بطاقة جديدة
5. **جدولة موعد:** قسم المواعيد → إضافة موعد جديد
6. **إدارة المخزون:** قسم المخزون → إضافة صنف جديد

#### الاختصارات المفيدة
- `Ctrl + S` - حفظ البيانات
- `Ctrl + P` - طباعة
- `F5` - إعادة تحميل
- `Ctrl + Q` - خروج

### 🔒 الأمان والنسخ الاحتياطية

#### النسخ الاحتياطية التلقائية
- يتم إنشاء نسخة احتياطية كل 5 عمليات حفظ
- نسخة احتياطية كل ساعة تلقائياً
- الاحتفاظ بآخر 10 نسخ احتياطية

#### تكامل تيليجرام
1. إنشاء بوت جديد عبر @BotFather
2. الحصول على Bot Token
3. إضافة البوت إلى مجموعة والحصول على Chat ID
4. تفعيل الميزة من الإعدادات

### 🛠️ استكشاف الأخطاء وإصلاحها

#### مشاكل شائعة

**التطبيق لا يبدأ:**
- تأكد من تثبيت جميع المتطلبات
- تشغيل كمدير
- فحص ملفات النظام

**مشاكل Windows 8:**
- شغل `fix-windows8-compatibility.bat` كمدير
- استخدم `تشغيل-متوافق.bat` للتشغيل
- تأكد من تثبيت .NET Framework 4.8
- اقرأ `تعليمات-Windows8.txt`

**فقدان البيانات:**
- فحص مجلد النسخ الاحتياطية
- استعادة من localStorage
- استخدام ملف data.json

**مشاكل الطباعة:**
- تحديث متصفح النظام
- فحص إعدادات الطابعة
- استخدام تصدير PDF كبديل

### 📞 الدعم الفني

- **البريد الإلكتروني:** <EMAIL>
- **الموقع:** https://github.com/future-fuel/gas-shop-management
- **الدعم:** متوفر من 8:00 ص إلى 6:00 م

### 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

### 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة [دليل المساهمة](CONTRIBUTING.md) قبل البدء.

### 📝 سجل التغييرات

#### الإصدار 2.2.0 (2024-12-14)
- ✅ إضافة نظام النسخ الاحتياطية المحسن
- ✅ تكامل تيليجرام
- ✅ الوضع المظلم
- ✅ اختصارات لوحة المفاتيح
- ✅ جدول الإرسال المتكامل
- ✅ تحسينات الأداء
- ✅ نظام ترخيص محسن مع حقل كود الترخيص
- ✅ دعم شامل لـ Windows 8/8.1
- ✅ أدوات اختبار وتطوير متقدمة
- ✅ واجهة تسجيل دخول محسنة

#### الإصدار 2.1.0
- إضافة نظام الديون
- تحسين واجهة المستخدم
- إضافة التقارير المتقدمة

---

**© 2024 مؤسسة وقود المستقبل. جميع الحقوق محفوظة.**
