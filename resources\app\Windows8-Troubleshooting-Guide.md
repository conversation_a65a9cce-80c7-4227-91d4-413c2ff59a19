# دليل استكشاف الأخطاء - Windows 8
## مؤسسة وقود المستقبل

### 🔧 المشاكل الشائعة وحلولها

#### 1. التطبيق لا يبدأ على Windows 8

**الأعراض:**
- النقر على التطبيق لا يحدث شيء
- ظهور رسالة خطأ عند التشغيل
- التطبيق يتوقف فور البدء

**الحلول:**

##### أ) تثبيت .NET Framework 4.8
```bash
1. اذهب إلى: https://dotnet.microsoft.com/download/dotnet-framework/net48
2. حمل النسخة المناسبة لنظامك (x64 أو x86)
3. شغل المثبت كمدير
4. أعد تشغيل النظام
```

##### ب) تثبيت Visual C++ Redistributables
```bash
1. حمل من: https://aka.ms/vs/16/release/vc_redist.x64.exe
2. حمل من: https://aka.ms/vs/16/release/vc_redist.x86.exe
3. ثبت كلا النسختين
4. أعد تشغيل النظام
```

##### ج) استخدام وضع التوافق
```bash
1. انقر بالزر الأيمن على ملف التطبيق
2. اختر "Properties" أو "خصائص"
3. اذهب إلى تبويب "Compatibility" أو "التوافق"
4. فعل "Run this program in compatibility mode"
5. اختر "Windows 8" أو "Windows 10"
6. فعل "Run as administrator"
7. اضغط OK وجرب التشغيل
```

#### 2. مشاكل الأداء

**الأعراض:**
- التطبيق بطيء
- تجمد أو توقف مؤقت
- استهلاك عالي للذاكرة

**الحلول:**

##### أ) تحسين الأداء
```bash
1. أغلق البرامج غير الضرورية
2. تأكد من توفر 4GB RAM على الأقل
3. تأكد من توفر 1GB مساحة فارغة على القرص الصلب
4. شغل التطبيق كمدير
```

##### ب) تنظيف النظام
```bash
1. شغل Disk Cleanup
2. امسح الملفات المؤقتة
3. أعد تشغيل النظام
4. تأكد من تحديث Windows 8 إلى آخر إصدار
```

#### 3. مشاكل الطباعة

**الأعراض:**
- لا يمكن طباعة التقارير
- تظهر صفحة فارغة عند الطباعة
- خطأ في معاينة الطباعة

**الحلول:**

##### أ) تحديث متصفح النظام
```bash
1. حدث Internet Explorer إلى آخر إصدار
2. أو ثبت Chrome/Firefox حديث
3. تأكد من تفعيل JavaScript
```

##### ب) إعدادات الطابعة
```bash
1. تأكد من تثبيت تعريف الطابعة
2. اختبر الطباعة من برنامج آخر
3. استخدم "تصدير PDF" كبديل
```

#### 4. مشاكل حفظ البيانات

**الأعراض:**
- البيانات لا تُحفظ
- فقدان البيانات عند إعادة التشغيل
- رسائل خطأ عند الحفظ

**الحلول:**

##### أ) صلاحيات الملفات
```bash
1. شغل التطبيق كمدير
2. تأكد من صلاحيات الكتابة في مجلد التطبيق
3. أضف مجلد التطبيق إلى استثناءات الحماية
```

##### ب) مسار البيانات
```bash
1. تأكد من وجود مجلد "data" في مجلد التطبيق
2. تحقق من وجود ملف "data.json"
3. انسخ نسخة احتياطية من البيانات
```

### 🛠️ أدوات الإصلاح

#### 1. فحص التوافق
```bash
شغل ملف: check-compatibility.bat
```

#### 2. إصلاح المشاكل
```bash
شغل ملف: fix-windows8-compatibility.bat
```

#### 3. التشغيل المتوافق
```bash
شغل ملف: run-compatible.bat
```

### 📋 قائمة التحقق السريع

- [ ] Windows 8/8.1 محدث إلى آخر إصدار
- [ ] .NET Framework 4.8 مثبت
- [ ] Visual C++ Redistributables مثبت
- [ ] 4GB RAM متوفرة
- [ ] 500MB مساحة فارغة
- [ ] تشغيل كمدير
- [ ] إضافة إلى استثناءات الحماية
- [ ] تعطيل UAC مؤقتاً (اختياري)

### 🔍 معلومات إضافية

#### متطلبات Windows 8 الخاصة:
- تحديث KB2919355 (April 2014 Update)
- Internet Explorer 11
- Windows PowerShell 3.0+

#### ملفات مهمة:
- `data.json` - ملف البيانات الرئيسي
- `backup/` - مجلد النسخ الاحتياطية
- `logs/` - ملفات السجلات

### 📞 الحصول على المساعدة

إذا استمرت المشاكل:

1. **جمع معلومات النظام:**
   ```bash
   شغل: msinfo32
   احفظ التقرير وأرسله
   ```

2. **جمع سجلات الأخطاء:**
   ```bash
   اذهب إلى: Event Viewer > Windows Logs > Application
   ابحث عن أخطاء التطبيق
   ```

3. **التواصل مع الدعم:**
   - البريد الإلكتروني: <EMAIL>
   - أرفق معلومات النظام وسجلات الأخطاء

### 🚀 نصائح للأداء الأمثل

1. **تشغيل منتظم للصيانة:**
   ```bash
   - تنظيف الملفات المؤقتة أسبوعياً
   - إعادة تشغيل النظام يومياً
   - فحص التحديثات شهرياً
   ```

2. **مراقبة الموارد:**
   ```bash
   - استخدام Task Manager لمراقبة الأداء
   - إغلاق البرامج غير الضرورية
   - تجنب تشغيل برامج ثقيلة مع التطبيق
   ```

3. **النسخ الاحتياطية:**
   ```bash
   - نسخ مجلد البيانات أسبوعياً
   - استخدام النسخ الاحتياطية التلقائية
   - حفظ نسخة على قرص خارجي
   ```
