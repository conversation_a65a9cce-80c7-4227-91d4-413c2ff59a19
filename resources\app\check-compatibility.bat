@echo off
chcp 65001 >nul
title فحص توافق النظام - مؤسسة وقود المستقبل

echo.
echo ========================================
echo    فحص توافق النظام
echo    مؤسسة وقود المستقبل
echo ========================================
echo.

echo جاري فحص نظام التشغيل...
echo.

:: فحص إصدار Windows
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
for /f "tokens=2 delims=[]" %%i in ('ver') do set FULL_VERSION=%%i

echo إصدار النظام: %FULL_VERSION%
echo.

:: فحص معمارية النظام
if "%PROCESSOR_ARCHITECTURE%"=="AMD64" (
    set ARCH=x64
    echo معمارية النظام: 64-bit ✓
) else if "%PROCESSOR_ARCHITECTURE%"=="x86" (
    if defined PROCESSOR_ARCHITEW6432 (
        set ARCH=x64
        echo معمارية النظام: 64-bit ✓
    ) else (
        set ARCH=x86
        echo معمارية النظام: 32-bit ✓
    )
) else (
    set ARCH=unknown
    echo معمارية النظام: غير معروف ⚠️
)

echo.

:: فحص إصدار Windows
echo جاري فحص توافق الإصدار...
wmic os get Caption,Version /format:list | findstr /i "Caption\|Version" > temp_os.txt

for /f "tokens=2 delims==" %%i in ('findstr /i "Caption" temp_os.txt') do set OS_NAME=%%i
for /f "tokens=2 delims==" %%i in ('findstr /i "Version" temp_os.txt') do set OS_VERSION=%%i

del temp_os.txt

echo اسم النظام: %OS_NAME%
echo إصدار النظام: %OS_VERSION%
echo.

:: التحقق من التوافق
set COMPATIBLE=0

echo %OS_NAME% | findstr /i "Windows 11" >nul && set COMPATIBLE=1
echo %OS_NAME% | findstr /i "Windows 10" >nul && set COMPATIBLE=1
echo %OS_NAME% | findstr /i "Windows 8.1" >nul && set COMPATIBLE=1
echo %OS_NAME% | findstr /i "Windows 8" >nul && set COMPATIBLE=1

if %COMPATIBLE%==1 (
    echo ✅ نظام التشغيل متوافق!
    echo.
    
    :: فحص .NET Framework للأنظمة القديمة
    echo %OS_NAME% | findstr /i "Windows 8" >nul && (
        echo جاري فحص .NET Framework...
        reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>&1
        if !errorlevel! equ 0 (
            echo ✅ .NET Framework 4.5+ متوفر
        ) else (
            echo ⚠️  .NET Framework 4.5+ غير متوفر
            echo يرجى تحميل وتثبيت .NET Framework 4.5 أو أحدث
            echo رابط التحميل: https://dotnet.microsoft.com/download/dotnet-framework
        )
        echo.
    )
    
    echo التوصية:
    if "%ARCH%"=="x64" (
        echo استخدم النسخة: مؤسسة وقود المستقبل-2.2.0-x64.exe
    ) else (
        echo استخدم النسخة: مؤسسة وقود المستقبل-2.2.0-ia32.exe
    )
    
) else (
    echo ❌ نظام التشغيل غير متوافق!
    echo.
    echo الأنظمة المدعومة:
    echo - Windows 8/8.1
    echo - Windows 10
    echo - Windows 11
    echo.
    echo نظامك الحالي: %OS_NAME%
)

echo.
echo فحص الذاكرة...
for /f "skip=1 tokens=2 delims=," %%i in ('wmic computersystem get TotalPhysicalMemory /format:csv') do set TOTAL_RAM=%%i

:: تحويل البايت إلى جيجابايت
set /a RAM_GB=%TOTAL_RAM:~0,-9%

if %RAM_GB% geq 4 (
    echo ✅ الذاكرة كافية: %RAM_GB% GB
) else (
    echo ⚠️  الذاكرة قد تكون غير كافية: %RAM_GB% GB
    echo الحد الأدنى المطلوب: 4 GB
)

echo.
echo فحص المساحة المتاحة...
for /f "tokens=3" %%i in ('dir C:\ /-c ^| findstr /i "bytes free"') do set FREE_SPACE=%%i

:: تحويل البايت إلى ميجابايت
set /a FREE_MB=%FREE_SPACE:~0,-6%

if %FREE_MB% geq 500 (
    echo ✅ المساحة كافية: %FREE_MB% MB متاحة
) else (
    echo ⚠️  المساحة قد تكون غير كافية: %FREE_MB% MB متاحة
    echo الحد الأدنى المطلوب: 500 MB
)

echo.
echo ========================================
echo انتهى فحص التوافق
echo ========================================
echo.

if %COMPATIBLE%==1 (
    echo يمكنك الآن تشغيل التطبيق بأمان! 🚀
) else (
    echo يرجى ترقية نظام التشغيل أو استخدام جهاز متوافق
)

echo.
pause
