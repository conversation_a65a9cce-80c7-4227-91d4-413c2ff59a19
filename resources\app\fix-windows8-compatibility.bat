@echo off
chcp 65001 >nul
title إصلاح توافق Windows 8 - مؤسسة وقود المستقبل

echo.
echo ========================================
echo    إصلاح توافق Windows 8
echo    مؤسسة وقود المستقبل
echo ========================================
echo.

echo هذا الملف سيقوم بإصلاح مشاكل التوافق مع Windows 8
echo.

:: التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ⚠️  يجب تشغيل هذا الملف كمدير!
    echo انقر بالزر الأيمن على الملف واختر "تشغيل كمدير"
    echo.
    pause
    exit /b 1
)

echo ✅ تم التحقق من صلاحيات المدير
echo.

echo جاري فحص النظام...
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j

echo إصدار Windows: %VERSION%
echo.

:: فحص وتثبيت .NET Framework
echo 1. فحص .NET Framework...
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" /v Release >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ .NET Framework 4.5+ متوفر
) else (
    echo ❌ .NET Framework 4.5+ غير متوفر
    echo.
    echo جاري تحميل .NET Framework 4.8...
    
    :: إنشاء مجلد مؤقت
    if not exist "%TEMP%\FutureFuelFix" mkdir "%TEMP%\FutureFuelFix"
    
    echo يرجى تحميل .NET Framework 4.8 من الرابط التالي:
    echo https://dotnet.microsoft.com/download/dotnet-framework/net48
    echo.
    echo أو استخدم الأمر التالي في PowerShell كمدير:
    echo Invoke-WebRequest -Uri "https://download.microsoft.com/download/7/D/E/7DE2B016-8ED8-4B8E-9F26-7E5C5E6E8E5C/NDP48-x86-x64-AllOS-ENU.exe" -OutFile "%TEMP%\FutureFuelFix\dotnet48.exe"
    echo.
    
    choice /c YN /m "هل تريد فتح صفحة التحميل الآن؟ (Y/N)"
    if !errorlevel! equ 1 (
        start https://dotnet.microsoft.com/download/dotnet-framework/net48
    )
)

echo.
echo 2. فحص Visual C++ Redistributables...

:: فحص Visual C++ 2015-2019
reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Visual C++ 2015-2019 x64 متوفر
) else (
    echo ❌ Visual C++ 2015-2019 x64 غير متوفر
    echo يرجى تحميله من: https://aka.ms/vs/16/release/vc_redist.x64.exe
)

reg query "HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x86" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Visual C++ 2015-2019 x86 متوفر
) else (
    echo ❌ Visual C++ 2015-2019 x86 غير متوفر
    echo يرجى تحميله من: https://aka.ms/vs/16/release/vc_redist.x86.exe
)

echo.
echo 3. فحص Windows Updates...
echo يُنصح بتحديث Windows 8 إلى آخر إصدار
echo اذهب إلى: الإعدادات > Windows Update > فحص التحديثات

echo.
echo 4. إعداد متغيرات البيئة...

:: إضافة مسار التطبيق إلى PATH إذا لم يكن موجوداً
set APP_PATH=%~dp0
echo %PATH% | findstr /i "%APP_PATH%" >nul
if %errorlevel% neq 0 (
    echo إضافة مسار التطبيق إلى متغيرات البيئة...
    setx PATH "%PATH%;%APP_PATH%" /M >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ تم إضافة المسار بنجاح
    ) else (
        echo ⚠️  فشل في إضافة المسار - قد تحتاج لإعادة تشغيل النظام
    )
)

echo.
echo 5. إنشاء ملف توافق...

:: إنشاء ملف shim للتوافق
echo @echo off > "%~dp0run-compatible.bat"
echo title مؤسسة وقود المستقبل - Windows 8 Compatible >> "%~dp0run-compatible.bat"
echo. >> "%~dp0run-compatible.bat"
echo :: تعيين متغيرات التوافق >> "%~dp0run-compatible.bat"
echo set ELECTRON_ENABLE_LOGGING=1 >> "%~dp0run-compatible.bat"
echo set ELECTRON_DISABLE_SECURITY_WARNINGS=1 >> "%~dp0run-compatible.bat"
echo set ELECTRON_FORCE_IS_PACKAGED=1 >> "%~dp0run-compatible.bat"
echo. >> "%~dp0run-compatible.bat"
echo :: تشغيل التطبيق >> "%~dp0run-compatible.bat"
echo if exist "%~dp0مؤسسة وقود المستقبل.exe" ( >> "%~dp0run-compatible.bat"
echo     "%~dp0مؤسسة وقود المستقبل.exe" %%* >> "%~dp0run-compatible.bat"
echo ^) else if exist "%~dp0-.exe" ( >> "%~dp0run-compatible.bat"
echo     "%~dp0-.exe" %%* >> "%~dp0run-compatible.bat"
echo ^) else ( >> "%~dp0run-compatible.bat"
echo     echo ❌ لم يتم العثور على ملف التطبيق >> "%~dp0run-compatible.bat"
echo     pause >> "%~dp0run-compatible.bat"
echo ^) >> "%~dp0run-compatible.bat"

echo ✅ تم إنشاء ملف التشغيل المتوافق: run-compatible.bat

echo.
echo 6. إنشاء اختصار سطح المكتب...

:: إنشاء اختصار على سطح المكتب
set DESKTOP=%USERPROFILE%\Desktop
if exist "%DESKTOP%" (
    echo Set oWS = WScript.CreateObject("WScript.Shell"^) > "%TEMP%\CreateShortcut.vbs"
    echo sLinkFile = "%DESKTOP%\مؤسسة وقود المستقبل.lnk" >> "%TEMP%\CreateShortcut.vbs"
    echo Set oLink = oWS.CreateShortcut(sLinkFile^) >> "%TEMP%\CreateShortcut.vbs"
    echo oLink.TargetPath = "%~dp0run-compatible.bat" >> "%TEMP%\CreateShortcut.vbs"
    echo oLink.WorkingDirectory = "%~dp0" >> "%TEMP%\CreateShortcut.vbs"
    echo oLink.Description = "مؤسسة وقود المستقبل - متوافق مع Windows 8" >> "%TEMP%\CreateShortcut.vbs"
    echo oLink.Save >> "%TEMP%\CreateShortcut.vbs"
    
    cscript //nologo "%TEMP%\CreateShortcut.vbs"
    del "%TEMP%\CreateShortcut.vbs"
    
    echo ✅ تم إنشاء اختصار على سطح المكتب
) else (
    echo ⚠️  لم يتم العثور على مجلد سطح المكتب
)

echo.
echo ========================================
echo انتهى الإصلاح
echo ========================================
echo.

echo ✅ تم إصلاح مشاكل التوافق مع Windows 8
echo.
echo للتشغيل:
echo 1. استخدم الاختصار على سطح المكتب
echo 2. أو شغل ملف: run-compatible.bat
echo 3. أو شغل التطبيق مباشرة إذا تم حل جميع المشاكل
echo.

echo ملاحظات مهمة:
echo - تأكد من تثبيت .NET Framework 4.8
echo - تأكد من تثبيت Visual C++ Redistributables
echo - قم بإعادة تشغيل النظام إذا لزم الأمر
echo.

pause
