<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد أكواد التفعيل - مؤسسة وقود المستقبل</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .generator-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #2c3e50;
        }

        .form-group select,
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group select:focus,
        .form-group input:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .codes-display {
            background: #2c3e50;
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            max-height: 400px;
            overflow-y: auto;
        }

        .code-item {
            background: rgba(255,255,255,0.1);
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .code-value {
            font-size: 18px;
            font-weight: bold;
            color: #f1c40f;
            margin-bottom: 5px;
        }

        .code-details {
            font-size: 14px;
            opacity: 0.8;
        }

        .master-codes {
            background: #e8f5e8;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .master-codes h3 {
            color: #27ae60;
            margin-bottom: 15px;
        }

        .master-code {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .copy-btn {
            background: #27ae60;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 مولد أكواد التفعيل</h1>
            <p>نظام إدارة بطاقات الغاز الطبيعي المضغوط - مؤسسة وقود المستقبل</p>
        </div>

        <div class="content">
            <!-- الإحصائيات -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="total-codes">7</div>
                    <div>أكواد رئيسية</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="generated-codes">0</div>
                    <div>أكواد مولدة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="active-codes">0</div>
                    <div>أكواد نشطة</div>
                </div>
            </div>

            <!-- الأكواد الرئيسية -->
            <div class="master-codes">
                <h3>🎯 الأكواد الرئيسية (جاهزة للاستخدام)</h3>
                <div id="master-codes-list"></div>
            </div>

            <!-- مولد الأكواد الجديدة -->
            <div class="generator-section">
                <h3>⚡ مولد أكواد التفعيل الجديدة</h3>
                
                <div class="form-group">
                    <label for="code-type">نوع الترخيص:</label>
                    <select id="code-type">
                        <option value="premium">مميز (Premium) - سنة واحدة</option>
                        <option value="platinum">بلاتيني (Platinum) - مدى الحياة</option>
                        <option value="vip">VIP - عامين</option>
                        <option value="enterprise">مؤسسي (Enterprise) - 3 سنوات</option>
                        <option value="master">رئيسي (Master) - غير محدود</option>
                        <option value="owner">مالك (Owner) - غير محدود</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="code-count">عدد الأكواد:</label>
                    <input type="number" id="code-count" value="1" min="1" max="50">
                </div>

                <div class="form-group">
                    <label for="custom-duration">مدة مخصصة (بالأيام) - اختياري:</label>
                    <input type="number" id="custom-duration" placeholder="اتركه فارغ للمدة الافتراضية">
                </div>

                <button class="btn btn-success" onclick="generateNewCodes()">
                    🎲 توليد أكواد جديدة
                </button>

                <button class="btn btn-warning" onclick="showAllCodes()">
                    📋 عرض جميع الأكواد
                </button>

                <button class="btn" onclick="exportCodes()">
                    💾 تصدير الأكواد
                </button>
            </div>

            <!-- عرض الأكواد المولدة -->
            <div id="generated-codes-display" class="codes-display" style="display: none;">
                <h4>🎉 الأكواد المولدة حديثاً:</h4>
                <div id="new-codes-list"></div>
            </div>
        </div>
    </div>

    <script src="src/auth/activation-system.js"></script>
    <script>
        // تحميل الأكواد الرئيسية عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            loadMasterCodes();
            updateStats();
        });

        // تحميل الأكواد الرئيسية
        function loadMasterCodes() {
            const masterCodesList = document.getElementById('master-codes-list');
            const codes = activationSystem.getAllAvailableCodes();
            
            masterCodesList.innerHTML = '';
            codes.forEach(codeData => {
                const codeDiv = document.createElement('div');
                codeDiv.className = 'master-code';
                codeDiv.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div class="code-value">${codeData.code}</div>
                            <div class="code-details">${codeData.description}</div>
                            <small>النوع: ${codeData.type} | المدة: ${codeData.duration}</small>
                        </div>
                        <button class="copy-btn" onclick="copyToClipboard('${codeData.code}')">نسخ</button>
                    </div>
                `;
                masterCodesList.appendChild(codeDiv);
            });
        }

        // توليد أكواد جديدة
        function generateNewCodes() {
            const type = document.getElementById('code-type').value;
            const count = parseInt(document.getElementById('code-count').value);
            const customDuration = document.getElementById('custom-duration').value;
            
            const duration = customDuration ? parseInt(customDuration) : 
                           type === 'premium' ? 365 :
                           type === 'platinum' ? 'unlimited' :
                           type === 'vip' ? 730 :
                           type === 'enterprise' ? 1095 :
                           'unlimited';

            const newCodes = activationSystem.generateMultipleCodes(count, type, duration);
            displayNewCodes(newCodes);
            updateStats();
        }

        // عرض الأكواد الجديدة
        function displayNewCodes(codes) {
            const display = document.getElementById('generated-codes-display');
            const list = document.getElementById('new-codes-list');
            
            list.innerHTML = '';
            codes.forEach(codeData => {
                const codeDiv = document.createElement('div');
                codeDiv.className = 'code-item';
                codeDiv.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div class="code-value">${codeData.code}</div>
                            <div class="code-details">
                                النوع: ${codeData.type} | المدة: ${codeData.duration === 'unlimited' ? 'غير محدود' : codeData.duration + ' يوم'}
                                <br>الأجهزة: ${codeData.deviceLimit} | الحالة: ${codeData.status}
                            </div>
                        </div>
                        <button class="copy-btn" onclick="copyToClipboard('${codeData.code}')">نسخ</button>
                    </div>
                `;
                list.appendChild(codeDiv);
            });
            
            display.style.display = 'block';
        }

        // عرض جميع الأكواد
        function showAllCodes() {
            const masterCodes = activationSystem.getAllAvailableCodes();
            displayNewCodes(masterCodes);
        }

        // نسخ الكود
        function copyToClipboard(code) {
            navigator.clipboard.writeText(code).then(() => {
                alert('تم نسخ الكود: ' + code);
            });
        }

        // تصدير الأكواد
        function exportCodes() {
            const codes = activationSystem.getAllAvailableCodes();
            const exportData = {
                generatedAt: new Date().toISOString(),
                totalCodes: codes.length,
                codes: codes
            };
            
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `activation-codes-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
        }

        // تحديث الإحصائيات
        function updateStats() {
            const codes = activationSystem.getAllAvailableCodes();
            document.getElementById('total-codes').textContent = codes.length;
            document.getElementById('generated-codes').textContent = codes.filter(c => c.type !== 'master').length;
            document.getElementById('active-codes').textContent = codes.filter(c => c.type === 'master' || c.type === 'owner').length;
        }
    </script>
</body>
</html>
