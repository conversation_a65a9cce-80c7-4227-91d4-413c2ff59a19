; ملف إعداد Inno Setup لمؤسسة وقود المستقبل
; Future Fuel Corporation Management System Installer

#define MyAppName "مؤسسة وقود المستقبل"
#define MyAppNameEn "Future Fuel Corporation"
#define MyAppVersion "2.2.0"
#define MyAppPublisher "Future Fuel Corporation"
#define MyAppURL "https://github.com/future-fuel/gas-shop-management"
#define MyAppExeName "-.exe"
#define MyAppId "{{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}"

[Setup]
; معلومات التطبيق الأساسية
AppId={#MyAppId}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppVerName={#MyAppName} {#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
AppCopyright=Copyright © 2024 {#MyAppPublisher}

; مسارات التثبيت
DefaultDirName={autopf}\{#MyAppNameEn}
DefaultGroupName={#MyAppName}
DisableProgramGroupPage=yes
LicenseFile=license.txt
InfoBeforeFile=readme.txt
OutputDir=..\build\installer
OutputBaseFilename=Future-Fuel-Setup-{#MyAppVersion}
SetupIconFile=..\assets\icons\app-icon.ico
UninstallDisplayIcon={app}\{#MyAppExeName}

; إعدادات الضغط والتشفير
Compression=lzma2/ultra64
SolidCompression=yes


; إعدادات النظام
PrivilegesRequired=admin
PrivilegesRequiredOverridesAllowed=dialog
ArchitecturesAllowed=x64 x86
ArchitecturesInstallIn64BitMode=x64
MinVersion=6.1sp1

; إعدادات الواجهة
WizardStyle=modern
WizardImageFile=wizard-image.bmp
WizardSmallImageFile=wizard-small.bmp
ShowLanguageDialog=no
LanguageDetectionMethod=uilanguage

; إعدادات إضافية
AllowNoIcons=yes
AlwaysRestart=no
RestartIfNeededByRun=no
CreateAppDir=yes
DirExistsWarning=auto
EnableDirDoesntExistWarning=yes
ShowTasksTreeLines=yes
ShowComponentSizes=yes
FlatComponentsList=no

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "إنشاء اختصار على سطح المكتب"; GroupDescription: "اختصارات إضافية:"; Flags: unchecked
Name: "quicklaunchicon"; Description: "إنشاء اختصار في شريط المهام"; GroupDescription: "اختصارات إضافية:"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "startmenu"; Description: "إضافة إلى قائمة ابدأ"; GroupDescription: "اختصارات إضافية:"
Name: "autostart"; Description: "تشغيل تلقائي مع بدء النظام"; GroupDescription: "خيارات التشغيل:"; Flags: unchecked
Name: "associate"; Description: "ربط ملفات البيانات (.ffdata) بالتطبيق"; GroupDescription: "ربط الملفات:"

[Files]
; الملفات الأساسية للتطبيق
Source: "..\*.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\*.dll"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\*.pak"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\*.dat"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\*.bin"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\*.json"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\version"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\LICENSE*"; DestDir: "{app}"; Flags: ignoreversion

; مجلد الموارد
Source: "..\resources\*"; DestDir: "{app}\resources"; Flags: ignoreversion recursesubdirs createallsubdirs

; مجلد المحليات
Source: "..\locales\*"; DestDir: "{app}\locales"; Flags: ignoreversion recursesubdirs createallsubdirs

; ملفات إضافية
Source: "..\README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "..\كيفية-الاستخدام.txt"; DestDir: "{app}"; Flags: ignoreversion

; ملفات التكوين (يتم إنشاؤها عند أول تشغيل)
Source: "default-config.json"; DestDir: "{app}"; DestName: "config.json"; Flags: onlyifdoesntexist

[Registry]
; ربط ملفات البيانات
Root: HKCR; Subkey: ".ffdata"; ValueType: string; ValueName: ""; ValueData: "FutureFuelData"; Flags: uninsdeletevalue; Tasks: associate
Root: HKCR; Subkey: "FutureFuelData"; ValueType: string; ValueName: ""; ValueData: "Future Fuel Data File"; Flags: uninsdeletekey; Tasks: associate
Root: HKCR; Subkey: "FutureFuelData\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\{#MyAppExeName},0"; Tasks: associate
Root: HKCR; Subkey: "FutureFuelData\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\{#MyAppExeName}"" ""%1"""; Tasks: associate

; إعدادات التطبيق في الريجستري
Root: HKCU; Subkey: "Software\{#MyAppPublisher}\{#MyAppNameEn}"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"
Root: HKCU; Subkey: "Software\{#MyAppPublisher}\{#MyAppNameEn}"; ValueType: string; ValueName: "Version"; ValueData: "{#MyAppVersion}"
Root: HKCU; Subkey: "Software\{#MyAppPublisher}\{#MyAppNameEn}"; ValueType: dword; ValueName: "FirstRun"; ValueData: 1

; التشغيل التلقائي
Root: HKCU; Subkey: "Software\Microsoft\Windows\CurrentVersion\Run"; ValueType: string; ValueName: "{#MyAppNameEn}"; ValueData: """{app}\{#MyAppExeName}"" --startup"; Tasks: autostart

[Icons]
; اختصارات قائمة ابدأ
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: startmenu
Name: "{group}\دليل المستخدم"; Filename: "{app}\README.md"; Tasks: startmenu
Name: "{group}\كيفية الاستخدام"; Filename: "{app}\كيفية-الاستخدام.txt"; Tasks: startmenu
Name: "{group}\إلغاء التثبيت"; Filename: "{uninstallexe}"; Tasks: startmenu

; اختصار سطح المكتب
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

; اختصار شريط المهام
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: quicklaunchicon

[Run]
; تشغيل التطبيق بعد التثبيت
Filename: "{app}\{#MyAppExeName}"; Description: "تشغيل {#MyAppName}"; Flags: nowait postinstall skipifsilent

; فتح دليل المستخدم
Filename: "{app}\README.md"; Description: "عرض دليل المستخدم"; Flags: postinstall skipifsilent shellexec unchecked

[UninstallRun]
; تنظيف الملفات المؤقتة عند إلغاء التثبيت
Filename: "{cmd}"; Parameters: "/c rmdir /s /q ""{userappdata}\{#MyAppNameEn}"""; Flags: runhidden

[UninstallDelete]
; حذف الملفات المتبقية
Type: filesandordirs; Name: "{app}\logs"
Type: filesandordirs; Name: "{app}\backups"
Type: filesandordirs; Name: "{app}\temp"
Type: files; Name: "{app}\*.log"
Type: files; Name: "{app}\*.tmp"

[Code]
// كود Pascal للتحكم في عملية التثبيت

// فحص متطلبات النظام
function InitializeSetup(): Boolean;
var
  Version: TWindowsVersion;
begin
  GetWindowsVersionEx(Version);
  
  // فحص إصدار Windows (Windows 7 SP1 أو أحدث)
  if (Version.Major < 6) or ((Version.Major = 6) and (Version.Minor = 1) and (Version.ServicePackMajor < 1)) then
  begin
    MsgBox('يتطلب هذا التطبيق Windows 7 SP1 أو إصدار أحدث.', mbError, MB_OK);
    Result := False;
  end
  else
    Result := True;
end;

// فحص التطبيقات المتضاربة
function PrepareToInstall(var NeedsRestart: Boolean): String;
var
  ResultCode: Integer;
begin
  // إغلاق التطبيق إذا كان يعمل
  if CheckForMutexes('{#MyAppId}-Mutex') then
  begin
    if MsgBox('التطبيق يعمل حالياً. هل تريد إغلاقه والمتابعة؟', mbConfirmation, MB_YESNO) = IDYES then
    begin
      // محاولة إغلاق التطبيق بلطف
      Exec('taskkill', '/f /im "{#MyAppExeName}"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
    end
    else
    begin
      Result := 'يجب إغلاق التطبيق قبل المتابعة.';
      Exit;
    end;
  end;
  
  Result := '';
end;

// إنشاء ملفات التكوين الافتراضية
procedure CurStepChanged(CurStep: TSetupStep);
var
  ConfigFile: String;
  ConfigContent: String;
begin
  if CurStep = ssPostInstall then
  begin
    // إنشاء مجلدات البيانات
    CreateDir(ExpandConstant('{userappdata}\{#MyAppNameEn}'));
    CreateDir(ExpandConstant('{userappdata}\{#MyAppNameEn}\data'));
    CreateDir(ExpandConstant('{userappdata}\{#MyAppNameEn}\backups'));
    CreateDir(ExpandConstant('{userappdata}\{#MyAppNameEn}\logs'));
    
    // إنشاء ملف التكوين الافتراضي
    ConfigFile := ExpandConstant('{userappdata}\{#MyAppNameEn}\config.json');
    if not FileExists(ConfigFile) then
    begin
      ConfigContent := '{' + #13#10 +
        '  "version": "{#MyAppVersion}",' + #13#10 +
        '  "language": "ar",' + #13#10 +
        '  "theme": "light",' + #13#10 +
        '  "autoBackup": true,' + #13#10 +
        '  "backupInterval": 24,' + #13#10 +
        '  "dataPath": "' + ExpandConstant('{userappdata}\{#MyAppNameEn}\data') + '",' + #13#10 +
        '  "backupPath": "' + ExpandConstant('{userappdata}\{#MyAppNameEn}\backups') + '",' + #13#10 +
        '  "logPath": "' + ExpandConstant('{userappdata}\{#MyAppNameEn}\logs') + '"' + #13#10 +
        '}';
      SaveStringToFile(ConfigFile, ConfigContent, False);
    end;
  end;
end;

// رسالة ترحيب مخصصة
function NextButtonClick(CurPageID: Integer): Boolean;
begin
  if CurPageID = wpWelcome then
  begin
    MsgBox('مرحباً بك في برنامج إدارة مؤسسة وقود المستقبل!' + #13#10 + #13#10 +
           'هذا البرنامج سيساعدك في إدارة محطة الغاز بكفاءة عالية.' + #13#10 + #13#10 +
           'للدعم الفني: 0696924176', mbInformation, MB_OK);
  end;
  Result := True;
end;

// رسالة الانتهاء
procedure CurPageChanged(CurPageID: Integer);
begin
  if CurPageID = wpFinished then
  begin
    WizardForm.FinishedLabel.Caption := 
      'تم تثبيت ' + '{#MyAppName}' + ' بنجاح!' + #13#10 + #13#10 +
      'يمكنك الآن تشغيل البرنامج والبدء في استخدامه.' + #13#10 + #13#10 +
      'للحصول على ترخيص التفعيل، يرجى التواصل مع المطور:' + #13#10 +
      'الهاتف: 0696924176' + #13#10 +
      'واتساب: 0696924176';
  end;
end;
