@echo off
chcp 65001 >nul
title إعادة بناء التطبيق لدعم Windows 8

echo.
echo ========================================
echo    إعادة بناء التطبيق لدعم Windows 8
echo    مؤسسة وقود المستقبل
echo ========================================
echo.

echo هذا الملف سيقوم بإعادة بناء التطبيق مع دعم محسن لـ Windows 8
echo.

:: التحقق من وجود Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت!
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo ✅ Node.js متوفر
node --version

:: التحقق من وجود npm
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متوفر!
    pause
    exit /b 1
)

echo ✅ npm متوفر
npm --version

echo.
echo 📦 الخطوة 1: تنظيف المشروع...

:: حذف node_modules إذا كان موجوداً
if exist "node_modules" (
    echo حذف node_modules القديم...
    rmdir /s /q "node_modules"
)

:: حذف package-lock.json إذا كان موجوداً
if exist "package-lock.json" (
    echo حذف package-lock.json القديم...
    del "package-lock.json"
)

echo ✅ تم تنظيف المشروع

echo.
echo 📥 الخطوة 2: تثبيت المتطلبات...

echo تثبيت المتطلبات الأساسية...
npm install

if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت المتطلبات!
    echo جرب تشغيل الأمر التالي يدوياً:
    echo npm install --force
    pause
    exit /b 1
)

echo ✅ تم تثبيت المتطلبات بنجاح

echo.
echo 🔧 الخطوة 3: إعداد التوافق مع Windows 8...

:: إنشاء ملف إعدادات خاص لـ Windows 8
echo إنشاء ملف إعدادات التوافق...
(
echo {
echo   "build": {
echo     "appId": "com.futurefuel.gasshop",
echo     "productName": "مؤسسة وقود المستقبل",
echo     "win": {
echo       "target": [
echo         {
echo           "target": "nsis",
echo           "arch": ["x64", "ia32"]
echo         },
echo         {
echo           "target": "portable", 
echo           "arch": ["x64", "ia32"]
echo         }
echo       ],
echo       "requestedExecutionLevel": "asInvoker",
echo       "verifyUpdateCodeSignature": false
echo     },
echo     "nsis": {
echo       "oneClick": false,
echo       "perMachine": false,
echo       "allowToChangeInstallationDirectory": true,
echo       "allowElevation": true,
echo       "createDesktopShortcut": true,
echo       "createStartMenuShortcut": true,
echo       "runAfterFinish": true,
echo       "deleteAppDataOnUninstall": false
echo     },
echo     "compression": "normal",
echo     "npmRebuild": false,
echo     "nodeGypRebuild": false
echo   }
echo }
) > "electron-builder-win8.json"

echo ✅ تم إنشاء ملف إعدادات التوافق

echo.
echo 🏗️ الخطوة 4: بناء التطبيق...

echo بناء التطبيق للأنظمة 64-bit و 32-bit...
npm run build-win

if %errorlevel% neq 0 (
    echo ⚠️  فشل البناء العادي، جرب البناء المتوافق...
    
    :: محاولة البناء مع إعدادات مخصصة
    npx electron-builder --win --config electron-builder-win8.json
    
    if %errorlevel% neq 0 (
        echo ❌ فشل في بناء التطبيق!
        echo.
        echo حلول مقترحة:
        echo 1. تأكد من اتصال الإنترنت
        echo 2. شغل الأمر كمدير
        echo 3. تأكد من توفر مساحة كافية على القرص
        echo 4. جرب البناء اليدوي:
        echo    npx electron-builder --win --x64
        echo    npx electron-builder --win --ia32
        pause
        exit /b 1
    )
)

echo ✅ تم بناء التطبيق بنجاح

echo.
echo 📁 الخطوة 5: نسخ الملفات المساعدة...

:: نسخ ملفات Windows 8 إلى مجلد البناء
set BUILD_DIR=dist

if exist "%BUILD_DIR%" (
    echo نسخ ملفات دعم Windows 8...
    
    if exist "check-compatibility.bat" copy "check-compatibility.bat" "%BUILD_DIR%\"
    if exist "fix-windows8-compatibility.bat" copy "fix-windows8-compatibility.bat" "%BUILD_DIR%\"
    if exist "setup-windows8.bat" copy "setup-windows8.bat" "%BUILD_DIR%\"
    if exist "تعليمات-Windows8.txt" copy "تعليمات-Windows8.txt" "%BUILD_DIR%\"
    if exist "Windows8-Troubleshooting-Guide.md" copy "Windows8-Troubleshooting-Guide.md" "%BUILD_DIR%\"
    
    echo ✅ تم نسخ ملفات الدعم
) else (
    echo ⚠️  مجلد البناء غير موجود
)

echo.
echo 🧹 الخطوة 6: تنظيف الملفات المؤقتة...

if exist "electron-builder-win8.json" del "electron-builder-win8.json"

echo ✅ تم تنظيف الملفات المؤقتة

echo.
echo ========================================
echo    اكتمل البناء بنجاح! 🎉
echo ========================================
echo.

echo ✅ تم بناء التطبيق مع دعم محسن لـ Windows 8
echo.

if exist "%BUILD_DIR%" (
    echo 📁 ملفات التطبيق متوفرة في مجلد: %BUILD_DIR%
    echo.
    
    echo الملفات المتاحة:
    dir "%BUILD_DIR%\*.exe" /b 2>nul
    echo.
    
    echo ملفات الدعم:
    if exist "%BUILD_DIR%\check-compatibility.bat" echo ✓ check-compatibility.bat
    if exist "%BUILD_DIR%\fix-windows8-compatibility.bat" echo ✓ fix-windows8-compatibility.bat
    if exist "%BUILD_DIR%\setup-windows8.bat" echo ✓ setup-windows8.bat
    if exist "%BUILD_DIR%\تعليمات-Windows8.txt" echo ✓ تعليمات-Windows8.txt
    
    echo.
    choice /c YN /m "هل تريد فتح مجلد البناء؟ (Y/N)"
    if !errorlevel! equ 1 (
        explorer "%BUILD_DIR%"
    )
) else (
    echo ⚠️  لم يتم العثور على مجلد البناء
    echo تحقق من وجود أخطاء في عملية البناء
)

echo.
echo نصائح للتوزيع على Windows 8:
echo 1. اختبر التطبيق على جهاز Windows 8 قبل التوزيع
echo 2. أرفق ملفات الدعم مع التطبيق
echo 3. وفر تعليمات التثبيت للمستخدمين
echo 4. تأكد من توفر .NET Framework 4.8 على الأجهزة المستهدفة
echo.

echo شكراً لاستخدام مؤسسة وقود المستقبل! 🚀
pause
