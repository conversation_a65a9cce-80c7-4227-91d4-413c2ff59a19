// بيانات التطبيق
let appData = {
    customers: [],
    vehicles: [],
    gasTanks: [], // خزانات الغاز
    gasCards: [],
    appointments: [],
    debts: [],
    debtPayments: [],
    suppliers: [],
    inventory: [],
    sales: [],
    purchases: [],
    stockAdjustments: [],
    notifications: [],
    transmissionTable: [], // جدول الإرسال
    installationCertificates: [], // شهادات التركيب
    monitoringCertificates: [], // شهادات المراقبة الدورية

    settings: {
        shopName: 'مؤسسة وقود المستقبل',
        reminderDays: 30,
        workingHoursStart: '08:00',
        workingHoursEnd: '18:00',
        debtReminderDays: 7,
        telegram: {
            botToken: '',
            chatId: '',
            enabled: false,
            autoBackup: true,
            backupFrequency: 'daily' // daily, weekly, manual
        }
    }
};

// تحميل البيانات
function loadData() {
    try {
        // التحقق من وجود واجهة Electron
        if (window.electronAPI) {
            // تحميل البيانات من ملف
            const savedData = window.electronAPI.loadData();
            if (savedData) {
                appData = savedData;
            } else {
                // إذا لم يتم العثور على ملف البيانات، استخدم localStorage كاحتياطي
                loadFromLocalStorage();
            }
        } else {
            // استخدام localStorage إذا لم تكن واجهة Electron متاحة
            loadFromLocalStorage();
        }

        // التحقق من صحة البيانات
        if (!appData.customers) appData.customers = [];
        if (!appData.vehicles) appData.vehicles = [];
        if (!appData.gasTanks) appData.gasTanks = [];
        if (!appData.gasCards) appData.gasCards = [];
        if (!appData.appointments) appData.appointments = [];
        if (!appData.debts) appData.debts = [];
        if (!appData.debtPayments) appData.debtPayments = [];
        if (!appData.suppliers) appData.suppliers = [];
        if (!appData.inventory) appData.inventory = [];
        if (!appData.sales) appData.sales = [];
        if (!appData.purchases) appData.purchases = [];
        if (!appData.stockAdjustments) appData.stockAdjustments = [];
        if (!appData.notifications) appData.notifications = [];
        if (!appData.transmissionTable) appData.transmissionTable = [];
        if (!appData.installationCertificates) appData.installationCertificates = [];
        if (!appData.monitoringCertificates) appData.monitoringCertificates = [];

        if (!appData.settings) {
            appData.settings = {
                shopName: 'مؤسسة وقود المستقبل',
                reminderDays: 30,
                workingHoursStart: '08:00',
                workingHoursEnd: '18:00',
                debtReminderDays: 7
            };
        } else {
            // إضافة إعدادات جديدة إذا لم تكن موجودة
            if (appData.settings.debtReminderDays === undefined) {
                appData.settings.debtReminderDays = 7;
            }
        }

        // إضافة حقل createdAt إذا لم يكن موجوداً
        const now = new Date().toISOString();
        appData.customers.forEach(customer => {
            if (!customer.createdAt) customer.createdAt = now;

            // إضافة حقل lastVisitDate إذا لم يكن موجوداً
            if (!customer.lastVisitDate) {
                // البحث عن آخر موعد للزبون
                const customerAppointments = appData.appointments.filter(app => app.customerId === customer.id);
                if (customerAppointments.length > 0) {
                    const sortedAppointments = customerAppointments.sort((a, b) => new Date(b.date) - new Date(a.date));
                    customer.lastVisitDate = sortedAppointments[0].date;
                }
            }
        });
        appData.vehicles.forEach(vehicle => {
            if (!vehicle.createdAt) vehicle.createdAt = now;
        });
        appData.gasCards.forEach(card => {
            if (!card.createdAt) card.createdAt = now;
        });
        appData.appointments.forEach(appointment => {
            if (!appointment.createdAt) appointment.createdAt = now;
        });

        updateDashboard();
        updateAllTables();
        console.log('تم تحميل البيانات بنجاح');
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        resetData();
    }
}

// تحميل البيانات من localStorage
function loadFromLocalStorage() {
    const savedData = localStorage.getItem('gasShopData');
    if (savedData) {
        try {
            appData = JSON.parse(savedData);
            console.log('تم تحميل البيانات من localStorage');
        } catch (error) {
            console.error('خطأ في تحميل البيانات من localStorage:', error);
            // إنشاء نسخة احتياطية من البيانات التالفة
            localStorage.setItem('gasShopData_backup_' + new Date().getTime(), savedData);
            resetData();
        }
    } else {
        console.log('لا توجد بيانات محفوظة، تم إنشاء بيانات جديدة');
        resetData();
    }
}

// حفظ البيانات
async function saveData() {
    try {
        let saveSuccess = false;

        // حفظ البيانات في Electron إذا كانت متاحة
        if (window.electronAPI) {
            saveSuccess = window.electronAPI.saveData(appData);
            if (saveSuccess) {
                showToast('تم حفظ البيانات بنجاح', true);

                // إنشاء نسخة احتياطية تلقائية كل 10 مرات حفظ
                const saveCount = parseInt(localStorage.getItem('saveCount') || '0') + 1;
                localStorage.setItem('saveCount', saveCount.toString());

                if (saveCount % 10 === 0) {
                    window.electronAPI.createBackup();
                }
            } else {
                // إذا فشل الحفظ في Electron، استخدم localStorage كاحتياطي
                saveSuccess = saveToLocalStorage();
            }
        } else {
            // استخدام localStorage إذا لم تكن واجهة Electron متاحة
            saveSuccess = saveToLocalStorage();
        }

        if (saveSuccess) {
            showToast('تم حفظ البيانات بنجاح', true);
        } else {
            showToast('حدث خطأ أثناء حفظ البيانات', false);
        }

        return saveSuccess;
    } catch (error) {
        console.error('خطأ في حفظ البيانات:', error);
        showToast('حدث خطأ أثناء حفظ البيانات', false);
        return false;
    }
}

// حفظ البيانات في localStorage
function saveToLocalStorage() {
    try {
        localStorage.setItem('gasShopData', JSON.stringify(appData));

        // حفظ نسخة احتياطية محسنة
        createAutoBackup();

        return true;
    } catch (error) {
        console.error('خطأ في حفظ البيانات في localStorage:', error);
        return false;
    }
}

// إنشاء نسخة احتياطية تلقائية محسنة مع دعم الخادم
async function createAutoBackup() {
    try {
        const saveCount = parseInt(localStorage.getItem('saveCount') || '0') + 1;
        localStorage.setItem('saveCount', saveCount.toString());

        // إنشاء نسخة احتياطية كل 5 مرات حفظ أو كل ساعة
        const lastBackupTime = parseInt(localStorage.getItem('lastBackupTime') || '0');
        const currentTime = new Date().getTime();
        const oneHour = 60 * 60 * 1000; // ساعة واحدة بالميلي ثانية

        const shouldCreateBackup = (saveCount % 5 === 0) || (currentTime - lastBackupTime > oneHour);

        if (shouldCreateBackup) {
            const timestamp = new Date().getTime();
            const backupName = `gasShopData_backup_${timestamp}`;

            // إنشاء معلومات النسخة الاحتياطية المحسنة
            const backupInfo = {
                timestamp: timestamp,
                date: new Date().toISOString(),
                version: '2.2.0',
                type: 'auto',
                dataSize: JSON.stringify(appData).length,
                checksum: generateChecksum(JSON.stringify(appData)),
                userAgent: navigator.userAgent,
                url: window.location.href,
                recordsCount: {
                    customers: appData.customers?.length || 0,
                    vehicles: appData.vehicles?.length || 0,
                    gasCards: appData.gasCards?.length || 0,
                    appointments: appData.appointments?.length || 0,
                    debts: appData.debts?.length || 0,
                    suppliers: appData.suppliers?.length || 0,
                    inventory: appData.inventory?.length || 0,
                    sales: appData.sales?.length || 0,
                    purchases: appData.purchases?.length || 0,
                    certificates: (appData.installationCertificates?.length || 0) + (appData.monitoringCertificates?.length || 0)
                }
            };

            // حفظ النسخة الاحتياطية مع المعلومات
            const backupData = {
                info: backupInfo,
                data: appData
            };

            // حفظ محلياً
            localStorage.setItem(backupName, JSON.stringify(backupData));
            localStorage.setItem('lastBackupTime', timestamp.toString());

            // تخطي حفظ النسخة الاحتياطية على الخادم (غير متاح حالياً)
            // يمكن إضافة هذه الميزة لاحقاً عند توفر خادم

            // إدارة النسخ الاحتياطية - الاحتفاظ بآخر 10 نسخ
            manageBackups();

            console.log(`تم إنشاء نسخة احتياطية: ${backupName}`);

            // إشعار محسن
            if (typeof notificationManager !== 'undefined') {
                notificationManager.showBackupSuccess(backupName);
            } else {
                // إضافة إشعار للمستخدم (النظام القديم)
                addNotification(
                    '💾 نسخة احتياطية',
                    'تم إنشاء نسخة احتياطية تلقائية من البيانات',
                    'info'
                );
            }

            // إرسال إلى تيليجرام إذا كان مفعلاً
            if (appData.settings.telegram?.enabled && appData.settings.telegram?.autoBackup) {
                sendBackupToTelegram('auto').catch(error => {
                    console.error('خطأ في إرسال النسخة الاحتياطية إلى تيليجرام:', error);
                });
            }

            return backupName;
        }
    } catch (error) {
        console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
        if (typeof notificationManager !== 'undefined') {
            notificationManager.error('خطأ في النسخة الاحتياطية', error.message);
        }
    }
}

// إنشاء checksum للبيانات
function generateChecksum(data) {
    let hash = 0;
    if (data.length === 0) return hash;
    for (let i = 0; i < data.length; i++) {
        const char = data.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
}

// حفظ النسخة الاحتياطية على الخادم (معطل حالياً)
// يمكن تفعيل هذه الدوال عند توفر خادم
function saveBackupToServer(backupData, backupName) {
    // دالة معطلة - يمكن تطويرها لاحقاً
    console.log('حفظ النسخة الاحتياطية على الخادم معطل حالياً');
    return Promise.resolve(null);
}

// إنشاء نسخة احتياطية على الخادم (معطل حالياً)
function createServerBackup() {
    // دالة معطلة - يمكن تطويرها لاحقاً
    console.log('إنشاء نسخة احتياطية على الخادم معطل حالياً');
    return Promise.resolve(null);
}

// إدارة النسخ الاحتياطية
function manageBackups() {
    try {
        const backups = [];
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('gasShopData_backup_')) {
                backups.push(key);
            }
        }

        // ترتيب النسخ حسب التاريخ (الأحدث أولاً)
        backups.sort((a, b) => {
            const timeA = parseInt(a.split('_')[2]);
            const timeB = parseInt(b.split('_')[2]);
            return timeB - timeA;
        });

        // حذف النسخ الزائدة (الاحتفاظ بآخر 10 نسخ)
        if (backups.length > 10) {
            for (let i = 10; i < backups.length; i++) {
                localStorage.removeItem(backups[i]);
                console.log(`تم حذف النسخة الاحتياطية القديمة: ${backups[i]}`);
            }
        }
    } catch (error) {
        console.error('خطأ في إدارة النسخ الاحتياطية:', error);
    }
}

// إعادة تعيين البيانات
function resetData() {
    appData = {
        customers: [],
        vehicles: [],
        gasTanks: [],
        gasCards: [],
        appointments: [],
        debts: [],
        debtPayments: [],
        suppliers: [],
        inventory: [],
        sales: [],
        purchases: [],
        stockAdjustments: [],
        notifications: [],

        settings: {
            shopName: 'مؤسسة وقود المستقبل',
            reminderDays: 30,
            workingHoursStart: '08:00',
            workingHoursEnd: '18:00',
            debtReminderDays: 7
        }
    };
    saveData();
    updateDashboard();
    updateAllTables();
}

// تحديث لوحة التحكم
function updateDashboard() {
    // تحديث عدادات البطاقات
    document.getElementById('total-cards').textContent = appData.gasCards.length;

    // حساب البطاقات التي تحتاج للتجديد
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(today.getDate() + appData.settings.reminderDays);

    const cardsToRenew = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        return expiryDate <= thirtyDaysFromNow && expiryDate >= today;
    });

    document.getElementById('cards-to-renew').textContent = cardsToRenew.length;

    // تحديث عدادات المواعيد
    const todayStr = today.toISOString().split('T')[0];
    const todayAppointments = appData.appointments.filter(app => app.date === todayStr);
    document.getElementById('today-appointments').textContent = todayAppointments.length;

    const upcomingAppointments = appData.appointments.filter(app => {
        const appDate = new Date(app.date);
        return appDate >= today;
    });
    document.getElementById('upcoming-appointments').textContent = upcomingAppointments.length;

    // تحديث عدادات الديون
    const activeDebts = appData.debts.filter(debt => !debt.isPaid);
    document.getElementById('active-debts').textContent = activeDebts.length;

    // حساب الديون المتأخرة
    const overdueDebts = activeDebts.filter(debt => {
        const dueDate = new Date(debt.dueDate);
        dueDate.setHours(0, 0, 0, 0);
        return dueDate < today;
    });
    document.getElementById('overdue-debts').textContent = overdueDebts.length;

    // تحديث عدادات المخزون
    document.getElementById('total-items').textContent = appData.inventory.length;

    // حساب الأصناف منخفضة المخزون
    const lowStockItems = appData.inventory.filter(item => item.quantity <= item.minQuantity);
    document.getElementById('low-stock-items').textContent = lowStockItems.length;

    // تحديث عدادات المبيعات
    const todaySales = appData.sales.filter(sale => sale.date === todayStr);
    const todaySalesAmount = todaySales.reduce((sum, sale) => sum + sale.total, 0);
    document.getElementById('today-sales').textContent = formatCurrency(todaySalesAmount);

    const monthSales = appData.sales.filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate.getMonth() === today.getMonth() && saleDate.getFullYear() === today.getFullYear();
    });
    const monthSalesAmount = monthSales.reduce((sum, sale) => sum + sale.total, 0);
    document.getElementById('month-sales').textContent = formatCurrency(monthSalesAmount);

    // تحديث عدادات المشتريات
    const todayPurchases = appData.purchases.filter(purchase => purchase.date === todayStr);
    const todayPurchasesAmount = todayPurchases.reduce((sum, purchase) => sum + purchase.total, 0);
    document.getElementById('today-purchases').textContent = formatCurrency(todayPurchasesAmount);

    const monthPurchases = appData.purchases.filter(purchase => {
        const purchaseDate = new Date(purchase.date);
        return purchaseDate.getMonth() === today.getMonth() && purchaseDate.getFullYear() === today.getFullYear();
    });
    const monthPurchasesAmount = monthPurchases.reduce((sum, purchase) => sum + purchase.total, 0);
    document.getElementById('month-purchases').textContent = formatCurrency(monthPurchasesAmount);

    // تحديث عدادات الزبائن
    document.getElementById('total-customers').textContent = appData.customers.length;

    // حساب الزبائن الجدد هذا الشهر
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const newCustomers = appData.customers.filter(customer => {
        const createdDate = new Date(customer.createdAt);
        return createdDate >= firstDayOfMonth;
    });
    document.getElementById('new-customers').textContent = newCustomers.length;

    // تحديث عدادات الموردين
    document.getElementById('total-suppliers').textContent = appData.suppliers.length;

    const activeSuppliers = appData.suppliers.filter(supplier => supplier.status === 'نشط');
    document.getElementById('active-suppliers').textContent = activeSuppliers.length;

    // تحديث جدول البطاقات التي تحتاج للتجديد
    const upcomingRenewalsTable = document.getElementById('upcoming-renewals').querySelector('tbody');
    upcomingRenewalsTable.innerHTML = '';

    cardsToRenew.sort((a, b) => new Date(a.expiryDate) - new Date(b.expiryDate)).slice(0, 5).forEach(card => {
        const vehicle = appData.vehicles.find(v => v.id === card.vehicleId);
        const customer = appData.customers.find(c => c.id === card.customerId);

        if (vehicle && customer) {
            const expiryDate = new Date(card.expiryDate);
            const daysLeft = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${customer.name}</td>
                <td>${vehicle.plateNumber}</td>
                <td>${formatDate(card.expiryDate)}</td>
                <td>${daysLeft} يوم</td>
            `;
            upcomingRenewalsTable.appendChild(row);
        }
    });

    // تحديث جدول مواعيد اليوم
    const todayAppointmentsTable = document.getElementById('today-appointments-table').querySelector('tbody');
    todayAppointmentsTable.innerHTML = '';

    todayAppointments.sort((a, b) => a.time.localeCompare(b.time)).forEach(appointment => {
        const vehicle = appData.vehicles.find(v => v.id === appointment.vehicleId);
        const customer = appData.customers.find(c => c.id === appointment.customerId);

        if (vehicle && customer) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${appointment.time}</td>
                <td>${customer.name}</td>
                <td>${vehicle.plateNumber}</td>
                <td>${appointment.service}</td>
            `;
            todayAppointmentsTable.appendChild(row);
        }
    });

    // تحديث جدول الديون المستحقة قريباً
    const upcomingDebtsTable = document.getElementById('upcoming-debts').querySelector('tbody');
    upcomingDebtsTable.innerHTML = '';

    // الديون التي ستستحق خلال فترة التذكير
    const upcomingDueDebts = activeDebts.filter(debt => {
        const dueDate = new Date(debt.dueDate);
        dueDate.setHours(0, 0, 0, 0);
        return dueDate >= today && dueDate <= new Date(today.getTime() + appData.settings.debtReminderDays * 24 * 60 * 60 * 1000);
    });

    upcomingDueDebts.sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate)).slice(0, 5).forEach(debt => {
        const customer = appData.customers.find(c => c.id === debt.customerId);
        if (customer) {
            // حساب المبلغ المتبقي
            const payments = appData.debtPayments.filter(payment => payment.debtId === debt.id);
            const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
            const remainingAmount = debt.amount - totalPaid;

            // حساب الأيام المتبقية
            const dueDate = new Date(debt.dueDate);
            dueDate.setHours(0, 0, 0, 0);
            const daysLeft = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${customer.name}</td>
                <td>${remainingAmount.toFixed(2)}</td>
                <td>${formatDate(debt.dueDate)}</td>
                <td>${daysLeft} يوم</td>
            `;
            upcomingDebtsTable.appendChild(row);
        }
    });

    // إضافة رسالة إذا لم تكن هناك بيانات
    if (cardsToRenew.length === 0) {
        upcomingRenewalsTable.innerHTML = '<tr><td colspan="4" class="text-center">لا توجد بطاقات تحتاج للتجديد قريباً</td></tr>';
    }

    if (todayAppointments.length === 0) {
        todayAppointmentsTable.innerHTML = '<tr><td colspan="4" class="text-center">لا توجد مواعيد اليوم</td></tr>';
    }

    if (upcomingDueDebts.length === 0) {
        upcomingDebtsTable.innerHTML = '<tr><td colspan="4" class="text-center">لا توجد ديون مستحقة قريباً</td></tr>';
    }
}


// تحديث جميع الجداول
function updateAllTables() {
    updateGasCardsTable();
    updateAppointmentsTable();
    updateCustomersTable();
    updateDebtsTable();
    updateSuppliersTable();
    updateInventoryTable();
    updateSalesTable();
    updatePurchasesTable();
    updateCalendar();
}

// تحديث جدول بطاقات الغاز
function updateGasCardsTable() {
    const table = document.getElementById('gas-cards-table').querySelector('tbody');
    table.innerHTML = '';

    appData.gasCards.forEach(card => {
        const vehicle = appData.vehicles.find(v => v.id === card.vehicleId);
        const customer = appData.customers.find(c => c.id === card.customerId);

        if (vehicle && customer) {
            const today = new Date();
            const expiryDate = new Date(card.expiryDate);
            let status = '';
            let statusClass = '';

            if (expiryDate < today) {
                status = 'منتهية';
                statusClass = 'status-expired';
            } else if (expiryDate <= new Date(today.getTime() + appData.settings.reminderDays * 24 * 60 * 60 * 1000)) {
                status = 'قريبة الانتهاء';
                statusClass = 'status-expiring';
            } else {
                status = 'سارية';
                statusClass = 'status-active';
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${customer.name}</td>
                <td>${vehicle.plateNumber}</td>
                <td>${card.cardNumber}</td>
                <td>${formatDate(card.issueDate)}</td>
                <td>${formatDate(card.expiryDate)}</td>
                <td>
                    <span class="operation-type ${card.operationType || 'تجديد'}">${card.operationType || 'تجديد بطاقة'}</span>
                </td>
                <td class="notes-cell" title="${card.notes || 'لا توجد ملاحظات'}">
                    ${card.notes ? (card.notes.length > 30 ? card.notes.substring(0, 30) + '...' : card.notes) : '-'}
                </td>
                <td class="${statusClass}">${status}</td>
                <td class="action-cell">
                    <button class="action-btn edit" data-id="${card.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                    <button class="action-btn delete" data-id="${card.id}" title="حذف"><i class="fas fa-trash"></i></button>
                    ${card.notes ? `<button class="action-btn view-notes" data-notes="${card.notes.replace(/"/g, '&quot;')}" title="عرض الملاحظات"><i class="fas fa-sticky-note"></i></button>` : ''}
                </td>
            `;
            table.appendChild(row);

            // إضافة مستمعي الأحداث للأزرار
            row.querySelector('.edit').addEventListener('click', () => editGasCard(card.id));
            row.querySelector('.delete').addEventListener('click', () => deleteGasCard(card.id));

            // إضافة مستمع لزر عرض الملاحظات إذا كان موجوداً
            const viewNotesBtn = row.querySelector('.view-notes');
            if (viewNotesBtn) {
                viewNotesBtn.addEventListener('click', () => {
                    const notes = viewNotesBtn.getAttribute('data-notes');
                    showNotesModal('ملاحظات البطاقة', notes);
                });
            }
        }
    });
}

// تحديث جدول المواعيد
function updateAppointmentsTable(date) {
    const table = document.getElementById('appointments-table').querySelector('tbody');
    table.innerHTML = '';

    let filteredAppointments = appData.appointments;

    // إذا تم تحديد تاريخ، قم بتصفية المواعيد
    if (date) {
        filteredAppointments = appData.appointments.filter(app => app.date === date);
        document.getElementById('appointments-list-title').textContent = `مواعيد ${formatDate(date)}`;
    } else {
        // عرض مواعيد اليوم والمستقبلية
        const today = new Date().toISOString().split('T')[0];
        filteredAppointments = appData.appointments.filter(app => app.date >= today);
        document.getElementById('appointments-list-title').textContent = 'المواعيد القادمة';
    }

    filteredAppointments.sort((a, b) => {
        if (a.date === b.date) {
            return a.time.localeCompare(b.time);
        }
        return a.date.localeCompare(b.date);
    }).forEach(appointment => {
        const vehicle = appData.vehicles.find(v => v.id === appointment.vehicleId);
        const customer = appData.customers.find(c => c.id === appointment.customerId);

        if (vehicle && customer) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${formatDate(appointment.date)}</td>
                <td>${appointment.time}</td>
                <td>${customer.name}</td>
                <td>${vehicle.plateNumber}</td>
                <td>${appointment.service}</td>
                <td>${appointment.notes || '-'}</td>
                <td class="action-cell">
                    <button class="action-btn edit" data-id="${appointment.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                    <button class="action-btn delete" data-id="${appointment.id}" title="حذف"><i class="fas fa-trash"></i></button>
                </td>
            `;
            table.appendChild(row);

            // إضافة مستمعي الأحداث للأزرار
            row.querySelector('.edit').addEventListener('click', () => editAppointment(appointment.id));
            row.querySelector('.delete').addEventListener('click', () => deleteAppointment(appointment.id));
        }
    });
}

// تحديث جدول الزبائن
function updateCustomersTable() {
    const table = document.getElementById('customers-table').querySelector('tbody');
    table.innerHTML = '';

    appData.customers.forEach(customer => {
        const customerVehicles = appData.vehicles.filter(v => v.customerId === customer.id);

        // عرض تاريخ آخر زيارة
        let lastVisit = '-';
        if (customer.lastVisitDate) {
            lastVisit = formatDate(customer.lastVisitDate);
        }

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${customer.name}</td>
            <td>${customer.phone}</td>
            <td>${customerVehicles.length}</td>
            <td>${lastVisit}</td>
            <td class="action-cell">
                <button class="action-btn view" data-id="${customer.id}" title="عرض"><i class="fas fa-eye"></i></button>
                <button class="action-btn edit" data-id="${customer.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete" data-id="${customer.id}" title="حذف"><i class="fas fa-trash"></i></button>
            </td>
        `;
        table.appendChild(row);

        // إضافة مستمعي الأحداث للأزرار
        row.querySelector('.view').addEventListener('click', () => showCustomerInfo(customer.id));
        row.querySelector('.edit').addEventListener('click', () => editCustomer(customer.id));
        row.querySelector('.delete').addEventListener('click', () => deleteCustomer(customer.id));
    });
}

// عرض معلومات الزبون
function showCustomerInfo(customerId) {
    const customer = appData.customers.find(c => c.id === customerId);
    if (!customer) return;

    // تعبئة معلومات الزبون
    document.getElementById('info-customer-name').textContent = customer.name;
    document.getElementById('info-customer-phone').textContent = customer.phone || '-';
    document.getElementById('info-customer-address').textContent = customer.address || '-';

    // إضافة تاريخ آخر زيارة
    let lastVisitText = '-';
    if (customer.lastVisitDate) {
        lastVisitText = formatDate(customer.lastVisitDate);
    }

    // التحقق من وجود عنصر تاريخ آخر زيارة
    let lastVisitEl = document.getElementById('info-customer-last-visit');
    if (!lastVisitEl) {
        // إنشاء عنصر جديد لتاريخ آخر زيارة
        const detailsDiv = document.querySelector('.customer-info-details');
        const lastVisitP = document.createElement('p');
        lastVisitP.innerHTML = `<i class="fas fa-calendar-check"></i> <span>آخر زيارة: </span><span id="info-customer-last-visit" class="last-visit-date">${lastVisitText}</span>`;
        detailsDiv.appendChild(lastVisitP);
    } else {
        // تحديث العنصر الموجود
        lastVisitEl.textContent = lastVisitText;
    }

    // تعبئة جدول السيارات
    const vehiclesTable = document.getElementById('info-vehicles-table').querySelector('tbody');
    vehiclesTable.innerHTML = '';

    const customerVehicles = appData.vehicles.filter(v => v.customerId === customerId);
    if (customerVehicles.length === 0) {
        vehiclesTable.innerHTML = '<tr><td colspan="4" class="text-center">لا توجد سيارات مسجلة</td></tr>';
    } else {
        customerVehicles.forEach(vehicle => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${vehicle.plateNumber}</td>
                <td>${vehicle.make || '-'}</td>
                <td>${vehicle.model || '-'}</td>
                <td>${vehicle.year || '-'}</td>
            `;
            vehiclesTable.appendChild(row);
        });
    }

    // تعبئة جدول بطاقات الغاز
    const cardsTable = document.getElementById('info-cards-table').querySelector('tbody');
    cardsTable.innerHTML = '';

    const customerCards = appData.gasCards.filter(c => c.customerId === customerId);
    if (customerCards.length === 0) {
        cardsTable.innerHTML = '<tr><td colspan="5" class="text-center">لا توجد بطاقات غاز مسجلة</td></tr>';
    } else {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        customerCards.forEach(card => {
            const vehicle = appData.vehicles.find(v => v.id === card.vehicleId);
            const expiryDate = new Date(card.expiryDate);

            // تحديد حالة البطاقة
            let status = '';
            let statusClass = '';

            if (expiryDate < today) {
                status = 'منتهية';
                statusClass = 'text-danger';
            } else if (expiryDate <= new Date(today.getTime() + appData.settings.reminderDays * 24 * 60 * 60 * 1000)) {
                status = 'قريبة الانتهاء';
                statusClass = 'text-warning';
            } else {
                status = 'سارية';
                statusClass = 'text-success';
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${card.cardNumber}</td>
                <td>${vehicle ? vehicle.plateNumber : '-'}</td>
                <td>${formatDate(card.issueDate)}</td>
                <td>${formatDate(card.expiryDate)}</td>
                <td class="${statusClass}">${status}</td>
            `;
            cardsTable.appendChild(row);
        });
    }

    // تعبئة جدول المواعيد
    const appointmentsTable = document.getElementById('info-appointments-table').querySelector('tbody');
    appointmentsTable.innerHTML = '';

    const customerAppointments = appData.appointments.filter(a => a.customerId === customerId);
    if (customerAppointments.length === 0) {
        appointmentsTable.innerHTML = '<tr><td colspan="5" class="text-center">لا توجد مواعيد مسجلة</td></tr>';
    } else {
        customerAppointments.sort((a, b) => new Date(b.date + 'T' + b.time) - new Date(a.date + 'T' + a.time)).forEach(appointment => {
            const vehicle = appData.vehicles.find(v => v.id === appointment.vehicleId);
            const appointmentDate = new Date(appointment.date + 'T' + appointment.time);

            // تحديد حالة الموعد
            let status = '';
            let statusClass = '';

            if (appointmentDate < today) {
                status = 'منتهي';
                statusClass = 'text-danger';
            } else if (appointment.date === today.toISOString().split('T')[0]) {
                status = 'اليوم';
                statusClass = 'text-warning';
            } else {
                status = 'قادم';
                statusClass = 'text-success';
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${formatDate(appointment.date)}</td>
                <td>${appointment.time}</td>
                <td>${appointment.service}</td>
                <td>${vehicle ? vehicle.plateNumber : '-'}</td>
                <td class="${statusClass}">${status}</td>
            `;
            appointmentsTable.appendChild(row);
        });
    }

    // تعبئة جدول الديون
    const debtsTable = document.getElementById('info-debts-table').querySelector('tbody');
    debtsTable.innerHTML = '';

    const customerDebts = appData.debts.filter(d => d.customerId === customerId);
    if (customerDebts.length === 0) {
        debtsTable.innerHTML = '<tr><td colspan="4" class="text-center">لا توجد ديون مسجلة</td></tr>';
    } else {
        customerDebts.sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate)).forEach(debt => {
            // حساب المبلغ المتبقي
            const payments = appData.debtPayments.filter(payment => payment.debtId === debt.id);
            const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
            const remainingAmount = debt.amount - totalPaid;

            // تحديد حالة الدين
            let status = '';
            let statusClass = '';

            if (debt.isPaid) {
                status = 'مسدد';
                statusClass = 'text-success';
            } else {
                const dueDate = new Date(debt.dueDate);
                dueDate.setHours(0, 0, 0, 0);
                const daysLeft = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

                if (daysLeft < 0) {
                    status = 'متأخر';
                    statusClass = 'text-danger';
                } else if (daysLeft <= appData.settings.debtReminderDays) {
                    status = 'مستحق قريباً';
                    statusClass = 'text-warning';
                } else {
                    status = 'نشط';
                }
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${debt.amount.toFixed(2)}</td>
                <td>${debt.isPaid ? '0.00' : remainingAmount.toFixed(2)}</td>
                <td>${formatDate(debt.dueDate)}</td>
                <td class="${statusClass}">${status}</td>
            `;
            debtsTable.appendChild(row);
        });
    }

    // إضافة مستمعي الأحداث للأزرار
    document.getElementById('edit-customer-info').addEventListener('click', () => {
        closeModal('customer-info-modal');
        editCustomer(customerId);
    });

    document.getElementById('print-customer-info').addEventListener('click', () => {
        printCustomerInfo(customerId);
    });

    document.getElementById('send-sms-customer').addEventListener('click', () => {
        sendSMSToCustomer(customerId);
    });

    document.getElementById('close-customer-info').addEventListener('click', () => {
        closeModal('customer-info-modal');
    });

    // إعداد التبويبات
    setupCustomerInfoTabs();

    // إضافة معرف الزبون إلى حاوية المعلومات
    document.querySelector('.customer-info-container').setAttribute('data-id', customerId);

    // عرض النافذة
    openModal('customer-info-modal');
}

// إعداد تبويبات معلومات الزبون
function setupCustomerInfoTabs() {
    const tabButtons = document.querySelectorAll('#customer-info-modal .tab-buttons .tab-btn');
    const tabContents = document.querySelectorAll('#customer-info-modal .tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            // إزالة الفئة النشطة من جميع الأزرار
            tabButtons.forEach(btn => btn.classList.remove('active'));
            // إضافة الفئة النشطة للزر المضغوط
            button.classList.add('active');

            // إخفاء جميع المحتويات
            tabContents.forEach(content => content.classList.remove('active'));
            // إظهار المحتوى المطلوب
            const tabId = button.getAttribute('data-tab') + '-tab';
            document.getElementById(tabId).classList.add('active');
        });
    });
}

// تصدير التقرير بصيغة PDF
function exportToPDF(reportTitle, contentElement) {
    // تهيئة jsPDF
    const { jsPDF } = window.jspdf;

    // إنشاء مستند PDF جديد
    const doc = new jsPDF({
        orientation: 'p',
        unit: 'mm',
        format: 'a4',
        putOnlyUsedFonts: true
    });

    // إضافة دعم للغة العربية
    doc.setFont('Helvetica', 'normal');
    doc.setR2L(true);

    // استخدام html2canvas لتحويل المحتوى إلى صورة
    html2canvas(contentElement, {
        scale: 2,
        useCORS: true,
        logging: false
    }).then(canvas => {
        // تحويل الصورة إلى صيغة يمكن استخدامها في PDF
        const imgData = canvas.toDataURL('image/png');

        // حساب نسبة العرض إلى الارتفاع
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const imgWidth = canvas.width;
        const imgHeight = canvas.height;
        const ratio = Math.min(pageWidth / imgWidth, pageHeight / imgHeight);

        // إضافة الصورة إلى المستند
        doc.addImage(imgData, 'PNG', 0, 0, imgWidth * ratio, imgHeight * ratio);

        // حفظ المستند
        doc.save(`${reportTitle}.pdf`);

        // عرض رسالة نجاح
        showToast(`تم تصدير ${reportTitle} بنجاح بصيغة PDF`);
    });
}

// طباعة معلومات الزبون
function printCustomerInfo(customerId) {
    const customer = appData.customers.find(c => c.id === customerId);
    if (!customer) return;

    // إنشاء نافذة الطباعة
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // إنشاء محتوى التقرير
    let reportContent = `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <title>بطاقة الزبون - ${customer.name}</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 20px;
                    direction: rtl;
                }
                h1, h2, h3 {
                    text-align: center;
                }
                .customer-header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 1px solid #ddd;
                }
                .customer-details {
                    margin-bottom: 20px;
                }
                .customer-details p {
                    margin: 5px 0;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                }
                th {
                    background-color: #f2f2f2;
                }
                .section {
                    margin-bottom: 20px;
                }
                .text-danger {
                    color: #e74c3c;
                }
                .text-warning {
                    color: #f39c12;
                }
                .text-success {
                    color: #2ecc71;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 0.8em;
                    color: #666;
                }
                .no-print {
                    display: block;
                }
                @media print {
                    .no-print {
                        display: none;
                    }
                    button {
                        display: none;
                    }
                }
            </style>
        </head>
        <body>
            <div class="no-print" style="text-align: center; margin-bottom: 20px;">
                <button onclick="window.print();" style="padding: 10px 20px; cursor: pointer;">طباعة البطاقة</button>
            </div>

            <h1>بطاقة الزبون</h1>

            <div class="customer-header">
                <div>
                    <strong>اسم المحل:</strong> ${appData.settings.shopName}
                </div>
                <div>
                    <strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-EG', { numberingSystem: 'latn' })}
                </div>
            </div>

            <div class="customer-details">
                <h2>${customer.name}</h2>
                <p><strong>رقم الهاتف:</strong> ${customer.phone || '-'}</p>
                <p><strong>البريد الإلكتروني:</strong> ${customer.email || '-'}</p>
                <p><strong>العنوان:</strong> ${customer.address || '-'}</p>
                <p><strong>تاريخ التسجيل:</strong> ${formatDate(customer.createdAt)}</p>
                <p><strong>ملاحظات:</strong> ${customer.notes || '-'}</p>
            </div>

            <div class="section">
                <h3>سيارات الزبون</h3>
                <table>
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>رقم اللوحة</th>
                            <th>النوع</th>
                            <th>الموديل</th>
                            <th>سنة الصنع</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    // إضافة بيانات السيارات
    const customerVehicles = appData.vehicles.filter(v => v.customerId === customerId);
    if (customerVehicles.length === 0) {
        reportContent += `
            <tr>
                <td colspan="5" style="text-align: center;">لا توجد سيارات مسجلة</td>
            </tr>
        `;
    } else {
        customerVehicles.forEach((vehicle, index) => {
            reportContent += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${vehicle.plateNumber}</td>
                    <td>${vehicle.make || '-'}</td>
                    <td>${vehicle.model || '-'}</td>
                    <td>${vehicle.year || '-'}</td>
                </tr>
            `;
        });
    }

    reportContent += `
                </tbody>
            </table>
            </div>

            <div class="section">
                <h3>بطاقات الغاز</h3>
                <table>
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>رقم البطاقة</th>
                            <th>رقم السيارة</th>
                            <th>تاريخ الإصدار</th>
                            <th>تاريخ الانتهاء</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    // إضافة بيانات بطاقات الغاز
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const customerCards = appData.gasCards.filter(c => c.customerId === customerId);

    if (customerCards.length === 0) {
        reportContent += `
            <tr>
                <td colspan="6" style="text-align: center;">لا توجد بطاقات غاز مسجلة</td>
            </tr>
        `;
    } else {
        customerCards.forEach((card, index) => {
            const vehicle = appData.vehicles.find(v => v.id === card.vehicleId);
            const expiryDate = new Date(card.expiryDate);

            // تحديد حالة البطاقة
            let status = '';
            let statusClass = '';

            if (expiryDate < today) {
                status = 'منتهية';
                statusClass = 'text-danger';
            } else if (expiryDate <= new Date(today.getTime() + appData.settings.reminderDays * 24 * 60 * 60 * 1000)) {
                status = 'قريبة الانتهاء';
                statusClass = 'text-warning';
            } else {
                status = 'سارية';
                statusClass = 'text-success';
            }

            reportContent += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${card.cardNumber}</td>
                    <td>${vehicle ? vehicle.plateNumber : '-'}</td>
                    <td>${formatDate(card.issueDate)}</td>
                    <td>${formatDate(card.expiryDate)}</td>
                    <td class="${statusClass}">${status}</td>
                </tr>
            `;
        });
    }

    reportContent += `
                </tbody>
            </table>
            </div>

            <div class="section">
                <h3>الديون</h3>
                <table>
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>المبلغ الأصلي</th>
                            <th>المبلغ المتبقي</th>
                            <th>تاريخ الإنشاء</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    // إضافة بيانات الديون
    const customerDebts = appData.debts.filter(d => d.customerId === customerId);

    if (customerDebts.length === 0) {
        reportContent += `
            <tr>
                <td colspan="6" style="text-align: center;">لا توجد ديون مسجلة</td>
            </tr>
        `;
    } else {
        customerDebts.forEach((debt, index) => {
            // حساب المبلغ المتبقي
            const payments = appData.debtPayments.filter(payment => payment.debtId === debt.id);
            const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
            const remainingAmount = debt.amount - totalPaid;

            // تحديد حالة الدين
            let status = '';
            let statusClass = '';

            if (debt.isPaid) {
                status = 'مسدد';
                statusClass = 'text-success';
            } else {
                const dueDate = new Date(debt.dueDate);
                dueDate.setHours(0, 0, 0, 0);
                const daysLeft = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

                if (daysLeft < 0) {
                    status = 'متأخر';
                    statusClass = 'text-danger';
                } else if (daysLeft <= appData.settings.debtReminderDays) {
                    status = 'مستحق قريباً';
                    statusClass = 'text-warning';
                } else {
                    status = 'نشط';
                }
            }

            reportContent += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${debt.amount.toFixed(2)}</td>
                    <td>${debt.isPaid ? '0.00' : remainingAmount.toFixed(2)}</td>
                    <td>${formatDate(debt.createdAt)}</td>
                    <td>${formatDate(debt.dueDate)}</td>
                    <td class="${statusClass}">${status}</td>
                </tr>
            `;
        });
    }

    reportContent += `
                </tbody>
            </table>
            </div>

            <div class="footer">
                تم إنشاء هذا التقرير بواسطة نظام إدارة محل تركيب وتصليح الغاز للسيارات<br>
                ${new Date().toLocaleString('ar-EG', { numberingSystem: 'latn' })}
            </div>
        </body>
        </html>
    `;

    // كتابة المحتوى في نافذة الطباعة
    printWindow.document.open();
    printWindow.document.write(reportContent);
    printWindow.document.close();
}

// إرسال رسالة نصية للزبون
function sendSMSToCustomer(customerId) {
    const customer = appData.customers.find(c => c.id === customerId);
    if (!customer || !customer.phone) {
        showToast('لا يمكن إرسال رسالة، رقم الهاتف غير متوفر', false);
        return;
    }

    // فتح تطبيق الرسائل النصية مع رقم الزبون
    const phoneNumber = customer.phone.replace(/\s/g, '');
    window.open(`sms:${phoneNumber}`, '_blank');
}

// المتغيرات العالمية للتقويم
let currentCalendarMonth = new Date().getMonth();
let currentCalendarYear = new Date().getFullYear();

// تحديث التقويم
function updateCalendar(month = null, year = null) {
    if (month !== null) currentCalendarMonth = month;
    if (year !== null) currentCalendarYear = year;

    const calendarEl = document.getElementById('appointments-calendar');
    calendarEl.innerHTML = '';

    const today = new Date();

    // تحديث عنوان الشهر
    const monthNames = ['جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي', 'جوان', 'جويلية', 'أوت', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
    document.getElementById('current-month').textContent = `${monthNames[currentCalendarMonth]} ${currentCalendarYear}`;

    // إضافة أيام الأسبوع
    const weekDays = ['الأحد', 'الإثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
    weekDays.forEach(day => {
        const dayHeader = document.createElement('div');
        dayHeader.className = 'calendar-day-header';
        dayHeader.textContent = day;
        calendarEl.appendChild(dayHeader);
    });

    // الحصول على اليوم الأول من الشهر
    const firstDay = new Date(currentCalendarYear, currentCalendarMonth, 1);
    const startingDay = firstDay.getDay();

    // الحصول على عدد أيام الشهر
    const lastDay = new Date(currentCalendarYear, currentCalendarMonth + 1, 0);
    const monthDays = lastDay.getDate();

    // إضافة الخلايا الفارغة قبل اليوم الأول
    for (let i = 0; i < startingDay; i++) {
        const emptyDay = document.createElement('div');
        emptyDay.className = 'calendar-day empty';
        calendarEl.appendChild(emptyDay);
    }

    // إضافة أيام الشهر
    for (let i = 1; i <= monthDays; i++) {
        const day = document.createElement('div');
        day.className = 'calendar-day';

        const dateStr = `${currentCalendarYear}-${(currentCalendarMonth + 1).toString().padStart(2, '0')}-${i.toString().padStart(2, '0')}`;

        // التحقق مما إذا كان هذا اليوم هو اليوم الحالي
        if (i === today.getDate() && currentCalendarMonth === today.getMonth() && currentCalendarYear === today.getFullYear()) {
            day.classList.add('today');
        }

        // التحقق مما إذا كان هذا اليوم في الماضي
        const currentDate = new Date(dateStr);
        if (currentDate < new Date(today.setHours(0, 0, 0, 0))) {
            day.classList.add('past-day');
        }

        // التحقق مما إذا كان هناك مواعيد في هذا اليوم
        const dayAppointments = appData.appointments.filter(app => app.date === dateStr);
        if (dayAppointments.length > 0) {
            day.classList.add('has-appointments');

            // إضافة نقاط للمواعيد
            const dotsContainer = document.createElement('div');
            dotsContainer.className = 'appointment-dots';
            for (let j = 0; j < Math.min(dayAppointments.length, 3); j++) {
                const dot = document.createElement('span');
                dot.className = 'appointment-dot';
                dotsContainer.appendChild(dot);
            }

            day.innerHTML = `
                <div class="day-number">${i}</div>
                <div class="appointment-count">${dayAppointments.length} موعد</div>
            `;
            day.appendChild(dotsContainer);
        } else {
            day.innerHTML = `<div class="day-number">${i}</div>`;
        }

        // إضافة مستمع الحدث للنقر على اليوم
        day.addEventListener('click', () => {
            // تحديد جميع الأيام كغير محددة
            document.querySelectorAll('.calendar-day').forEach(d => {
                d.classList.remove('selected-day');
            });

            // تحديد اليوم المحدد
            day.classList.add('selected-day');

            // تحديث جدول المواعيد
            updateAppointmentsTable(dateStr);
        });

        calendarEl.appendChild(day);
    }

    // إذا كان الشهر الحالي، قم بتحديد اليوم الحالي
    if (currentCalendarMonth === today.getMonth() && currentCalendarYear === today.getFullYear()) {
        const todayStr = today.toISOString().split('T')[0];
        updateAppointmentsTable(todayStr);
    } else {
        // عرض جميع مواعيد الشهر
        const monthStr = `${currentCalendarYear}-${(currentCalendarMonth + 1).toString().padStart(2, '0')}`;
        const monthAppointments = appData.appointments.filter(app => app.date.startsWith(monthStr));

        if (monthAppointments.length > 0) {
            // عرض مواعيد أول يوم به مواعيد
            monthAppointments.sort((a, b) => a.date.localeCompare(b.date));
            updateAppointmentsTable(monthAppointments[0].date);

            // تحديد اليوم في التقويم
            const dayNumber = parseInt(monthAppointments[0].date.split('-')[2]);
            const calendarDays = document.querySelectorAll('.calendar-day:not(.empty)');
            if (calendarDays[dayNumber - 1]) {
                calendarDays[dayNumber - 1].classList.add('selected-day');
            }
        } else {
            // عرض رسالة لا توجد مواعيد
            document.getElementById('appointments-list-title').textContent = `مواعيد شهر ${monthNames[currentCalendarMonth]}`;
            const table = document.getElementById('appointments-table').querySelector('tbody');
            table.innerHTML = '<tr><td colspan="7" style="text-align: center;">لا توجد مواعيد لهذا الشهر</td></tr>';
        }
    }
}

// تنسيق التاريخ
function formatDate(dateStr) {
    const date = new Date(dateStr);
    return date.toLocaleDateString('ar-EG', { year: 'numeric', month: 'long', day: 'numeric', numberingSystem: 'latn' });
}

// إنشاء معرف فريد
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// دالة إظهار الإشعارات المحسنة
function showToast(message, success = true, type = null) {
    // إزالة الإشعارات السابقة
    const existingToasts = document.querySelectorAll('.toast');
    existingToasts.forEach(toast => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    });

    const toast = document.createElement('div');

    // تحديد نوع الإشعار
    let toastType = 'info';
    if (type) {
        toastType = type;
    } else if (success === true) {
        toastType = 'success';
    } else if (success === false) {
        toastType = 'error';
    }

    toast.className = `toast ${toastType}`;

    // إضافة المحتوى
    const messageSpan = document.createElement('span');
    messageSpan.textContent = message;
    toast.appendChild(messageSpan);

    // إضافة زر الإغلاق
    const closeBtn = document.createElement('button');
    closeBtn.innerHTML = '×';
    closeBtn.style.cssText = `
        background: none;
        border: none;
        color: white;
        font-size: 20px;
        cursor: pointer;
        margin-left: 10px;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0.8;
        transition: opacity 0.3s ease;
    `;
    closeBtn.onmouseover = () => closeBtn.style.opacity = '1';
    closeBtn.onmouseout = () => closeBtn.style.opacity = '0.8';
    closeBtn.onclick = () => hideToast(toast);
    toast.appendChild(closeBtn);

    document.body.appendChild(toast);

    // إظهار الإشعار مع تأثير
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    // إخفاء الإشعار تلقائياً بعد 5 ثوان
    setTimeout(() => {
        hideToast(toast);
    }, 5000);
}

// دالة إخفاء الإشعار
function hideToast(toast) {
    if (toast && toast.parentNode) {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 400);
    }
}

// دوال مساعدة لأنواع مختلفة من الإشعارات
function showSuccessToast(message) {
    showToast(message, true, 'success');
}

function showErrorToast(message) {
    showToast(message, false, 'error');
}

function showInfoToast(message) {
    showToast(message, null, 'info');
}

function showWarningToast(message) {
    showToast(message, null, 'warning');
}

// ===============================
// دوال التحسينات المتقدمة
// ===============================

// دالة مساعدة لتحديث النصوص مع تأثيرات
function updateElementText(elementId, value, animate = true) {
    const element = document.getElementById(elementId);
    if (!element) return;

    if (animate && element.textContent !== value.toString()) {
        element.style.transform = 'scale(1.2)';
        element.style.color = '#007bff';

        setTimeout(() => {
            element.textContent = value;
            element.style.transform = 'scale(1)';
            element.style.color = '';
        }, 200);
    } else {
        element.textContent = value;
    }
}

// تحديث الإحصائيات المتقدمة
function updateAdvancedAnalytics() {
    const today = new Date();
    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);

    // إحصائيات المبيعات
    const todaySales = appData.sales ? appData.sales.filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate.toDateString() === today.toDateString();
    }) : [];

    const monthSales = appData.sales ? appData.sales.filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate >= thisMonth;
    }) : [];

    const todaySalesAmount = todaySales.reduce((sum, sale) => sum + (sale.total || 0), 0);
    const monthSalesAmount = monthSales.reduce((sum, sale) => sum + (sale.total || 0), 0);

    updateElementText('today-sales', formatCurrency(todaySalesAmount));
    updateElementText('month-sales', formatCurrency(monthSalesAmount));

    // إحصائيات المشتريات
    const todayPurchases = appData.purchases ? appData.purchases.filter(purchase => {
        const purchaseDate = new Date(purchase.date);
        return purchaseDate.toDateString() === today.toDateString();
    }) : [];

    const monthPurchases = appData.purchases ? appData.purchases.filter(purchase => {
        const purchaseDate = new Date(purchase.date);
        return purchaseDate >= thisMonth;
    }) : [];

    const todayPurchasesAmount = todayPurchases.reduce((sum, purchase) => sum + (purchase.total || 0), 0);
    const monthPurchasesAmount = monthPurchases.reduce((sum, purchase) => sum + (purchase.total || 0), 0);

    updateElementText('today-purchases', formatCurrency(todayPurchasesAmount));
    updateElementText('month-purchases', formatCurrency(monthPurchasesAmount));

    // إحصائيات المخزون
    const totalItems = appData.inventory ? appData.inventory.length : 0;
    const lowStockItems = appData.inventory ? appData.inventory.filter(item =>
        item.quantity <= (item.minQuantity || 5)
    ).length : 0;

    updateElementText('total-items', totalItems);
    updateElementText('low-stock-items', lowStockItems);
}

// تحديث إحصائيات الإيرادات
function updateRevenueStats() {
    const today = new Date();
    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    // حساب الإيرادات الشهرية
    const thisMonthRevenue = appData.sales ? appData.sales.filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate >= thisMonth && saleDate <= thisMonthEnd;
    }).reduce((sum, sale) => sum + (sale.total || 0), 0) : 0;

    const lastMonthRevenue = appData.sales ? appData.sales.filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate >= lastMonth && saleDate < thisMonth;
    }).reduce((sum, sale) => sum + (sale.total || 0), 0) : 0;

    // حساب نسبة النمو
    const growthRate = lastMonthRevenue > 0 ?
        ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue * 100).toFixed(1) : 0;

    // تحديث العناصر إذا كانت موجودة
    updateElementText('this-month-revenue', formatCurrency(thisMonthRevenue));
    updateElementText('last-month-revenue', formatCurrency(lastMonthRevenue));
    updateElementText('revenue-growth', `${growthRate}%`);
}

// تحديث رؤى الزبائن
function updateCustomerInsights() {
    const today = new Date();
    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // أفضل الزبائن (حسب عدد الزيارات أو المعاملات)
    const customerVisits = {};
    appData.customers.forEach(customer => {
        // يمكن حساب عدد الزيارات أو المعاملات هنا
        customerVisits[customer.id] = customer.visitCount || 0;
    });

    const topCustomers = Object.entries(customerVisits)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([customerId, count]) => {
            const customer = appData.customers.find(c => c.id === customerId);
            return { customer, visitCount: count };
        });

    // الزبائن النشطون هذا الشهر
    const activeCustomersThisMonth = appData.customers.filter(customer => {
        const customerDate = new Date(customer.createdAt);
        return customerDate >= thisMonth;
    }).length;

    updateElementText('active-customers-month', activeCustomersThisMonth);
    updateElementText('top-customers-count', topCustomers.length);
}

// دالة مساعدة لتنسيق العملة (محسنة)
function formatCurrency(amount) {
    if (typeof amount !== 'number') amount = 0;
    return new Intl.NumberFormat('ar-DZ', {
        style: 'currency',
        currency: 'DZD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(amount).replace('DZD', 'د.ج');
}

// ===============================
// نظام التنبيهات الذكي
// ===============================



// فحص المواعيد القادمة
function checkUpcomingAppointments() {
    const today = new Date();
    const tomorrow = new Date();
    tomorrow.setDate(today.getDate() + 1);

    const todayStr = today.toISOString().split('T')[0];
    const tomorrowStr = tomorrow.toISOString().split('T')[0];

    // مواعيد اليوم
    const todayAppointments = appData.appointments.filter(app => app.date === todayStr);

    // مواعيد الغد
    const tomorrowAppointments = appData.appointments.filter(app => app.date === tomorrowStr);

    // تنبيهات مواعيد اليوم
    if (todayAppointments.length > 0) {
        addNotification(
            '📅 مواعيد اليوم',
            `لديك ${todayAppointments.length} موعد اليوم`,
            'info'
        );
    }

    // تنبيهات مواعيد الغد
    if (tomorrowAppointments.length > 0) {
        addNotification(
            '📅 مواعيد الغد',
            `لديك ${tomorrowAppointments.length} موعد غداً`,
            'info'
        );
    }

    return {
        today: todayAppointments.length,
        tomorrow: tomorrowAppointments.length
    };
}

// فحص الديون المستحقة
function checkOverdueDebts() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const overdueDebts = appData.debts.filter(debt => {
        if (debt.isPaid) return false;
        const dueDate = new Date(debt.dueDate);
        dueDate.setHours(0, 0, 0, 0);
        return dueDate < today;
    });

    // تنبيهات الديون المتأخرة
    overdueDebts.forEach(debt => {
        const customer = appData.customers.find(c => c.id === debt.customerId);
        const daysPastDue = Math.ceil((today - new Date(debt.dueDate)) / (1000 * 60 * 60 * 24));

        if (customer) {
            addNotification(
                '💰 دين متأخر',
                `دين ${customer.name} بمبلغ ${formatCurrency(debt.amount)} متأخر ${daysPastDue} يوم`,
                'error'
            );
        }
    });

    return overdueDebts.length;
}

// فحص المخزون المنخفض
function checkLowStock() {
    const lowStockItems = appData.inventory.filter(item =>
        item.quantity <= (item.minQuantity || 5)
    );

    // تنبيهات المخزون المنخفض
    lowStockItems.forEach(item => {
        addNotification(
            '📦 مخزون منخفض',
            `الصنف "${item.name}" وصل للحد الأدنى (${item.quantity} متبقي)`,
            'warning'
        );
    });

    return lowStockItems.length;
}

// تشغيل جميع الفحوصات
function runSmartAlerts() {
    console.log('🔍 تشغيل نظام التنبيهات الذكي...');

    const appointmentsStatus = checkUpcomingAppointments();
    const overdueDebtsCount = checkOverdueDebts();
    const lowStockCount = checkLowStock();

    // إضافة تنبيه ملخص إذا كان هناك مشاكل
    const totalIssues = overdueDebtsCount + lowStockCount;

    if (totalIssues > 0) {
        addNotification(
            '📊 ملخص التنبيهات',
            `لديك ${totalIssues} تنبيه يحتاج للمتابعة`,
            'info'
        );
    }

    console.log('✅ تم تشغيل نظام التنبيهات الذكي');

    return {
        appointments: appointmentsStatus,
        overdueDebts: overdueDebtsCount,
        lowStock: lowStockCount,
        totalIssues
    };
}



// تشغيل التنبيهات الذكية كل 30 دقيقة
function startSmartAlertsScheduler() {
    // تشغيل فوري
    runSmartAlerts();

    // تشغيل كل 30 دقيقة
    setInterval(() => {
        runSmartAlerts();
    }, 30 * 60 * 1000); // 30 دقيقة

    console.log('🔔 تم تشغيل جدولة التنبيهات الذكية (كل 30 دقيقة)');
}

// ===============================
// نظام الوضع المظلم المحسن
// ===============================

// إعداد نظام الوضع المظلم
function setupDarkMode() {
    console.log('🌙 بدء إعداد نظام الوضع المظلم...');

    const darkModeToggle = document.getElementById('dark-mode-toggle');
    const body = document.body;

    if (!darkModeToggle) {
        console.error('❌ زر الوضع المظلم غير موجود في DOM');
        // محاولة البحث عن الزر مرة أخرى بعد تأخير
        setTimeout(() => {
            const retryToggle = document.getElementById('dark-mode-toggle');
            if (retryToggle) {
                console.log('✅ تم العثور على زر الوضع المظلم بعد إعادة المحاولة');
                initializeDarkModeToggle(retryToggle);
            } else {
                console.error('❌ فشل في العثور على زر الوضع المظلم نهائياً');
            }
        }, 1000);
        return;
    }

    initializeDarkModeToggle(darkModeToggle);
}

// تهيئة زر الوضع المظلم
function initializeDarkModeToggle(darkModeToggle) {
    const body = document.body;

    console.log('🔧 تهيئة زر الوضع المظلم...');

    // تحميل الإعداد المحفوظ
    const savedTheme = localStorage.getItem('theme');
    console.log('💾 الثيم المحفوظ:', savedTheme);

    if (savedTheme === 'dark') {
        enableDarkMode();
    } else {
        enableLightMode();
    }

    // إزالة أي مستمعين سابقين لتجنب التكرار
    const newToggle = darkModeToggle.cloneNode(true);
    darkModeToggle.parentNode.replaceChild(newToggle, darkModeToggle);

    // ربط حدث النقر الجديد
    newToggle.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        console.log('🖱️ تم النقر على زر الوضع المظلم');

        if (body.classList.contains('dark-mode')) {
            console.log('🌞 التبديل إلى الوضع الفاتح');
            enableLightMode();
        } else {
            console.log('🌙 التبديل إلى الوضع المظلم');
            enableDarkMode();
        }
    });

    console.log('✅ تم إعداد نظام الوضع المظلم بنجاح');
}

// تفعيل الوضع المظلم
function enableDarkMode() {
    console.log('🌙 تفعيل الوضع المظلم...');

    const body = document.body;
    const darkModeToggle = document.getElementById('dark-mode-toggle');

    // إضافة كلاس الوضع المظلم
    body.classList.add('dark-mode');
    console.log('✅ تم إضافة كلاس dark-mode إلى body');

    // تحديث أيقونة الزر
    if (darkModeToggle) {
        const icon = darkModeToggle.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-sun';
            console.log('☀️ تم تغيير الأيقونة إلى الشمس');
        }
        darkModeToggle.title = 'تفعيل الوضع الفاتح';
    } else {
        console.warn('⚠️ لم يتم العثور على زر الوضع المظلم لتحديث الأيقونة');
    }

    // حفظ الإعداد
    localStorage.setItem('theme', 'dark');
    console.log('💾 تم حفظ الثيم المظلم في localStorage');

    // تحديث الألوان والأنماط
    updateThemeColors();

    // إضافة تنبيه
    if (typeof addNotification === 'function') {
        addNotification(
            '🌙 تم تفعيل الوضع المظلم',
            'تم تبديل التطبيق إلى الوضع المظلم لراحة العينين',
            'info'
        );
    }

    console.log('✅ تم تفعيل الوضع المظلم بنجاح');
}

// تفعيل الوضع الفاتح
function enableLightMode() {
    console.log('☀️ تفعيل الوضع الفاتح...');

    const body = document.body;
    const darkModeToggle = document.getElementById('dark-mode-toggle');

    // إزالة كلاس الوضع المظلم
    body.classList.remove('dark-mode');
    console.log('✅ تم إزالة كلاس dark-mode من body');

    // تحديث أيقونة الزر
    if (darkModeToggle) {
        const icon = darkModeToggle.querySelector('i');
        if (icon) {
            icon.className = 'fas fa-moon';
            console.log('🌙 تم تغيير الأيقونة إلى القمر');
        }
        darkModeToggle.title = 'تفعيل الوضع المظلم';
    } else {
        console.warn('⚠️ لم يتم العثور على زر الوضع المظلم لتحديث الأيقونة');
    }

    // حفظ الإعداد
    localStorage.setItem('theme', 'light');
    console.log('💾 تم حفظ الثيم الفاتح في localStorage');

    // تحديث الألوان والأنماط
    updateThemeColors();

    // إضافة تنبيه
    if (typeof addNotification === 'function') {
        addNotification(
            '☀️ تم تفعيل الوضع الفاتح',
            'تم تبديل التطبيق إلى الوضع الفاتح',
            'info'
        );
    }

    console.log('✅ تم تفعيل الوضع الفاتح بنجاح');
}

// تبديل الوضع تلقائياً حسب وقت النهار
function autoToggleTheme() {
    const hour = new Date().getHours();
    const isNightTime = hour >= 18 || hour <= 6;

    // فقط إذا لم يكن هناك إعداد محفوظ
    if (!localStorage.getItem('theme')) {
        if (isNightTime) {
            enableDarkMode();
        } else {
            enableLightMode();
        }
    }
}

// تحديث الألوان حسب الوضع
function updateThemeColors() {
    const isDarkMode = document.body.classList.contains('dark-mode');
    console.log('🎨 تحديث ألوان الثيم، الوضع المظلم:', isDarkMode);

    // تحديث ألوان البطاقات
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        if (isDarkMode) {
            card.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.4)';
        } else {
            card.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.2)';
        }
    });

    // تحديث ألوان الجداول
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        if (isDarkMode) {
            table.style.backgroundColor = 'var(--card-bg-color)';
            table.style.color = 'var(--text-color)';
        } else {
            table.style.backgroundColor = '#ffffff';
            table.style.color = '#2c3e50';
        }
    });

    // تحديث ألوان النماذج
    const inputs = document.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        if (isDarkMode) {
            input.style.backgroundColor = 'var(--card-bg-color)';
            input.style.color = 'var(--text-color)';
            input.style.borderColor = 'var(--border-color)';
        } else {
            input.style.backgroundColor = '#ffffff';
            input.style.color = '#2c3e50';
            input.style.borderColor = '#ddd';
        }
    });

    // تحديث ألوان النوافذ المنبثقة
    const modals = document.querySelectorAll('.modal-content');
    modals.forEach(modal => {
        if (isDarkMode) {
            modal.style.backgroundColor = 'var(--card-bg-color)';
            modal.style.color = 'var(--text-color)';
            modal.style.borderColor = 'var(--border-color)';
        } else {
            modal.style.backgroundColor = '#ffffff';
            modal.style.color = '#2c3e50';
            modal.style.borderColor = '#ddd';
        }
    });

    console.log('✅ تم تحديث ألوان الثيم');
}

// مراقبة تغيير الوضع
function observeThemeChanges() {
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                updateThemeColors();
            }
        });
    });

    observer.observe(document.body, {
        attributes: true,
        attributeFilter: ['class']
    });
}

// اختبار إعداد الوضع المظلم
function testDarkModeSetup() {
    console.log('🧪 اختبار إعداد الوضع المظلم...');

    const darkModeToggle = document.getElementById('dark-mode-toggle');
    const body = document.body;

    if (!darkModeToggle) {
        console.error('❌ فشل الاختبار: زر الوضع المظلم غير موجود');
        return false;
    }

    if (!darkModeToggle.onclick && !darkModeToggle.addEventListener) {
        console.error('❌ فشل الاختبار: لا توجد أحداث مربوطة بالزر');
        return false;
    }

    const savedTheme = localStorage.getItem('theme');
    const hasCorrectClass = savedTheme === 'dark' ? body.classList.contains('dark-mode') : !body.classList.contains('dark-mode');

    if (!hasCorrectClass) {
        console.warn('⚠️ تحذير: الثيم المحفوظ لا يتطابق مع كلاس body');
    }

    console.log('✅ اختبار الوضع المظلم مكتمل');
    console.log('📊 تفاصيل الاختبار:', {
        toggleExists: !!darkModeToggle,
        savedTheme: savedTheme,
        bodyHasDarkClass: body.classList.contains('dark-mode'),
        classMatches: hasCorrectClass
    });

    return true;
}

// إضافة دالة لإعادة تعيين الوضع المظلم في حالة وجود مشاكل
function resetDarkMode() {
    console.log('🔄 إعادة تعيين نظام الوضع المظلم...');

    // إزالة الثيم المحفوظ
    localStorage.removeItem('theme');

    // إزالة كلاس الوضع المظلم
    document.body.classList.remove('dark-mode');

    // إعادة إعداد النظام
    setupDarkMode();

    console.log('✅ تم إعادة تعيين نظام الوضع المظلم');
}

// ===============================
// نظام التقارير المتقدم
// ===============================

// إنشاء تقرير شامل للزبائن
function generateCustomersReport() {
    const today = new Date();
    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const reportData = {
        title: 'تقرير الزبائن الشامل',
        generatedAt: today.toLocaleString('ar-SA'),
        totalCustomers: appData.customers.length,
        newCustomersThisMonth: appData.customers.filter(c => new Date(c.createdAt) >= thisMonth).length,
        customersWithVehicles: appData.customers.filter(c =>
            appData.vehicles.some(v => v.customerId === c.id)
        ).length,
        customersWithGasCards: appData.customers.filter(c =>
            appData.gasCards.some(card => card.customerId === c.id)
        ).length,
        customersWithDebts: appData.customers.filter(c =>
            appData.debts.some(d => d.customerId === c.id && !d.isPaid)
        ).length,
        customers: appData.customers.map(customer => {
            const vehicles = appData.vehicles.filter(v => v.customerId === customer.id);
            const gasCards = appData.gasCards.filter(c => c.customerId === customer.id);
            const debts = appData.debts.filter(d => d.customerId === customer.id && !d.isPaid);

            return {
                ...customer,
                vehiclesCount: vehicles.length,
                gasCardsCount: gasCards.length,
                activeDebtsCount: debts.length,
                totalDebtAmount: debts.reduce((sum, d) => sum + d.amount, 0)
            };
        })
    };

    return reportData;
}



// إنشاء تقرير الإيرادات
function generateRevenueReport() {
    const today = new Date();
    const thisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    const thisYear = new Date(today.getFullYear(), 0, 1);

    const thisMonthSales = appData.sales.filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate >= thisMonth;
    });

    const lastMonthSales = appData.sales.filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate >= lastMonth && saleDate < thisMonth;
    });

    const thisYearSales = appData.sales.filter(sale => {
        const saleDate = new Date(sale.date);
        return saleDate >= thisYear;
    });

    const thisMonthRevenue = thisMonthSales.reduce((sum, sale) => sum + sale.total, 0);
    const lastMonthRevenue = lastMonthSales.reduce((sum, sale) => sum + sale.total, 0);
    const thisYearRevenue = thisYearSales.reduce((sum, sale) => sum + sale.total, 0);

    const growthRate = lastMonthRevenue > 0 ?
        ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue * 100) : 0;

    const reportData = {
        title: 'تقرير الإيرادات',
        generatedAt: today.toLocaleString('ar-SA'),
        thisMonthRevenue,
        lastMonthRevenue,
        thisYearRevenue,
        growthRate: growthRate.toFixed(1),
        totalSales: appData.sales.length,
        averageSaleValue: appData.sales.length > 0 ?
            appData.sales.reduce((sum, sale) => sum + sale.total, 0) / appData.sales.length : 0,
        topSellingItems: getTopSellingItems(),
        monthlyBreakdown: getMonthlyRevenueBreakdown()
    };

    return reportData;
}

// الحصول على أفضل الأصناف مبيعاً
function getTopSellingItems() {
    const itemSales = {};

    appData.sales.forEach(sale => {
        if (sale.items) {
            sale.items.forEach(item => {
                if (!itemSales[item.name]) {
                    itemSales[item.name] = { quantity: 0, revenue: 0 };
                }
                itemSales[item.name].quantity += item.quantity;
                itemSales[item.name].revenue += item.quantity * item.price;
            });
        }
    });

    return Object.entries(itemSales)
        .sort(([,a], [,b]) => b.revenue - a.revenue)
        .slice(0, 10)
        .map(([name, data]) => ({ name, ...data }));
}

// الحصول على تفصيل الإيرادات الشهرية
function getMonthlyRevenueBreakdown() {
    const monthlyData = {};
    const today = new Date();

    // آخر 12 شهر
    for (let i = 11; i >= 0; i--) {
        const month = new Date(today.getFullYear(), today.getMonth() - i, 1);
        const monthKey = month.toISOString().slice(0, 7); // YYYY-MM
        monthlyData[monthKey] = 0;
    }

    appData.sales.forEach(sale => {
        const saleMonth = sale.date.slice(0, 7);
        if (monthlyData.hasOwnProperty(saleMonth)) {
            monthlyData[saleMonth] += sale.total;
        }
    });

    return Object.entries(monthlyData).map(([month, revenue]) => ({
        month,
        revenue,
        monthName: new Date(month + '-01').toLocaleDateString('ar-SA', { month: 'long', year: 'numeric' })
    }));
}

// طباعة تقرير
function printReport(reportType) {
    let reportData;

    switch (reportType) {
        case 'customers':
            reportData = generateCustomersReport();
            break;
        case 'revenue':
            reportData = generateRevenueReport();
            break;
        default:
            showErrorToast('نوع التقرير غير صحيح');
            return;
    }

    const printWindow = window.open('', '_blank');
    const reportHTML = generateReportHTML(reportData, reportType);

    printWindow.document.write(reportHTML);
    printWindow.document.close();

    printWindow.onload = function() {
        setTimeout(() => {
            printWindow.print();
        }, 1000);
    };

    showSuccessToast(`تم إنشاء ${reportData.title} بنجاح`);
}

// إنشاء HTML للتقرير
function generateReportHTML(reportData, reportType) {
    const companyInfo = `
        <div class="company-header">
            <h1>مؤسسة وقود المستقبل</h1>
            <h2>Future Fuel Corporation</h2>
            <p>تركيب وصيانة أنظمة الغاز للمركبات</p>
        </div>
    `;

    const reportHeader = `
        <div class="report-header">
            <h2>${reportData.title}</h2>
            <p>تاريخ الإنشاء: ${reportData.generatedAt}</p>
        </div>
    `;

    let reportContent = '';

    switch (reportType) {
        case 'customers':
            reportContent = generateCustomersReportHTML(reportData);
            break;
        case 'revenue':
            reportContent = generateRevenueReportHTML(reportData);
            break;
    }

    return `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${reportData.title}</title>
            <style>
                body {
                    font-family: 'Arial', sans-serif;
                    margin: 20px;
                    direction: rtl;
                    line-height: 1.6;
                    color: #333;
                }
                .company-header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 3px solid #007bff;
                    padding-bottom: 20px;
                }
                .company-header h1 {
                    color: #007bff;
                    margin: 0;
                    font-size: 28px;
                }
                .company-header h2 {
                    color: #666;
                    margin: 5px 0;
                    font-size: 18px;
                }
                .company-header p {
                    color: #888;
                    margin: 0;
                    font-size: 14px;
                }
                .report-header {
                    text-align: center;
                    margin-bottom: 30px;
                }
                .report-header h2 {
                    color: #333;
                    margin: 0;
                    font-size: 24px;
                }
                .summary-cards {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 20px;
                    margin-bottom: 30px;
                }
                .summary-card {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 8px;
                    text-align: center;
                    border: 1px solid #ddd;
                }
                .summary-card h3 {
                    margin: 0 0 10px 0;
                    color: #007bff;
                    font-size: 18px;
                }
                .summary-card .value {
                    font-size: 24px;
                    font-weight: bold;
                    color: #333;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 30px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 12px;
                    text-align: right;
                }
                th {
                    background-color: #007bff;
                    color: white;
                    font-weight: bold;
                }
                tr:nth-child(even) {
                    background-color: #f9f9f9;
                }
                .status-expired { color: #dc3545; font-weight: bold; }
                .status-expiring { color: #ffc107; font-weight: bold; }
                .status-valid { color: #28a745; font-weight: bold; }
                @media print {
                    body { margin: 0; }
                    .summary-cards { grid-template-columns: repeat(4, 1fr); }
                }
            </style>
        </head>
        <body>
            ${companyInfo}
            ${reportHeader}
            ${reportContent}
        </body>
        </html>
    `;
}

// إنشاء محتوى تقرير الزبائن
function generateCustomersReportHTML(reportData) {
    const summaryCards = `
        <div class="summary-cards">
            <div class="summary-card">
                <h3>إجمالي الزبائن</h3>
                <div class="value">${reportData.totalCustomers}</div>
            </div>
            <div class="summary-card">
                <h3>زبائن جدد هذا الشهر</h3>
                <div class="value">${reportData.newCustomersThisMonth}</div>
            </div>
            <div class="summary-card">
                <h3>زبائن لديهم سيارات</h3>
                <div class="value">${reportData.customersWithVehicles}</div>
            </div>
            <div class="summary-card">
                <h3>زبائن لديهم بطاقات غاز</h3>
                <div class="value">${reportData.customersWithGasCards}</div>
            </div>
        </div>
    `;

    const customersTable = `
        <table>
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>الهاتف</th>
                    <th>عدد السيارات</th>
                    <th>عدد بطاقات الغاز</th>
                    <th>الديون النشطة</th>
                    <th>مبلغ الديون</th>
                    <th>تاريخ التسجيل</th>
                </tr>
            </thead>
            <tbody>
                ${reportData.customers.map(customer => `
                    <tr>
                        <td>${customer.name}</td>
                        <td>${customer.phone}</td>
                        <td>${customer.vehiclesCount}</td>
                        <td>${customer.gasCardsCount}</td>
                        <td>${customer.activeDebtsCount}</td>
                        <td>${formatCurrency(customer.totalDebtAmount)}</td>
                        <td>${new Date(customer.createdAt).toLocaleDateString('ar-SA')}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    return summaryCards + customersTable;
}



// إنشاء محتوى تقرير الإيرادات
function generateRevenueReportHTML(reportData) {
    const summaryCards = `
        <div class="summary-cards">
            <div class="summary-card">
                <h3>إيرادات هذا الشهر</h3>
                <div class="value">${formatCurrency(reportData.thisMonthRevenue)}</div>
            </div>
            <div class="summary-card">
                <h3>إيرادات الشهر الماضي</h3>
                <div class="value">${formatCurrency(reportData.lastMonthRevenue)}</div>
            </div>
            <div class="summary-card">
                <h3>إيرادات هذا العام</h3>
                <div class="value">${formatCurrency(reportData.thisYearRevenue)}</div>
            </div>
            <div class="summary-card">
                <h3>نسبة النمو</h3>
                <div class="value">${reportData.growthRate}%</div>
            </div>
        </div>
    `;

    const salesSummary = `
        <div class="summary-cards">
            <div class="summary-card">
                <h3>إجمالي المبيعات</h3>
                <div class="value">${reportData.totalSales}</div>
            </div>
            <div class="summary-card">
                <h3>متوسط قيمة البيع</h3>
                <div class="value">${formatCurrency(reportData.averageSaleValue)}</div>
            </div>
        </div>
    `;

    const topItemsTable = `
        <h3>أفضل الأصناف مبيعاً</h3>
        <table>
            <thead>
                <tr>
                    <th>اسم الصنف</th>
                    <th>الكمية المباعة</th>
                    <th>إجمالي الإيرادات</th>
                </tr>
            </thead>
            <tbody>
                ${reportData.topSellingItems.map(item => `
                    <tr>
                        <td>${item.name}</td>
                        <td>${item.quantity}</td>
                        <td>${formatCurrency(item.revenue)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    const monthlyBreakdown = `
        <h3>تفصيل الإيرادات الشهرية</h3>
        <table>
            <thead>
                <tr>
                    <th>الشهر</th>
                    <th>الإيرادات</th>
                </tr>
            </thead>
            <tbody>
                ${reportData.monthlyBreakdown.map(month => `
                    <tr>
                        <td>${month.monthName}</td>
                        <td>${formatCurrency(month.revenue)}</td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;

    return summaryCards + salesSummary + topItemsTable + monthlyBreakdown;
}

// إعداد البحث
function setupSearch() {
    // بحث بطاقات الغاز
    document.getElementById('search-cards').addEventListener('input', (e) => {
        const searchTerm = e.target.value.trim().toLowerCase();
        const table = document.getElementById('gas-cards-table').querySelector('tbody');
        const rows = table.querySelectorAll('tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });

    // بحث المواعيد
    document.getElementById('search-appointments').addEventListener('input', (e) => {
        const searchTerm = e.target.value.trim().toLowerCase();
        const table = document.getElementById('appointments-table').querySelector('tbody');
        const rows = table.querySelectorAll('tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });

    // بحث الزبائن
    document.getElementById('search-customers').addEventListener('input', (e) => {
        const searchTerm = e.target.value.trim().toLowerCase();
        const table = document.getElementById('customers-table').querySelector('tbody');
        const rows = table.querySelectorAll('tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            if (text.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });


}

// إعداد الإعدادات
function setupSettings() {
    const systemSettingsForm = document.getElementById('system-settings');
    const telegramSettingsForm = document.getElementById('telegram-settings');

    // تحميل الإعدادات الحالية
    document.getElementById('shop-name').value = appData.settings.shopName;
    document.getElementById('reminder-days').value = appData.settings.reminderDays;
    document.getElementById('debt-reminder-days').value = appData.settings.debtReminderDays || 7;
    document.getElementById('working-hours-start').value = appData.settings.workingHoursStart;
    document.getElementById('working-hours-end').value = appData.settings.workingHoursEnd;

    // تحميل إعدادات تيليجرام
    if (appData.settings.telegram) {
        document.getElementById('telegram-enabled').checked = appData.settings.telegram.enabled || false;
        document.getElementById('telegram-bot-token').value = appData.settings.telegram.botToken || '';
        document.getElementById('telegram-chat-id').value = appData.settings.telegram.chatId || '';
        document.getElementById('telegram-auto-backup').checked = appData.settings.telegram.autoBackup || false;
        document.getElementById('telegram-backup-frequency').value = appData.settings.telegram.backupFrequency || 'manual';
    }

    // حفظ الإعدادات العامة
    systemSettingsForm.addEventListener('submit', (e) => {
        e.preventDefault();

        appData.settings.shopName = document.getElementById('shop-name').value;
        appData.settings.reminderDays = parseInt(document.getElementById('reminder-days').value);
        appData.settings.debtReminderDays = parseInt(document.getElementById('debt-reminder-days').value);
        appData.settings.workingHoursStart = document.getElementById('working-hours-start').value;
        appData.settings.workingHoursEnd = document.getElementById('working-hours-end').value;

        saveData();
        updateDashboard();
        showToast('تم حفظ الإعدادات بنجاح');
    });

    // حفظ إعدادات تيليجرام
    telegramSettingsForm.addEventListener('submit', (e) => {
        e.preventDefault();

        if (!appData.settings.telegram) {
            appData.settings.telegram = {};
        }

        appData.settings.telegram.enabled = document.getElementById('telegram-enabled').checked;
        appData.settings.telegram.botToken = document.getElementById('telegram-bot-token').value.trim();
        appData.settings.telegram.chatId = document.getElementById('telegram-chat-id').value.trim();
        appData.settings.telegram.autoBackup = document.getElementById('telegram-auto-backup').checked;
        appData.settings.telegram.backupFrequency = document.getElementById('telegram-backup-frequency').value;

        saveData();
        showToast('تم حفظ إعدادات تيليجرام بنجاح');
    });

    // إضافة مستمعي الأحداث لأزرار النسخ الاحتياطي والاستعادة
    if (window.electronAPI) {
        // إنشاء نسخة احتياطية
        document.getElementById('create-backup-btn').addEventListener('click', () => {
            const success = window.electronAPI.createBackup();
            if (success) {
                showToast('تم إنشاء نسخة احتياطية بنجاح');
                updateBackupsList();
            } else {
                showToast('فشل إنشاء النسخة الاحتياطية', false);
            }
        });

        // استعادة نسخة احتياطية
        document.getElementById('restore-backup-btn').addEventListener('click', () => {
            const backupSelect = document.getElementById('backup-select');
            const selectedBackup = backupSelect.value;

            if (!selectedBackup) {
                showToast('الرجاء اختيار نسخة احتياطية', false);
                return;
            }

            if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم فقدان جميع البيانات الحالية.')) {
                const success = window.electronAPI.restoreBackup(selectedBackup);
                if (success) {
                    showToast('تم استعادة النسخة الاحتياطية بنجاح');
                    loadData();
                } else {
                    showToast('فشل استعادة النسخة الاحتياطية', false);
                }
            }
        });

        // تصدير البيانات
        document.getElementById('export-data-btn').addEventListener('click', async () => {
            const options = {
                title: 'تصدير البيانات',
                defaultPath: 'gas_shop_data.json',
                filters: [
                    { name: 'JSON Files', extensions: ['json'] }
                ]
            };

            const filePath = await window.electronAPI.send('save-dialog', options);
            if (filePath) {
                const success = window.electronAPI.exportData(filePath);
                if (success) {
                    showToast('تم تصدير البيانات بنجاح');
                } else {
                    showToast('فشل تصدير البيانات', false);
                }
            }
        });

        // استيراد البيانات
        document.getElementById('import-data-btn').addEventListener('click', async () => {
            const options = {
                title: 'استيراد البيانات',
                filters: [
                    { name: 'JSON Files', extensions: ['json'] }
                ],
                properties: ['openFile']
            };

            const filePath = await window.electronAPI.send('open-dialog', options);
            if (filePath) {
                if (confirm('هل أنت متأكد من استيراد هذه البيانات؟ سيتم فقدان جميع البيانات الحالية.')) {
                    const success = window.electronAPI.importData(filePath);
                    if (success) {
                        showToast('تم استيراد البيانات بنجاح');
                        loadData();
                    } else {
                        showToast('فشل استيراد البيانات', false);
                    }
                }
            }
        });

        // تحديث قائمة النسخ الاحتياطية
        updateBackupsList();
    } else {
        // إخفاء أزرار النسخ الاحتياطي والاستعادة إذا لم تكن واجهة Electron متاحة
        const backupSection = document.getElementById('backup-restore-section');
        if (backupSection) {
            backupSection.style.display = 'none';
        }
    }

    // تصدير البيانات
    document.getElementById('export-data').addEventListener('click', () => {
        const dataStr = JSON.stringify(appData);
        const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

        const exportFileDefaultName = 'gas_shop_data.json';

        const linkElement = document.createElement('a');
        linkElement.setAttribute('href', dataUri);
        linkElement.setAttribute('download', exportFileDefaultName);
        linkElement.click();

        showToast('تم تصدير البيانات بنجاح');
    });

    // استيراد البيانات
    document.getElementById('import-data').addEventListener('click', () => {
        document.getElementById('import-file').click();
    });

    document.getElementById('import-file').addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (event) => {
            try {
                const importedData = JSON.parse(event.target.result);

                // التحقق من صحة البيانات
                if (!importedData.customers || !importedData.vehicles || !importedData.gasCards || !importedData.appointments) {
                    showToast('ملف البيانات غير صالح', false);
                    return;
                }

                if (confirm('سيتم استبدال جميع البيانات الحالية بالبيانات المستوردة. هل تريد المتابعة؟')) {
                    appData = importedData;
                    saveData();
                    updateDashboard();
                    updateAllTables();
                    showToast('تم استيراد البيانات بنجاح');
                }
            } catch (error) {
                showToast('حدث خطأ أثناء استيراد البيانات', false);
                console.error(error);
            }
        };
        reader.readAsText(file);
    });

    // مسح جميع البيانات
    document.getElementById('clear-data').addEventListener('click', () => {
        if (confirm('هل أنت متأكد من مسح جميع البيانات؟ لا يمكن التراجع عن هذا الإجراء!')) {
            if (confirm('تأكيد نهائي: سيتم مسح جميع البيانات نهائياً!')) {
                appData = {
                    customers: [],
                    vehicles: [],
                    gasCards: [],
                    appointments: [],
                    settings: {
                        shopName: 'مؤسسة وقود المستقبل',
                        reminderDays: 30,
                        workingHoursStart: '08:00',
                        workingHoursEnd: '18:00'
                    }
                };
                saveData();
                updateDashboard();
                updateAllTables();
                showToast('تم مسح جميع البيانات بنجاح');
            }
        }
    });
}

// إعداد التنقل في التقويم
function setupCalendarNavigation() {
    document.getElementById('prev-month').addEventListener('click', () => {
        // التنقل للشهر السابق
        let newMonth = currentCalendarMonth - 1;
        let newYear = currentCalendarYear;

        if (newMonth < 0) {
            newMonth = 11;
            newYear--;
        }

        updateCalendar(newMonth, newYear);
    });

    document.getElementById('next-month').addEventListener('click', () => {
        // التنقل للشهر التالي
        let newMonth = currentCalendarMonth + 1;
        let newYear = currentCalendarYear;

        if (newMonth > 11) {
            newMonth = 0;
            newYear++;
        }

        updateCalendar(newMonth, newYear);
    });

    // إضافة زر العودة للشهر الحالي
    const calendarHeader = document.querySelector('.calendar-header');
    const todayBtn = document.createElement('button');
    todayBtn.id = 'today-btn';
    todayBtn.className = 'btn';
    todayBtn.innerHTML = '<i class="fas fa-calendar-day"></i> اليوم';
    todayBtn.addEventListener('click', () => {
        const today = new Date();
        updateCalendar(today.getMonth(), today.getFullYear());
    });

    calendarHeader.appendChild(todayBtn);
}

// وظائف طباعة التقارير المحسنة
function setupPrintReports() {
    console.log('🖨️ إعداد أزرار الطباعة...');

    // طباعة تقرير بطاقات الغاز
    const printCardsBtn = document.getElementById('print-cards-btn');
    if (printCardsBtn) {
        printCardsBtn.addEventListener('click', () => {
            console.log('طباعة تقرير بطاقات الغاز');
            printGasCardsReport();
        });
    }

    // طباعة تقرير المواعيد
    const printAppointmentsBtn = document.getElementById('print-appointments-btn');
    if (printAppointmentsBtn) {
        printAppointmentsBtn.addEventListener('click', () => {
            console.log('طباعة تقرير المواعيد');
            printAppointmentsReport();
        });
    }

    // طباعة تقرير الزبائن
    const printCustomersBtn = document.getElementById('print-customers-btn');
    if (printCustomersBtn) {
        printCustomersBtn.addEventListener('click', () => {
            console.log('طباعة تقرير الزبائن');
            printReport('customers');
        });
    }

    // طباعة تقرير الديون
    const printDebtsBtn = document.getElementById('print-debts-btn');
    if (printDebtsBtn) {
        printDebtsBtn.addEventListener('click', () => {
            console.log('طباعة تقرير الديون');
            printDebtsReport();
        });
    }



    // طباعة التقرير الشهري للديون
    const printMonthlyDebtsBtn = document.getElementById('print-monthly-debts-btn');
    if (printMonthlyDebtsBtn) {
        printMonthlyDebtsBtn.addEventListener('click', () => {
            console.log('طباعة التقرير الشهري للديون');
            printMonthlyDebtsReport();
        });
    }

    // طباعة تقرير الإيرادات
    const printRevenueBtn = document.getElementById('print-revenue-btn');
    if (printRevenueBtn) {
        printRevenueBtn.addEventListener('click', () => {
            console.log('طباعة تقرير الإيرادات');
            printReport('revenue');
        });
    }

    console.log('✅ تم إعداد أزرار الطباعة بنجاح');

    // تصدير تقرير بطاقات الغاز بصيغة PDF
    document.getElementById('export-cards-pdf').addEventListener('click', () => {
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        printWindow.document.write(generateGasCardsReport());
        printWindow.document.close();

        setTimeout(() => {
            const content = printWindow.document.body;
            exportToPDF('تقرير_بطاقات_الغاز', content);
            setTimeout(() => printWindow.close(), 1000);
        }, 1000);
    });

    // تصدير تقرير المواعيد بصيغة PDF
    document.getElementById('export-appointments-pdf').addEventListener('click', () => {
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        printWindow.document.write(generateAppointmentsReport());
        printWindow.document.close();

        setTimeout(() => {
            const content = printWindow.document.body;
            exportToPDF('تقرير_المواعيد', content);
            setTimeout(() => printWindow.close(), 1000);
        }, 1000);
    });

    // تصدير تقرير الزبائن بصيغة PDF
    document.getElementById('export-customers-pdf').addEventListener('click', () => {
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        printWindow.document.write(generateCustomersReport());
        printWindow.document.close();

        setTimeout(() => {
            const content = printWindow.document.body;
            exportToPDF('تقرير_الزبائن', content);
            setTimeout(() => printWindow.close(), 1000);
        }, 1000);
    });

    // تصدير تقرير الديون بصيغة PDF
    document.getElementById('export-debts-pdf').addEventListener('click', () => {
        const printWindow = window.open('', '_blank', 'width=800,height=600');
        printWindow.document.write(generateDebtsReport());
        printWindow.document.close();

        setTimeout(() => {
            const content = printWindow.document.body;
            exportToPDF('تقرير_الديون', content);
            setTimeout(() => printWindow.close(), 1000);
        }, 1000);
    });
}

// إنشاء محتوى تقرير بطاقات الغاز
function generateGasCardsReport() {
    // إنشاء محتوى التقرير
    let reportContent = `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <title>تقرير بطاقات الغاز</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 20px;
                    direction: rtl;
                }
                h1, h2, h3 {
                    text-align: center;
                }
                .report-header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 1px solid #ddd;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                }
                th {
                    background-color: #f2f2f2;
                }
                .text-danger {
                    color: #e74c3c;
                }
                .text-warning {
                    color: #f39c12;
                }
                .text-success {
                    color: #2ecc71;
                }
                .summary {
                    margin-top: 30px;
                    padding: 10px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 0.8em;
                    color: #666;
                }
                .no-print {
                    display: block;
                }
                @media print {
                    .no-print {
                        display: none;
                    }
                    button {
                        display: none;
                    }
                }
            </style>
        </head>
        <body>
            <div class="no-print" style="text-align: center; margin-bottom: 20px;">
                <button onclick="window.print();" style="padding: 10px 20px; cursor: pointer;">طباعة التقرير</button>
            </div>

            <h1>تقرير بطاقات الغاز</h1>

            <div class="report-header">
                <div>
                    <strong>اسم المحل:</strong> ${appData.settings.shopName}
                </div>
                <div>
                    <strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الزبون</th>
                        <th>رقم السيارة</th>
                        <th>رقم البطاقة</th>
                        <th>تاريخ الإصدار</th>
                        <th>تاريخ الانتهاء</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // إضافة بيانات البطاقات
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const reminderDays = appData.settings.reminderDays;

    appData.gasCards.forEach((card, index) => {
        const vehicle = appData.vehicles.find(v => v.id === card.vehicleId);
        const customer = appData.customers.find(c => c.id === card.customerId);

        if (customer && vehicle) {
            const expiryDate = new Date(card.expiryDate);

            // تحديد حالة البطاقة
            let status = '';
            let statusClass = '';

            if (expiryDate < today) {
                status = 'منتهية';
                statusClass = 'text-danger';
            } else if (expiryDate <= new Date(today.getTime() + reminderDays * 24 * 60 * 60 * 1000)) {
                status = 'قريبة الانتهاء';
                statusClass = 'text-warning';
            } else {
                status = 'سارية';
                statusClass = 'text-success';
            }

            reportContent += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${customer.name}</td>
                    <td>${vehicle.plateNumber}</td>
                    <td>${card.cardNumber}</td>
                    <td>${formatDate(card.issueDate)}</td>
                    <td>${formatDate(card.expiryDate)}</td>
                    <td class="${statusClass}">${status}</td>
                </tr>
            `;
        }
    });

    // إضافة ملخص
    const activeCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        return expiryDate > today && expiryDate > new Date(today.getTime() + reminderDays * 24 * 60 * 60 * 1000);
    }).length;

    const expiringCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        return expiryDate <= new Date(today.getTime() + reminderDays * 24 * 60 * 60 * 1000) && expiryDate >= today;
    }).length;

    const expiredCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        return expiryDate < today;
    }).length;

    reportContent += `
                </tbody>
            </table>

            <div class="summary">
                <h3>ملخص البطاقات</h3>
                <p>إجمالي البطاقات: ${appData.gasCards.length}</p>
                <p>البطاقات السارية: ${activeCards}</p>
                <p>البطاقات قريبة الانتهاء: ${expiringCards}</p>
                <p>البطاقات المنتهية: ${expiredCards}</p>
            </div>

            <div class="footer">
                تم إنشاء هذا التقرير بواسطة نظام إدارة محل تركيب وتصليح الغاز للسيارات<br>
                ${new Date().toLocaleString('ar-SA')}
            </div>
        </body>
        </html>
    `;

    return reportContent;
}

// طباعة تقرير بطاقات الغاز
function printGasCardsReport() {
    // إنشاء نافذة الطباعة
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // تحديد محتوى التقرير
    let reportContent = generateGasCardsReport();

    // إضافة بيانات البطاقات
    const today = new Date();
    const reminderDays = appData.settings.reminderDays;

    appData.gasCards.forEach((card, index) => {
        const vehicle = appData.vehicles.find(v => v.id === card.vehicleId);
        const customer = appData.customers.find(c => c.id === card.customerId);

        if (vehicle && customer) {
            const expiryDate = new Date(card.expiryDate);
            let status = '';
            let statusClass = '';

            if (expiryDate < today) {
                status = 'منتهية';
                statusClass = 'status-expired';
            } else if (expiryDate <= new Date(today.getTime() + reminderDays * 24 * 60 * 60 * 1000)) {
                status = 'قريبة الانتهاء';
                statusClass = 'status-expiring';
            } else {
                status = 'سارية';
                statusClass = 'status-active';
            }

            reportContent += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${customer.name}</td>
                    <td>${vehicle.plateNumber}</td>
                    <td>${card.cardNumber}</td>
                    <td>${formatDate(card.issueDate)}</td>
                    <td>${formatDate(card.expiryDate)}</td>
                    <td class="${statusClass}">${status}</td>
                </tr>
            `;
        }
    });

    // إضافة ملخص
    const activeCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        return expiryDate >= today;
    }).length;

    const expiringCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        return expiryDate <= new Date(today.getTime() + reminderDays * 24 * 60 * 60 * 1000) && expiryDate >= today;
    }).length;

    const expiredCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        return expiryDate < today;
    }).length;

    reportContent += `
                </tbody>
            </table>

            <div>
                <strong>إجمالي البطاقات:</strong> ${appData.gasCards.length}<br>
                <strong>البطاقات السارية:</strong> ${activeCards}<br>
                <strong>البطاقات قريبة الانتهاء:</strong> ${expiringCards}<br>
                <strong>البطاقات المنتهية:</strong> ${expiredCards}
            </div>

            <div class="footer">
                تم إنشاء هذا التقرير بواسطة نظام إدارة محل تركيب وتصليح الغاز للسيارات<br>
                ${new Date().toLocaleString('ar-SA')}
            </div>
        </body>
        </html>
    `;

    // كتابة المحتوى في نافذة الطباعة
    printWindow.document.open();
    printWindow.document.write(reportContent);
    printWindow.document.close();
}

// إنشاء محتوى تقرير المواعيد
function generateAppointmentsReport() {
    // إنشاء محتوى التقرير
    let reportContent = `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <title>تقرير المواعيد</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 20px;
                    direction: rtl;
                }
                h1, h2, h3 {
                    text-align: center;
                }
                .report-header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 1px solid #ddd;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                }
                th {
                    background-color: #f2f2f2;
                }
                .text-danger {
                    color: #e74c3c;
                }
                .text-warning {
                    color: #f39c12;
                }
                .text-success {
                    color: #2ecc71;
                }
                .summary {
                    margin-top: 30px;
                    padding: 10px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 0.8em;
                    color: #666;
                }
                .no-print {
                    display: block;
                }
                @media print {
                    .no-print {
                        display: none;
                    }
                    button {
                        display: none;
                    }
                }
            </style>
        </head>
        <body>
            <div class="no-print" style="text-align: center; margin-bottom: 20px;">
                <button onclick="window.print();" style="padding: 10px 20px; cursor: pointer;">طباعة التقرير</button>
            </div>

            <h1>تقرير المواعيد</h1>

            <div class="report-header">
                <div>
                    <strong>اسم المحل:</strong> ${appData.settings.shopName}
                </div>
                <div>
                    <strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-EG', { numberingSystem: 'latn' })}
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الزبون</th>
                        <th>التاريخ</th>
                        <th>الوقت</th>
                        <th>نوع الخدمة</th>
                        <th>رقم السيارة</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // إضافة بيانات المواعيد
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // ترتيب المواعيد حسب التاريخ
    const sortedAppointments = [...appData.appointments].sort((a, b) => {
        return new Date(a.date + 'T' + a.time) - new Date(b.date + 'T' + b.time);
    });

    sortedAppointments.forEach((appointment, index) => {
        const customer = appData.customers.find(c => c.id === appointment.customerId);
        const vehicle = appData.vehicles.find(v => v.id === appointment.vehicleId);

        if (customer) {
            const appointmentDate = new Date(appointment.date + 'T' + appointment.time);

            // تحديد حالة الموعد
            let status = '';
            let statusClass = '';

            if (appointmentDate < today) {
                status = 'منتهي';
                statusClass = 'text-danger';
            } else if (appointment.date === today.toISOString().split('T')[0]) {
                status = 'اليوم';
                statusClass = 'text-warning';
            } else {
                status = 'قادم';
                statusClass = 'text-success';
            }

            reportContent += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${customer.name}</td>
                    <td>${formatDate(appointment.date)}</td>
                    <td>${appointment.time}</td>
                    <td>${appointment.service}</td>
                    <td>${vehicle ? vehicle.plateNumber : '-'}</td>
                    <td class="${statusClass}">${status}</td>
                </tr>
            `;
        }
    });

    // إضافة ملخص
    const pastAppointments = appData.appointments.filter(a => {
        const appointmentDate = new Date(a.date + 'T' + a.time);
        return appointmentDate < today;
    }).length;

    const todayAppointments = appData.appointments.filter(a => {
        return a.date === today.toISOString().split('T')[0];
    }).length;

    const upcomingAppointments = appData.appointments.filter(a => {
        const appointmentDate = new Date(a.date + 'T' + a.time);
        return appointmentDate > today && a.date !== today.toISOString().split('T')[0];
    }).length;

    reportContent += `
                </tbody>
            </table>

            <div class="summary">
                <h3>ملخص المواعيد</h3>
                <p>إجمالي المواعيد: ${appData.appointments.length}</p>
                <p>المواعيد المنتهية: ${pastAppointments}</p>
                <p>مواعيد اليوم: ${todayAppointments}</p>
                <p>المواعيد القادمة: ${upcomingAppointments}</p>
            </div>

            <div class="footer">
                تم إنشاء هذا التقرير بواسطة نظام إدارة محل تركيب وتصليح الغاز للسيارات<br>
                ${new Date().toLocaleString('ar-EG', { numberingSystem: 'latn' })}
            </div>
        </body>
        </html>
    `;

    return reportContent;
}

// طباعة تقرير المواعيد
function printAppointmentsReport() {
    // إنشاء نافذة الطباعة
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // تحديد محتوى التقرير
    let reportContent = `
        <html dir="rtl">
        <head>
            <title>تقرير المواعيد</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 20px;
                    direction: rtl;
                }
                h1 {
                    text-align: center;
                    margin-bottom: 20px;
                }
                .report-header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                }
                th {
                    background-color: #f2f2f2;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 0.8em;
                    color: #666;
                }
                @media print {
                    .no-print {
                        display: none;
                    }
                    button {
                        display: none;
                    }
                }
            </style>
        </head>
        <body>
            <div class="no-print" style="text-align: center; margin-bottom: 20px;">
                <button onclick="window.print();" style="padding: 10px 20px; cursor: pointer;">طباعة التقرير</button>
            </div>

            <h1>تقرير المواعيد</h1>

            <div class="report-header">
                <div>
                    <strong>اسم المحل:</strong> ${appData.settings.shopName}
                </div>
                <div>
                    <strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}
                </div>
            </div>

            <h2>مواعيد اليوم</h2>
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الوقت</th>
                        <th>اسم الزبون</th>
                        <th>رقم السيارة</th>
                        <th>نوع الخدمة</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // إضافة مواعيد اليوم
    const today = new Date().toISOString().split('T')[0];
    const todayAppointments = appData.appointments.filter(app => app.date === today);

    if (todayAppointments.length === 0) {
        reportContent += `
            <tr>
                <td colspan="6" style="text-align: center;">لا توجد مواعيد لهذا اليوم</td>
            </tr>
        `;
    } else {
        todayAppointments.sort((a, b) => a.time.localeCompare(b.time)).forEach((appointment, index) => {
            const vehicle = appData.vehicles.find(v => v.id === appointment.vehicleId);
            const customer = appData.customers.find(c => c.id === appointment.customerId);

            if (vehicle && customer) {
                reportContent += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${appointment.time}</td>
                        <td>${customer.name}</td>
                        <td>${vehicle.plateNumber}</td>
                        <td>${appointment.service}</td>
                        <td>${appointment.notes || '-'}</td>
                    </tr>
                `;
            }
        });
    }

    reportContent += `
                </tbody>
            </table>

            <h2>المواعيد القادمة</h2>
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>التاريخ</th>
                        <th>الوقت</th>
                        <th>اسم الزبون</th>
                        <th>رقم السيارة</th>
                        <th>نوع الخدمة</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // إضافة المواعيد القادمة
    const upcomingAppointments = appData.appointments.filter(app => app.date > today);

    if (upcomingAppointments.length === 0) {
        reportContent += `
            <tr>
                <td colspan="6" style="text-align: center;">لا توجد مواعيد قادمة</td>
            </tr>
        `;
    } else {
        upcomingAppointments.sort((a, b) => {
            if (a.date === b.date) {
                return a.time.localeCompare(b.time);
            }
            return a.date.localeCompare(b.date);
        }).slice(0, 10).forEach((appointment, index) => {
            const vehicle = appData.vehicles.find(v => v.id === appointment.vehicleId);
            const customer = appData.customers.find(c => c.id === appointment.customerId);

            if (vehicle && customer) {
                reportContent += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${formatDate(appointment.date)}</td>
                        <td>${appointment.time}</td>
                        <td>${customer.name}</td>
                        <td>${vehicle.plateNumber}</td>
                        <td>${appointment.service}</td>
                    </tr>
                `;
            }
        });
    }

    reportContent += `
                </tbody>
            </table>

            <div>
                <strong>إجمالي مواعيد اليوم:</strong> ${todayAppointments.length}<br>
                <strong>إجمالي المواعيد القادمة:</strong> ${upcomingAppointments.length}
            </div>

            <div class="footer">
                تم إنشاء هذا التقرير بواسطة نظام إدارة محل تركيب وتصليح الغاز للسيارات<br>
                ${new Date().toLocaleString('ar-EG', { numberingSystem: 'latn' })}
            </div>
        </body>
        </html>
    `;

    // كتابة المحتوى في نافذة الطباعة
    printWindow.document.open();
    printWindow.document.write(reportContent);
    printWindow.document.close();
}

// إنشاء محتوى تقرير الزبائن
function generateCustomersReport() {
    // إنشاء محتوى التقرير
    let reportContent = `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <title>تقرير الزبائن</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 20px;
                    direction: rtl;
                }
                h1, h2, h3 {
                    text-align: center;
                }
                .report-header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 1px solid #ddd;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                }
                th {
                    background-color: #f2f2f2;
                }
                .summary {
                    margin-top: 30px;
                    padding: 10px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 0.8em;
                    color: #666;
                }
                .no-print {
                    display: block;
                }
                @media print {
                    .no-print {
                        display: none;
                    }
                    button {
                        display: none;
                    }
                }
            </style>
        </head>
        <body>
            <div class="no-print" style="text-align: center; margin-bottom: 20px;">
                <button onclick="window.print();" style="padding: 10px 20px; cursor: pointer;">طباعة التقرير</button>
            </div>

            <h1>تقرير الزبائن</h1>

            <div class="report-header">
                <div>
                    <strong>اسم المحل:</strong> ${appData.settings.shopName}
                </div>
                <div>
                    <strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الزبون</th>
                        <th>رقم الهاتف</th>
                        <th>البريد الإلكتروني</th>
                        <th>العنوان</th>
                        <th>عدد السيارات</th>
                        <th>عدد البطاقات</th>
                        <th>آخر زيارة</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // إضافة بيانات الزبائن
    appData.customers.forEach((customer, index) => {
        const customerVehicles = appData.vehicles.filter(v => v.customerId === customer.id);
        const customerCards = appData.gasCards.filter(c => c.customerId === customer.id);

        // عرض تاريخ آخر زيارة
        let lastVisit = '-';
        if (customer.lastVisitDate) {
            lastVisit = formatDate(customer.lastVisitDate);
        }

        reportContent += `
            <tr>
                <td>${index + 1}</td>
                <td>${customer.name}</td>
                <td>${customer.phone || '-'}</td>
                <td>${customer.email || '-'}</td>
                <td>${customer.address || '-'}</td>
                <td>${customerVehicles.length}</td>
                <td>${customerCards.length}</td>
                <td>${lastVisit}</td>
            </tr>
        `;
    });

    // إضافة ملخص
    const totalVehicles = appData.vehicles.length;
    const totalCards = appData.gasCards.length;
    const activeCards = appData.gasCards.filter(card => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const expiryDate = new Date(card.expiryDate);
        return expiryDate >= today;
    }).length;

    reportContent += `
                </tbody>
            </table>

            <div class="summary">
                <h3>ملخص الزبائن</h3>
                <p>إجمالي الزبائن: ${appData.customers.length}</p>
                <p>إجمالي السيارات: ${totalVehicles}</p>
                <p>إجمالي بطاقات الغاز: ${totalCards}</p>
                <p>بطاقات الغاز السارية: ${activeCards}</p>
            </div>

            <div class="footer">
                تم إنشاء هذا التقرير بواسطة نظام إدارة محل تركيب وتصليح الغاز للسيارات<br>
                ${new Date().toLocaleString('ar-SA')}
            </div>
        </body>
        </html>
    `;

    return reportContent;
}

// طباعة تقرير الزبائن
function printCustomersReport() {
    // إنشاء نافذة الطباعة
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // تحديد محتوى التقرير
    let reportContent = `
        <html dir="rtl">
        <head>
            <title>تقرير الزبائن</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 20px;
                    direction: rtl;
                }
                h1 {
                    text-align: center;
                    margin-bottom: 20px;
                }
                .report-header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                }
                th {
                    background-color: #f2f2f2;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 0.8em;
                    color: #666;
                }
                @media print {
                    .no-print {
                        display: none;
                    }
                    button {
                        display: none;
                    }
                }
            </style>
        </head>
        <body>
            <div class="no-print" style="text-align: center; margin-bottom: 20px;">
                <button onclick="window.print();" style="padding: 10px 20px; cursor: pointer;">طباعة التقرير</button>
            </div>

            <h1>تقرير الزبائن</h1>

            <div class="report-header">
                <div>
                    <strong>اسم المحل:</strong> ${appData.settings.shopName}
                </div>
                <div>
                    <strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-EG', { numberingSystem: 'latn' })}
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الزبون</th>
                        <th>رقم الهاتف</th>
                        <th>عدد السيارات</th>
                        <th>عدد البطاقات</th>
                        <th>تاريخ آخر زيارة</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // إضافة بيانات الزبائن
    appData.customers.forEach((customer, index) => {
        const customerVehicles = appData.vehicles.filter(v => v.customerId === customer.id);
        const customerCards = appData.gasCards.filter(c => c.customerId === customer.id);

        // عرض تاريخ آخر زيارة
        let lastVisit = '-';
        if (customer.lastVisitDate) {
            lastVisit = formatDate(customer.lastVisitDate);
        }

        reportContent += `
            <tr>
                <td>${index + 1}</td>
                <td>${customer.name}</td>
                <td>${customer.phone}</td>
                <td>${customerVehicles.length}</td>
                <td>${customerCards.length}</td>
                <td>${lastVisit}</td>
            </tr>
        `;
    });

    // إضافة ملخص
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const newCustomers = appData.customers.filter(customer => {
        const createdDate = new Date(customer.createdAt);
        return createdDate >= firstDayOfMonth;
    }).length;

    reportContent += `
                </tbody>
            </table>

            <div>
                <strong>إجمالي الزبائن:</strong> ${appData.customers.length}<br>
                <strong>زبائن جدد هذا الشهر:</strong> ${newCustomers}<br>
                <strong>إجمالي السيارات:</strong> ${appData.vehicles.length}<br>
                <strong>إجمالي بطاقات الغاز:</strong> ${appData.gasCards.length}
            </div>

            <div class="footer">
                تم إنشاء هذا التقرير بواسطة نظام إدارة محل تركيب وتصليح الغاز للسيارات<br>
                ${new Date().toLocaleString('ar-EG', { numberingSystem: 'latn' })}
            </div>
        </body>
        </html>
    `;

    // كتابة المحتوى في نافذة الطباعة
    printWindow.document.open();
    printWindow.document.write(reportContent);
    printWindow.document.close();
}

// نظام الإشعارات
function setupNotifications() {
    const notificationsBtn = document.getElementById('notifications-btn');
    const floatingNotificationsBtn = document.getElementById('floating-notifications-btn');
    const notificationsContainer = document.getElementById('notifications-container');
    const closeNotificationsBtn = document.getElementById('close-notifications');

    if (!notificationsContainer || !closeNotificationsBtn) {
        console.error('عناصر الإشعارات غير موجودة');
        return;
    }

    console.log('إعداد نظام الإشعارات...');

    // دالة فتح قائمة الإشعارات
    const openNotifications = (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('تم النقر على زر الإشعارات');
        notificationsContainer.classList.toggle('active');
        updateNotificationsList();

        // إضافة تأثير الموجة للأيقونة العائمة
        if (floatingNotificationsBtn && e.target.closest('#floating-notifications-btn')) {
            addRippleEffect(floatingNotificationsBtn);
        }
    };

    // فتح قائمة الإشعارات من الهيدر
    if (notificationsBtn) {
        notificationsBtn.addEventListener('click', openNotifications);
    }

    // فتح قائمة الإشعارات من الأيقونة العائمة
    if (floatingNotificationsBtn) {
        floatingNotificationsBtn.addEventListener('click', openNotifications);
    }

    // إغلاق قائمة الإشعارات
    closeNotificationsBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        console.log('تم النقر على زر الإغلاق');
        notificationsContainer.classList.remove('active');
    });

    // إغلاق قائمة الإشعارات عند النقر خارجها
    document.addEventListener('click', (e) => {
        if (!notificationsContainer.contains(e.target) &&
            !notificationsBtn?.contains(e.target) &&
            !floatingNotificationsBtn?.contains(e.target)) {
            notificationsContainer.classList.remove('active');
        }
    });

    // تحديث عدد الإشعارات غير المقروءة
    updateNotificationsCount();

    // إضافة مستمعي الأحداث لأزرار الإشعارات
    const markAllReadBtn = document.getElementById('mark-all-read');
    const clearNotificationsBtn = document.getElementById('clear-notifications');

    if (markAllReadBtn) {
        markAllReadBtn.addEventListener('click', () => {
            markAllNotificationsAsRead();
        });
    }

    if (clearNotificationsBtn) {
        clearNotificationsBtn.addEventListener('click', () => {
            clearAllNotifications();
        });
    }

    // إضافة بعض الإشعارات التجريبية
    addSampleNotifications();

    console.log('تم إعداد نظام الإشعارات بنجاح');
}

// تحديد جميع الإشعارات كمقروءة
function markAllNotificationsAsRead() {
    if (appData.notifications) {
        appData.notifications.forEach(notification => {
            notification.read = true;
        });
        saveData();
        updateNotificationsCount();
        updateNotificationsList();
        showSuccessToast('تم تحديد جميع الإشعارات كمقروءة');
    }
}

// مسح جميع الإشعارات
function clearAllNotifications() {
    if (confirm('هل أنت متأكد من حذف جميع الإشعارات؟')) {
        appData.notifications = [];
        saveData();
        updateNotificationsCount();
        updateNotificationsList();
        showSuccessToast('تم حذف جميع الإشعارات');
    }
}

// إضافة إشعارات تجريبية
function addSampleNotifications() {
    // التحقق من وجود إشعارات
    if (!appData.notifications) {
        appData.notifications = [];
    }

    // إضافة إشعارات تجريبية إذا لم تكن موجودة
    if (appData.notifications.length === 0) {
        addNotification(
            'مرحباً بك!',
            'مرحباً بك في نظام إدارة محل تركيب وتصليح الغاز للسيارات',
            'success'
        );

        addNotification(
            'تذكير',
            'لا تنس فحص البطاقات التي تحتاج للتجديد',
            'warning'
        );

        addNotification(
            'معلومة',
            'يمكنك إدارة بطاقات الغاز والمواعيد بسهولة من النظام',
            'info'
        );
    }
}

// تم حذف هذه الدالة لتجنب التضارب - استخدم الدالة الموحدة أدناه

// تحديث قائمة الإشعارات
function updateNotificationsList() {
    const notificationsList = document.getElementById('notifications-list');
    if (!notificationsList) return;

    notificationsList.innerHTML = '';

    if (!appData.notifications || appData.notifications.length === 0) {
        notificationsList.innerHTML = '<div class="notification-item">لا توجد إشعارات</div>';
        return;
    }

    appData.notifications.forEach(notification => {
        const notificationElement = document.createElement('div');
        notificationElement.className = `notification-item ${!notification.read ? 'unread' : ''}`;

        const typeIcon = getNotificationIcon(notification.type);

        notificationElement.innerHTML = `
            <div class="notification-title">
                ${typeIcon} ${notification.title}
            </div>
            <div class="notification-message">${notification.message}</div>
            <div class="notification-date">${formatDate(notification.createdAt)}</div>
            <div class="notification-actions">
                <button onclick="markNotificationAsRead('${notification.id}')" class="btn">تحديد كمقروء</button>
                <button onclick="deleteNotification('${notification.id}')" class="btn">حذف</button>
            </div>
        `;

        notificationsList.appendChild(notificationElement);
    });
}

// الحصول على أيقونة الإشعار حسب النوع
function getNotificationIcon(type) {
    switch (type) {
        case 'success': return '✅';
        case 'warning': return '⚠️';
        case 'error': return '❌';
        case 'info':
        default: return 'ℹ️';
    }
}

// تحديد إشعار كمقروء
function markNotificationAsRead(notificationId) {
    const notification = appData.notifications.find(n => n.id === notificationId);
    if (notification) {
        notification.read = true;
        saveData();
        updateNotificationsCount();
        updateNotificationsList();
    }
}

// حذف إشعار
function deleteNotification(notificationId) {
    appData.notifications = appData.notifications.filter(n => n.id !== notificationId);
    saveData();
    updateNotificationsCount();
    updateNotificationsList();
}

// تم حذف هذه الدالة لتجنب التضارب - استخدم الدالة الموحدة أدناه

// إضافة إشعار جديد
function addNotification(title, message, type = 'info', relatedId = null, relatedType = null) {
    const notification = {
        id: generateId(),
        title,
        message,
        type,
        relatedId,
        relatedType,
        read: false,
        createdAt: new Date().toISOString()
    };

    appData.notifications.unshift(notification);

    // الاحتفاظ بآخر 50 إشعار فقط
    if (appData.notifications.length > 50) {
        appData.notifications = appData.notifications.slice(0, 50);
    }

    saveData();
    updateNotificationsCount();

    return notification;
}

// تحديث عدد الإشعارات غير المقروءة
function updateNotificationsCount() {
    const unreadCount = appData.notifications.filter(n => !n.read).length;

    // تحديث شارة الهيدر
    const badge = document.getElementById('notifications-count');
    if (badge) {
        badge.textContent = unreadCount;
        if (unreadCount === 0) {
            badge.classList.add('hidden');
        } else {
            badge.classList.remove('hidden');
        }
    }

    // تحديث شارة الأيقونة العائمة
    const floatingBadge = document.getElementById('floating-notifications-badge');
    const floatingBtn = document.getElementById('floating-notifications-btn');

    if (floatingBadge && floatingBtn) {
        floatingBadge.textContent = unreadCount;
        if (unreadCount === 0) {
            floatingBadge.classList.add('hidden');
            floatingBtn.classList.remove('has-notifications');
        } else {
            floatingBadge.classList.remove('hidden');
            floatingBtn.classList.add('has-notifications');
        }
    }
}

// إضافة تأثير الموجة للأيقونة العائمة
function addRippleEffect(button) {
    button.classList.add('ripple');

    setTimeout(() => {
        button.classList.remove('ripple');
    }, 600);
}

// تحديث قائمة الإشعارات
function updateNotificationsList() {
    const notificationsList = document.getElementById('notifications-list');
    notificationsList.innerHTML = '';

    if (appData.notifications.length === 0) {
        notificationsList.innerHTML = '<div class="empty-notifications">لا توجد إشعارات</div>';
        return;
    }

    appData.notifications.forEach(notification => {
        const notificationItem = document.createElement('div');
        notificationItem.className = 'notification-item';
        if (!notification.read) {
            notificationItem.classList.add('unread');
        }

        // تحديد أيقونة الإشعار حسب النوع
        let icon = 'info-circle';
        if (notification.type === 'warning') icon = 'exclamation-triangle';
        if (notification.type === 'success') icon = 'check-circle';
        if (notification.type === 'error') icon = 'times-circle';
        if (notification.type === 'appointment') icon = 'calendar-alt';
        if (notification.type === 'card') icon = 'id-card';

        notificationItem.innerHTML = `
            <div class="notification-title">
                <i class="fas fa-${icon}"></i> ${notification.title}
            </div>
            <div class="notification-message">${notification.message}</div>
            <div class="notification-date">${formatDateTime(notification.createdAt)}</div>
            <div class="notification-actions">
                <button class="mark-read" data-id="${notification.id}">${notification.read ? 'تحديد كغير مقروء' : 'تحديد كمقروء'}</button>
                <button class="delete-notification" data-id="${notification.id}">حذف</button>
                ${notification.relatedId ? `<button class="view-related" data-id="${notification.relatedId}" data-type="${notification.relatedType}">عرض</button>` : ''}
            </div>
        `;

        notificationsList.appendChild(notificationItem);

        // إضافة مستمعي الأحداث للأزرار
        const markReadBtn = notificationItem.querySelector('.mark-read');
        markReadBtn.addEventListener('click', () => {
            toggleNotificationRead(notification.id);
        });

        const deleteBtn = notificationItem.querySelector('.delete-notification');
        deleteBtn.addEventListener('click', () => {
            deleteNotification(notification.id);
        });

        if (notification.relatedId) {
            const viewRelatedBtn = notificationItem.querySelector('.view-related');
            viewRelatedBtn.addEventListener('click', () => {
                viewRelatedItem(notification.relatedId, notification.relatedType);
            });
        }
    });
}

// تبديل حالة قراءة الإشعار
function toggleNotificationRead(notificationId) {
    const notificationIndex = appData.notifications.findIndex(n => n.id === notificationId);
    if (notificationIndex !== -1) {
        appData.notifications[notificationIndex].read = !appData.notifications[notificationIndex].read;
        saveData();
        updateNotificationsList();
        updateNotificationsCount();
    }
}

// حذف إشعار
function deleteNotification(notificationId) {
    const notificationIndex = appData.notifications.findIndex(n => n.id === notificationId);
    if (notificationIndex !== -1) {
        appData.notifications.splice(notificationIndex, 1);
        saveData();
        updateNotificationsList();
        updateNotificationsCount();
    }
}

// عرض العنصر المرتبط بالإشعار
function viewRelatedItem(itemId, itemType) {
    // إغلاق قائمة الإشعارات
    document.getElementById('notifications-container').classList.remove('active');

    // التنقل إلى القسم المناسب وعرض العنصر
    if (itemType === 'card') {
        // التنقل إلى قسم بطاقات الغاز
        document.querySelector('nav a[data-section="gas-cards"]').click();

        // تحديد البطاقة في الجدول
        setTimeout(() => {
            const cardRow = document.querySelector(`#gas-cards-table .action-btn[data-id="${itemId}"]`);
            if (cardRow) {
                cardRow.closest('tr').scrollIntoView({ behavior: 'smooth', block: 'center' });
                cardRow.closest('tr').classList.add('highlight-row');
                setTimeout(() => {
                    cardRow.closest('tr').classList.remove('highlight-row');
                }, 3000);
            }
        }, 300);
    } else if (itemType === 'appointment') {
        // التنقل إلى قسم المواعيد
        document.querySelector('nav a[data-section="appointments"]').click();

        // البحث عن الموعد وعرضه
        const appointment = appData.appointments.find(a => a.id === itemId);
        if (appointment) {
            setTimeout(() => {
                // تحديث التقويم للشهر المناسب
                const appointmentDate = new Date(appointment.date);
                updateCalendar(appointmentDate.getMonth(), appointmentDate.getFullYear());

                // تحديث جدول المواعيد
                updateAppointmentsTable(appointment.date);

                // تحديد الموعد في الجدول
                const appointmentRow = document.querySelector(`#appointments-table .action-btn[data-id="${itemId}"]`);
                if (appointmentRow) {
                    appointmentRow.closest('tr').scrollIntoView({ behavior: 'smooth', block: 'center' });
                    appointmentRow.closest('tr').classList.add('highlight-row');
                    setTimeout(() => {
                        appointmentRow.closest('tr').classList.remove('highlight-row');
                    }, 3000);
                }
            }, 300);
        }
    }
}

// فحص البطاقات التي تحتاج للتجديد
function checkCardsForRenewal() {
    const today = new Date();
    const reminderDays = appData.settings.reminderDays;

    // البطاقات التي تنتهي خلال فترة التذكير
    const expiringCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        const daysLeft = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
        return daysLeft >= 0 && daysLeft <= reminderDays;
    });

    // إنشاء إشعارات للبطاقات التي تحتاج للتجديد
    expiringCards.forEach(card => {
        const vehicle = appData.vehicles.find(v => v.id === card.vehicleId);
        const customer = appData.customers.find(c => c.id === card.customerId);

        if (vehicle && customer) {
            const expiryDate = new Date(card.expiryDate);
            const daysLeft = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

            // التحقق من عدم وجود إشعار مماثل
            const existingNotification = appData.notifications.find(n =>
                n.relatedId === card.id &&
                n.relatedType === 'card' &&
                n.type === 'warning' &&
                !n.read
            );

            if (!existingNotification) {
                let message = '';
                if (daysLeft === 0) {
                    message = `بطاقة الغاز للزبون ${customer.name} (${vehicle.plateNumber}) تنتهي اليوم.`;
                } else {
                    message = `بطاقة الغاز للزبون ${customer.name} (${vehicle.plateNumber}) تنتهي بعد ${daysLeft} يوم.`;
                }

                addNotification(
                    'تذكير بتجديد بطاقة غاز',
                    message,
                    'warning',
                    card.id,
                    'card'
                );
            }
        }
    });

    // البطاقات المنتهية
    const expiredCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        return expiryDate < today;
    });

    // إنشاء إشعارات للبطاقات المنتهية
    expiredCards.forEach(card => {
        const vehicle = appData.vehicles.find(v => v.id === card.vehicleId);
        const customer = appData.customers.find(c => c.id === card.customerId);

        if (vehicle && customer) {
            const expiryDate = new Date(card.expiryDate);
            const daysPassed = Math.ceil((today - expiryDate) / (1000 * 60 * 60 * 24));

            // التحقق من عدم وجود إشعار مماثل
            const existingNotification = appData.notifications.find(n =>
                n.relatedId === card.id &&
                n.relatedType === 'card' &&
                n.type === 'error' &&
                !n.read
            );

            if (!existingNotification) {
                addNotification(
                    'بطاقة غاز منتهية',
                    `بطاقة الغاز للزبون ${customer.name} (${vehicle.plateNumber}) منتهية منذ ${daysPassed} يوم.`,
                    'error',
                    card.id,
                    'card'
                );
            }
        }
    });
}

// فحص المواعيد القادمة
function checkUpcomingAppointments() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayStr = today.toISOString().split('T')[0];
    const tomorrowStr = new Date(today.getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // مواعيد اليوم
    const todayAppointments = appData.appointments.filter(app => app.date === todayStr);

    // إنشاء إشعارات لمواعيد اليوم
    todayAppointments.forEach(appointment => {
        const vehicle = appData.vehicles.find(v => v.id === appointment.vehicleId);
        const customer = appData.customers.find(c => c.id === appointment.customerId);

        if (vehicle && customer) {
            // التحقق من عدم وجود إشعار مماثل
            const existingNotification = appData.notifications.find(n =>
                n.relatedId === appointment.id &&
                n.relatedType === 'appointment' &&
                n.type === 'appointment' &&
                !n.read
            );

            if (!existingNotification) {
                addNotification(
                    'موعد اليوم',
                    `لديك موعد اليوم الساعة ${appointment.time} مع الزبون ${customer.name} (${vehicle.plateNumber}) لـ ${appointment.service}.`,
                    'appointment',
                    appointment.id,
                    'appointment'
                );
            }
        }
    });

    // مواعيد الغد
    const tomorrowAppointments = appData.appointments.filter(app => app.date === tomorrowStr);

    // إنشاء إشعارات لمواعيد الغد
    tomorrowAppointments.forEach(appointment => {
        const vehicle = appData.vehicles.find(v => v.id === appointment.vehicleId);
        const customer = appData.customers.find(c => c.id === appointment.customerId);

        if (vehicle && customer) {
            // التحقق من عدم وجود إشعار مماثل
            const existingNotification = appData.notifications.find(n =>
                n.relatedId === appointment.id &&
                n.relatedType === 'appointment' &&
                n.type === 'info' &&
                !n.read
            );

            if (!existingNotification) {
                addNotification(
                    'موعد غداً',
                    `لديك موعد غداً الساعة ${appointment.time} مع الزبون ${customer.name} (${vehicle.plateNumber}) لـ ${appointment.service}.`,
                    'info',
                    appointment.id,
                    'appointment'
                );
            }
        }
    });
}

// فحص الديون المستحقة
function checkDueDebts() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // الحصول على الديون النشطة فقط
    const activeDebts = appData.debts.filter(debt => !debt.isPaid);

    // البحث عن الديون التي ستستحق قريباً
    const upcomingDueDebts = activeDebts.filter(debt => {
        const dueDate = new Date(debt.dueDate);
        dueDate.setHours(0, 0, 0, 0);

        // حساب الأيام المتبقية
        const daysLeft = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

        // الديون التي ستستحق خلال فترة التذكير
        return daysLeft >= 0 && daysLeft <= appData.settings.debtReminderDays;
    });

    // البحث عن الديون المتأخرة
    const overdueDebts = activeDebts.filter(debt => {
        const dueDate = new Date(debt.dueDate);
        dueDate.setHours(0, 0, 0, 0);

        return dueDate < today;
    });

    if (upcomingDueDebts.length > 0) {
        // إضافة إشعار بالديون التي ستستحق قريباً
        addNotification(
            'ديون مستحقة قريباً',
            `لديك ${upcomingDueDebts.length} ديون ستستحق خلال ${appData.settings.debtReminderDays} أيام`,
            'warning'
        );
    }

    if (overdueDebts.length > 0) {
        // إضافة إشعار بالديون المتأخرة
        addNotification(
            'ديون متأخرة',
            `لديك ${overdueDebts.length} ديون متأخرة عن موعد السداد`,
            'error'
        );
    }

    activeDebts.forEach(debt => {
        const customer = appData.customers.find(c => c.id === debt.customerId);
        if (customer) {
            const dueDate = new Date(debt.dueDate);
            dueDate.setHours(0, 0, 0, 0);

            // حساب المبلغ المتبقي
            const payments = appData.debtPayments.filter(payment => payment.debtId === debt.id);
            const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
            const remainingAmount = debt.amount - totalPaid;

            // إضافة إشعار إذا كان الدين مستحق اليوم أو متأخر
            if (dueDate <= today) {
                const daysLate = Math.ceil((today - dueDate) / (1000 * 60 * 60 * 24));
                let message = '';

                if (daysLate === 0) {
                    message = `دين الزبون ${customer.name} مستحق اليوم. المبلغ المتبقي: ${remainingAmount.toFixed(2)}`;
                } else {
                    message = `دين الزبون ${customer.name} متأخر بـ ${daysLate} يوم. المبلغ المتبقي: ${remainingAmount.toFixed(2)}`;
                }

                // التحقق من عدم وجود إشعار مشابه غير مقروء
                const existingNotification = appData.notifications.find(n =>
                    n.relatedId === debt.id &&
                    n.relatedType === 'debt' &&
                    n.type === 'error' &&
                    !n.read
                );

                if (!existingNotification) {
                    addNotification(
                        'دين مستحق',
                        message,
                        'error',
                        debt.id,
                        'debt'
                    );
                }
            }

            // إضافة إشعار إذا كان الدين سيستحق قريباً (خلال فترة التذكير)
            const reminderDate = new Date(dueDate);
            reminderDate.setDate(reminderDate.getDate() - appData.settings.debtReminderDays);

            if (today >= reminderDate && today < dueDate) {
                const daysUntilDue = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));
                const message = `دين الزبون ${customer.name} سيستحق بعد ${daysUntilDue} يوم. المبلغ المتبقي: ${remainingAmount.toFixed(2)}`;

                // التحقق من عدم وجود إشعار مشابه غير مقروء
                const existingNotification = appData.notifications.find(n =>
                    n.relatedId === debt.id &&
                    n.relatedType === 'debt' &&
                    n.type === 'warning' &&
                    !n.read
                );

                if (!existingNotification) {
                    addNotification(
                        'تذكير بدين مستحق قريباً',
                        message,
                        'warning',
                        debt.id,
                        'debt'
                    );
                }
            }
        }
    });
}

// تنسيق التاريخ والوقت
function formatDateTime(dateTimeStr) {
    const date = new Date(dateTimeStr);
    return date.toLocaleString('ar-EG', { numberingSystem: 'latn' });
}

// تحديث جداول الديون
function updateDebtsTable() {
    // تحديث جدول الديون النشطة
    const activeDebtsTable = document.getElementById('active-debts-table').querySelector('tbody');
    activeDebtsTable.innerHTML = '';

    // تحديث جدول الديون المسددة
    const paidDebtsTable = document.getElementById('paid-debts-table').querySelector('tbody');
    paidDebtsTable.innerHTML = '';

    // تحديث جدول سجل المدفوعات
    const paymentsHistoryTable = document.getElementById('payments-history-table').querySelector('tbody');
    paymentsHistoryTable.innerHTML = '';

    // تحديث قائمة الزبائن في فلتر الزبائن
    updateDebtCustomerFilter();

    // الحصول على التاريخ الحالي
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // الحصول على قيم الفلاتر
    const statusFilter = document.getElementById('debt-status-filter').value;
    const customerFilter = document.getElementById('debt-customer-filter').value;

    // تحديث فلتر الزبائن
    function updateDebtCustomerFilter() {
        const customerFilter = document.getElementById('debt-customer-filter');
        // حفظ القيمة الحالية
        const currentValue = customerFilter.value;

        // إفراغ القائمة مع الاحتفاظ بخيار "الكل"
        customerFilter.innerHTML = '<option value="all">الكل</option>';

        // الحصول على قائمة الزبائن الذين لديهم ديون
        const debtCustomerIds = [...new Set(appData.debts.map(debt => debt.customerId))];

        // إضافة الزبائن للقائمة
        debtCustomerIds.forEach(customerId => {
            const customer = appData.customers.find(c => c.id === customerId);
            if (customer) {
                const option = document.createElement('option');
                option.value = customer.id;
                option.textContent = customer.name;
                customerFilter.appendChild(option);
            }
        });

        // استعادة القيمة السابقة إذا كانت موجودة
        if (currentValue && customerFilter.querySelector(`option[value="${currentValue}"]`)) {
            customerFilter.value = currentValue;
        }
    }

    // تصفية وعرض الديون النشطة
    let activeDebts = appData.debts.filter(debt => !debt.isPaid);

    // تطبيق فلتر الزبون
    if (customerFilter !== 'all') {
        activeDebts = activeDebts.filter(debt => debt.customerId === customerFilter);
    }

    // تطبيق فلتر الحالة
    if (statusFilter !== 'all') {
        activeDebts = activeDebts.filter(debt => {
            const dueDate = new Date(debt.dueDate);
            dueDate.setHours(0, 0, 0, 0);
            const daysLeft = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

            if (statusFilter === 'overdue') {
                return daysLeft < 0;
            } else if (statusFilter === 'upcoming') {
                return daysLeft >= 0 && daysLeft <= appData.settings.debtReminderDays;
            } else if (statusFilter === 'active') {
                return daysLeft >= 0;
            }

            return true;
        });
    }

    activeDebts.sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate)).forEach(debt => {
        const customer = appData.customers.find(c => c.id === debt.customerId);
        if (customer) {
            // حساب المبلغ المتبقي
            const payments = appData.debtPayments.filter(payment => payment.debtId === debt.id);
            const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
            const remainingAmount = debt.amount - totalPaid;

            // حساب الأيام المتبقية
            const dueDate = new Date(debt.dueDate);
            dueDate.setHours(0, 0, 0, 0);
            const daysLeft = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));
            let daysLeftText = `${daysLeft} يوم`;
            let daysLeftClass = '';

            if (daysLeft < 0) {
                daysLeftText = `متأخر ${Math.abs(daysLeft)} يوم`;
                daysLeftClass = 'text-danger';
            } else if (daysLeft <= appData.settings.debtReminderDays) {
                daysLeftClass = 'text-warning';
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${customer.name}</td>
                <td>${debt.amount.toFixed(2)}</td>
                <td>${remainingAmount.toFixed(2)}</td>
                <td>${formatDate(debt.dueDate)}</td>
                <td class="${daysLeftClass}">${daysLeftText}</td>
                <td>${debt.notes || '-'}</td>
                <td class="action-cell">
                    <button class="action-btn pay" data-id="${debt.id}" title="تسجيل دفعة"><i class="fas fa-money-bill"></i></button>
                    <button class="action-btn edit" data-id="${debt.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                    <button class="action-btn delete" data-id="${debt.id}" title="حذف"><i class="fas fa-trash"></i></button>
                </td>
            `;
            activeDebtsTable.appendChild(row);

            // إضافة مستمعي الأحداث للأزرار
            row.querySelector('.pay').addEventListener('click', () => openPaymentModal(debt.id));
            row.querySelector('.edit').addEventListener('click', () => editDebt(debt.id));
            row.querySelector('.delete').addEventListener('click', () => deleteDebt(debt.id));
        }
    });

    // تصفية وعرض الديون المسددة
    const paidDebts = appData.debts.filter(debt => debt.isPaid);
    paidDebts.sort((a, b) => new Date(b.paidDate) - new Date(a.paidDate)).forEach(debt => {
        const customer = appData.customers.find(c => c.id === debt.customerId);
        if (customer) {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${customer.name}</td>
                <td>${debt.amount.toFixed(2)}</td>
                <td>${formatDate(debt.createdAt)}</td>
                <td>${formatDate(debt.paidDate)}</td>
                <td>${debt.notes || '-'}</td>
                <td class="action-cell">
                    <button class="action-btn view" data-id="${debt.id}" title="عرض"><i class="fas fa-eye"></i></button>
                    <button class="action-btn delete" data-id="${debt.id}" title="حذف"><i class="fas fa-trash"></i></button>
                </td>
            `;
            paidDebtsTable.appendChild(row);

            // إضافة مستمعي الأحداث للأزرار
            row.querySelector('.view').addEventListener('click', () => viewDebtPayments(debt.id));
            row.querySelector('.delete').addEventListener('click', () => deleteDebt(debt.id));
        }
    });

    // عرض سجل المدفوعات
    appData.debtPayments.sort((a, b) => new Date(b.date) - new Date(a.date)).forEach(payment => {
        const debt = appData.debts.find(d => d.id === payment.debtId);
        if (debt) {
            const customer = appData.customers.find(c => c.id === debt.customerId);
            if (customer) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${customer.name}</td>
                    <td>${payment.amount.toFixed(2)}</td>
                    <td>${formatDate(payment.date)}</td>
                    <td>${payment.method}</td>
                    <td>${payment.notes || '-'}</td>
                    <td class="action-cell">
                        <button class="action-btn edit" data-id="${payment.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                        <button class="action-btn delete" data-id="${payment.id}" title="حذف"><i class="fas fa-trash"></i></button>
                    </td>
                `;
                paymentsHistoryTable.appendChild(row);

                // إضافة مستمعي الأحداث للأزرار
                row.querySelector('.edit').addEventListener('click', () => editPayment(payment.id));
                row.querySelector('.delete').addEventListener('click', () => deletePayment(payment.id));
            }
        }
    });

    // إضافة رسالة إذا لم تكن هناك بيانات
    if (activeDebts.length === 0) {
        activeDebtsTable.innerHTML = '<tr><td colspan="7" class="text-center">لا توجد ديون نشطة</td></tr>';
    }

    if (paidDebts.length === 0) {
        paidDebtsTable.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد ديون مسددة</td></tr>';
    }

    if (appData.debtPayments.length === 0) {
        paymentsHistoryTable.innerHTML = '<tr><td colspan="6" class="text-center">لا يوجد سجل مدفوعات</td></tr>';
    }
}

// تحديث الرسوم البيانية
function updateCharts() {
    updateCardsChart();
    updateAppointmentsChart();
    updateDebtsChart();
    updateStatsSummary();
}

// تحديث الرسم البياني للبطاقات
function updateCardsChart() {
    const today = new Date();
    const reminderDays = appData.settings.reminderDays;

    // حساب عدد البطاقات حسب الحالة
    const activeCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        return expiryDate > new Date(today.getTime() + reminderDays * 24 * 60 * 60 * 1000);
    }).length;

    const expiringCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        return expiryDate <= new Date(today.getTime() + reminderDays * 24 * 60 * 60 * 1000) && expiryDate >= today;
    }).length;

    const expiredCards = appData.gasCards.filter(card => {
        const expiryDate = new Date(card.expiryDate);
        return expiryDate < today;
    }).length;

    // تحديث ملخص الإحصائيات
    document.getElementById('active-cards-count').textContent = activeCards;
    document.getElementById('expiring-cards-count').textContent = expiringCards;
    document.getElementById('expired-cards-count').textContent = expiredCards;

    // إنشاء الرسم البياني
    const ctx = document.getElementById('cards-chart').getContext('2d');

    // التحقق من وجود رسم بياني سابق وتدميره
    if (window.cardsChart) {
        window.cardsChart.destroy();
    }

    window.cardsChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['سارية', 'قريبة الانتهاء', 'منتهية'],
            datasets: [{
                data: [activeCards, expiringCards, expiredCards],
                backgroundColor: [
                    '#2ecc71',
                    '#f39c12',
                    '#e74c3c'
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// تحديث الرسم البياني للمواعيد
function updateAppointmentsChart() {
    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    // الحصول على الأشهر الستة الماضية
    const months = [];
    const monthsData = [];

    for (let i = 5; i >= 0; i--) {
        let month = currentMonth - i;
        let year = currentYear;

        if (month < 0) {
            month += 12;
            year--;
        }

        // استخدام مصفوفة أسماء الأشهر بالفرنسية بدلاً من toLocaleDateString
        const frenchMonths = ['جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي', 'جوان', 'جويلية', 'أوت', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'];
        const monthName = frenchMonths[month];
        months.push(monthName);

        // حساب عدد المواعيد في هذا الشهر
        const monthStart = new Date(year, month, 1).toISOString().split('T')[0];
        const monthEnd = new Date(year, month + 1, 0).toISOString().split('T')[0];

        const monthAppointments = appData.appointments.filter(app => {
            return app.date >= monthStart && app.date <= monthEnd;
        }).length;

        monthsData.push(monthAppointments);
    }

    // تحديث ملخص الإحصائيات
    const currentMonthAppointments = monthsData[5];
    const lastMonthAppointments = monthsData[4];

    document.getElementById('month-appointments-count').textContent = currentMonthAppointments;
    document.getElementById('last-month-appointments-count').textContent = lastMonthAppointments;

    // حساب نسبة النمو
    let growthRate = 0;
    if (lastMonthAppointments > 0) {
        growthRate = ((currentMonthAppointments - lastMonthAppointments) / lastMonthAppointments) * 100;
    }

    document.getElementById('appointments-growth').textContent = growthRate.toFixed(1) + '%';
    document.getElementById('appointments-growth').style.color = growthRate >= 0 ? '#2ecc71' : '#e74c3c';

    // إنشاء الرسم البياني
    const ctx = document.getElementById('appointments-chart').getContext('2d');

    // التحقق من وجود رسم بياني سابق وتدميره
    if (window.appointmentsChart) {
        window.appointmentsChart.destroy();
    }

    window.appointmentsChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: months,
            datasets: [{
                label: 'عدد المواعيد',
                data: monthsData,
                backgroundColor: '#3498db',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

// تحديث الرسم البياني للديون
function updateDebtsChart() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // حساب عدد الديون حسب الحالة
    const activeDebts = appData.debts.filter(debt => !debt.isPaid).length;

    const overdueDebts = appData.debts.filter(debt => {
        if (debt.isPaid) return false;
        const dueDate = new Date(debt.dueDate);
        dueDate.setHours(0, 0, 0, 0);
        return dueDate < today;
    }).length;

    const paidDebts = appData.debts.filter(debt => debt.isPaid).length;

    // حساب إجمالي المبالغ المستحقة
    let totalDebtAmount = 0;
    appData.debts.forEach(debt => {
        if (!debt.isPaid) {
            const payments = appData.debtPayments.filter(payment => payment.debtId === debt.id);
            const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
            totalDebtAmount += (debt.amount - totalPaid);
        }
    });

    // تحديث عدادات الإحصائيات
    document.getElementById('active-debts-count').textContent = activeDebts;
    document.getElementById('overdue-debts-count').textContent = overdueDebts;
    document.getElementById('paid-debts-count').textContent = paidDebts;
    document.getElementById('total-debt-amount').textContent = totalDebtAmount.toFixed(2);

    // إنشاء الرسم البياني
    const ctx = document.getElementById('debts-chart').getContext('2d');

    // التحقق من وجود رسم بياني سابق وتدميره
    if (window.debtsChart) {
        window.debtsChart.destroy();
    }

    window.debtsChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: ['ديون نشطة', 'ديون متأخرة', 'ديون مسددة'],
            datasets: [{
                data: [activeDebts - overdueDebts, overdueDebts, paidDebts],
                backgroundColor: [
                    '#3498db', // أزرق
                    '#e74c3c', // أحمر
                    '#2ecc71'  // أخضر
                ],
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            family: 'Cairo, sans-serif'
                        }
                    }
                }
            }
        }
    });
}

// تحديث ملخص الإحصائيات
function updateStatsSummary() {
    // يمكن إضافة المزيد من الإحصائيات هنا
}

// إعداد فلاتر الديون
function setupDebtFilters() {
    const statusFilter = document.getElementById('debt-status-filter');
    const customerFilter = document.getElementById('debt-customer-filter');

    // إضافة مستمعي الأحداث للفلاتر
    statusFilter.addEventListener('change', updateDebtsTable);
    customerFilter.addEventListener('change', updateDebtsTable);
}

// إعداد نموذج الدين
function setupDebtForm() {
    const addDebtBtn = document.getElementById('add-debt-btn');
    const debtModal = document.getElementById('debt-modal');
    const debtForm = document.getElementById('debt-form');
    const cancelDebtBtn = document.getElementById('cancel-debt');
    const closeBtn = debtModal.querySelector('.close');

    // فتح النموذج عند النقر على زر الإضافة
    addDebtBtn.addEventListener('click', () => {
        document.getElementById('debt-modal-title').textContent = 'إضافة دين جديد';
        debtForm.reset();
        document.getElementById('debt-id').value = '';

        // ملء قائمة الزبائن
        const customerSelect = document.getElementById('debt-customer');
        customerSelect.innerHTML = '<option value="">اختر زبون</option>';
        appData.customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            customerSelect.appendChild(option);
        });

        // تعيين تاريخ الاستحقاق الافتراضي (بعد 30 يوم)
        const defaultDueDate = new Date();
        defaultDueDate.setDate(defaultDueDate.getDate() + 30);
        document.getElementById('debt-due-date').value = defaultDueDate.toISOString().split('T')[0];

        debtModal.style.display = 'block';
    });

    // إغلاق النموذج
    cancelDebtBtn.addEventListener('click', () => {
        debtModal.style.display = 'none';
    });

    closeBtn.addEventListener('click', () => {
        debtModal.style.display = 'none';
    });

    // معالجة تقديم النموذج
    debtForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const debtId = document.getElementById('debt-id').value;
        const customerId = document.getElementById('debt-customer').value;
        const amount = parseFloat(document.getElementById('debt-amount').value);
        const dueDate = document.getElementById('debt-due-date').value;
        const reason = document.getElementById('debt-reason').value;
        const notes = document.getElementById('debt-notes').value;

        if (!customerId || !amount || !dueDate || !reason) {
            showToast('يرجى ملء جميع الحقول المطلوبة', false);
            return;
        }

        if (amount <= 0) {
            showToast('يجب أن يكون المبلغ أكبر من صفر', false);
            return;
        }

        if (new Date(dueDate) < new Date().setHours(0, 0, 0, 0)) {
            if (!confirm('تاريخ الاستحقاق في الماضي. هل تريد المتابعة؟')) {
                return;
            }
        }

        if (debtId) {
            // تحديث دين موجود
            const debtIndex = appData.debts.findIndex(d => d.id === debtId);
            if (debtIndex !== -1) {
                appData.debts[debtIndex] = {
                    ...appData.debts[debtIndex],
                    customerId,
                    amount,
                    dueDate,
                    reason,
                    notes,
                    updatedAt: new Date().toISOString()
                };
                showToast('تم تحديث الدين بنجاح');
            }
        } else {
            // إضافة دين جديد
            const newDebt = {
                id: generateId(),
                customerId,
                amount,
                dueDate,
                reason,
                notes,
                isPaid: false,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            appData.debts.push(newDebt);
            showToast('تمت إضافة الدين بنجاح');
        }

        // حفظ البيانات وتحديث الواجهة
        saveData();
        updateDebtsTable();

        // إغلاق النموذج
        debtModal.style.display = 'none';
    });
}

// إعداد نموذج الدفعة
function setupPaymentForm() {
    const addPaymentBtn = document.getElementById('add-payment-btn');
    const paymentModal = document.getElementById('payment-modal');
    const paymentForm = document.getElementById('payment-form');
    const cancelPaymentBtn = document.getElementById('cancel-payment');
    const closeBtn = paymentModal.querySelector('.close');

    // فتح النموذج عند النقر على زر الإضافة
    addPaymentBtn.addEventListener('click', () => {
        document.getElementById('payment-modal-title').textContent = 'تسجيل دفعة';
        paymentForm.reset();
        document.getElementById('payment-id').value = '';
        document.getElementById('debt-info').style.display = 'none';

        // ملء قائمة الديون
        const debtSelect = document.getElementById('payment-debt');
        debtSelect.innerHTML = '<option value="">اختر الدين</option>';

        // الحصول على الديون النشطة فقط
        const activeDebts = appData.debts.filter(debt => !debt.isPaid);

        activeDebts.forEach(debt => {
            const customer = appData.customers.find(c => c.id === debt.customerId);
            if (customer) {
                // حساب المبلغ المتبقي
                const payments = appData.debtPayments.filter(payment => payment.debtId === debt.id);
                const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
                const remainingAmount = debt.amount - totalPaid;

                const option = document.createElement('option');
                option.value = debt.id;
                option.textContent = `${customer.name} - ${remainingAmount.toFixed(2)} (${debt.reason})`;
                option.setAttribute('data-customer', customer.name);
                option.setAttribute('data-original', debt.amount.toFixed(2));
                option.setAttribute('data-remaining', remainingAmount.toFixed(2));
                debtSelect.appendChild(option);
            }
        });

        // تعيين تاريخ الدفع الافتراضي (اليوم)
        document.getElementById('payment-date').value = new Date().toISOString().split('T')[0];

        paymentModal.style.display = 'block';
    });

    // تحديث معلومات الدين عند اختيار دين
    document.getElementById('payment-debt').addEventListener('change', (e) => {
        const debtId = e.target.value;
        if (debtId) {
            const selectedOption = e.target.options[e.target.selectedIndex];
            document.getElementById('payment-customer-name').textContent = selectedOption.getAttribute('data-customer');
            document.getElementById('payment-original-amount').textContent = selectedOption.getAttribute('data-original');
            document.getElementById('payment-remaining-amount').textContent = selectedOption.getAttribute('data-remaining');
            document.getElementById('debt-info').style.display = 'block';

            // تعيين المبلغ المتبقي كقيمة افتراضية للدفعة
            document.getElementById('payment-amount').value = selectedOption.getAttribute('data-remaining');
            document.getElementById('payment-amount').max = selectedOption.getAttribute('data-remaining');
        } else {
            document.getElementById('debt-info').style.display = 'none';
        }
    });

    // إغلاق النموذج
    cancelPaymentBtn.addEventListener('click', () => {
        paymentModal.style.display = 'none';
    });

    closeBtn.addEventListener('click', () => {
        paymentModal.style.display = 'none';
    });

    // معالجة تقديم النموذج
    paymentForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const paymentId = document.getElementById('payment-id').value;
        const debtId = document.getElementById('payment-debt').value;
        const amount = parseFloat(document.getElementById('payment-amount').value);
        const date = document.getElementById('payment-date').value;
        const method = document.getElementById('payment-method').value;
        const notes = document.getElementById('payment-notes').value;

        if (!debtId || !amount || !date || !method) {
            showToast('يرجى ملء جميع الحقول المطلوبة', false);
            return;
        }

        if (amount <= 0) {
            showToast('يجب أن يكون المبلغ أكبر من صفر', false);
            return;
        }

        // التحقق من أن المبلغ المدفوع لا يتجاوز المبلغ المتبقي
        const debt = appData.debts.find(d => d.id === debtId);
        if (debt) {
            const payments = appData.debtPayments.filter(payment => payment.debtId === debtId);
            const totalPaid = payments.reduce((sum, payment) => {
                // استثناء الدفعة الحالية إذا كانت موجودة (في حالة التعديل)
                if (paymentId && payment.id === paymentId) {
                    return sum;
                }
                return sum + payment.amount;
            }, 0);
            const remainingAmount = debt.amount - totalPaid;

            if (amount > remainingAmount) {
                showToast(`المبلغ المدفوع يتجاوز المبلغ المتبقي (${remainingAmount.toFixed(2)})`, false);
                return;
            }

            if (paymentId) {
                // تحديث دفعة موجودة
                const paymentIndex = appData.debtPayments.findIndex(p => p.id === paymentId);
                if (paymentIndex !== -1) {
                    appData.debtPayments[paymentIndex] = {
                        ...appData.debtPayments[paymentIndex],
                        debtId,
                        amount,
                        date,
                        method,
                        notes,
                        updatedAt: new Date().toISOString()
                    };
                    showToast('تم تحديث الدفعة بنجاح');
                }
            } else {
                // إضافة دفعة جديدة
                const newPayment = {
                    id: generateId(),
                    debtId,
                    amount,
                    date,
                    method,
                    notes,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                };
                appData.debtPayments.push(newPayment);
                showToast('تمت إضافة الدفعة بنجاح');

                // التحقق مما إذا كان الدين قد تم سداده بالكامل
                const allPayments = [...payments, newPayment];
                const newTotalPaid = allPayments.reduce((sum, payment) => sum + payment.amount, 0);

                if (newTotalPaid >= debt.amount) {
                    // تحديث حالة الدين إلى مسدد
                    const debtIndex = appData.debts.findIndex(d => d.id === debtId);
                    if (debtIndex !== -1) {
                        appData.debts[debtIndex] = {
                            ...appData.debts[debtIndex],
                            isPaid: true,
                            paidDate: date,
                            updatedAt: new Date().toISOString()
                        };
                        showToast('تم سداد الدين بالكامل!', true);
                    }
                }
            }

            // حفظ البيانات وتحديث الواجهة
            saveData();
            updateDebtsTable();

            // إغلاق النموذج
            paymentModal.style.display = 'none';
        }
    });
}

// فتح نافذة تسجيل دفعة لدين محدد
function openPaymentModal(debtId) {
    const debt = appData.debts.find(d => d.id === debtId);
    if (!debt) return;

    const customer = appData.customers.find(c => c.id === debt.customerId);
    if (!customer) return;

    // حساب المبلغ المتبقي
    const payments = appData.debtPayments.filter(payment => payment.debtId === debtId);
    const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
    const remainingAmount = debt.amount - totalPaid;

    document.getElementById('payment-modal-title').textContent = 'تسجيل دفعة';
    document.getElementById('payment-form').reset();
    document.getElementById('payment-id').value = '';

    // ملء قائمة الديون
    const debtSelect = document.getElementById('payment-debt');
    debtSelect.innerHTML = `<option value="${debt.id}" selected>${customer.name} - ${remainingAmount.toFixed(2)} (${debt.reason})</option>`;
    debtSelect.disabled = true;

    // عرض معلومات الدين
    document.getElementById('payment-customer-name').textContent = customer.name;
    document.getElementById('payment-original-amount').textContent = debt.amount.toFixed(2);
    document.getElementById('payment-remaining-amount').textContent = remainingAmount.toFixed(2);
    document.getElementById('debt-info').style.display = 'block';

    // تعيين المبلغ المتبقي كقيمة افتراضية للدفعة
    document.getElementById('payment-amount').value = remainingAmount.toFixed(2);
    document.getElementById('payment-amount').max = remainingAmount;

    // تعيين تاريخ الدفع الافتراضي (اليوم)
    document.getElementById('payment-date').value = new Date().toISOString().split('T')[0];

    document.getElementById('payment-modal').style.display = 'block';
}

// تحرير دين
function editDebt(debtId) {
    const debt = appData.debts.find(d => d.id === debtId);
    if (!debt) return;

    document.getElementById('debt-modal-title').textContent = 'تعديل الدين';
    document.getElementById('debt-id').value = debt.id;

    // ملء قائمة الزبائن
    const customerSelect = document.getElementById('debt-customer');
    customerSelect.innerHTML = '<option value="">اختر زبون</option>';
    appData.customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = customer.name;
        if (customer.id === debt.customerId) {
            option.selected = true;
        }
        customerSelect.appendChild(option);
    });

    document.getElementById('debt-amount').value = debt.amount;
    document.getElementById('debt-due-date').value = debt.dueDate;
    document.getElementById('debt-reason').value = debt.reason;
    document.getElementById('debt-notes').value = debt.notes || '';

    document.getElementById('debt-modal').style.display = 'block';
}

// تحرير دفعة
function editPayment(paymentId) {
    const payment = appData.debtPayments.find(p => p.id === paymentId);
    if (!payment) return;

    const debt = appData.debts.find(d => d.id === payment.debtId);
    if (!debt) return;

    const customer = appData.customers.find(c => c.id === debt.customerId);
    if (!customer) return;

    document.getElementById('payment-modal-title').textContent = 'تعديل الدفعة';
    document.getElementById('payment-id').value = payment.id;

    // حساب المبلغ المتبقي
    const payments = appData.debtPayments.filter(p => p.debtId === debt.id);
    const totalPaid = payments.reduce((sum, p) => {
        // استثناء الدفعة الحالية
        if (p.id === paymentId) {
            return sum;
        }
        return sum + p.amount;
    }, 0);
    const remainingAmount = debt.amount - totalPaid;

    // ملء قائمة الديون
    const debtSelect = document.getElementById('payment-debt');
    debtSelect.innerHTML = `<option value="${debt.id}" selected>${customer.name} - ${(remainingAmount + payment.amount).toFixed(2)} (${debt.reason})</option>`;
    debtSelect.disabled = true;

    // عرض معلومات الدين
    document.getElementById('payment-customer-name').textContent = customer.name;
    document.getElementById('payment-original-amount').textContent = debt.amount.toFixed(2);
    document.getElementById('payment-remaining-amount').textContent = (remainingAmount + payment.amount).toFixed(2);
    document.getElementById('debt-info').style.display = 'block';

    document.getElementById('payment-amount').value = payment.amount;
    document.getElementById('payment-amount').max = remainingAmount + payment.amount;
    document.getElementById('payment-date').value = payment.date;
    document.getElementById('payment-method').value = payment.method;
    document.getElementById('payment-notes').value = payment.notes || '';

    document.getElementById('payment-modal').style.display = 'block';
}

// حذف دين
function deleteDebt(debtId) {
    if (confirm('هل أنت متأكد من حذف هذا الدين؟ سيتم حذف جميع المدفوعات المرتبطة به أيضاً.')) {
        // حذف الدين
        const debtIndex = appData.debts.findIndex(d => d.id === debtId);
        if (debtIndex !== -1) {
            appData.debts.splice(debtIndex, 1);

            // حذف المدفوعات المرتبطة بالدين
            appData.debtPayments = appData.debtPayments.filter(payment => payment.debtId !== debtId);

            saveData();
            updateDebtsTable();
            showToast('تم حذف الدين بنجاح');
        }
    }
}

// حذف دفعة
function deletePayment(paymentId) {
    if (confirm('هل أنت متأكد من حذف هذه الدفعة؟')) {
        const payment = appData.debtPayments.find(p => p.id === paymentId);
        if (payment) {
            const debtId = payment.debtId;

            // حذف الدفعة
            const paymentIndex = appData.debtPayments.findIndex(p => p.id === paymentId);
            if (paymentIndex !== -1) {
                appData.debtPayments.splice(paymentIndex, 1);

                // إعادة تعيين حالة الدين إذا كان مسدداً
                const debt = appData.debts.find(d => d.id === debtId);
                if (debt && debt.isPaid) {
                    const debtIndex = appData.debts.findIndex(d => d.id === debtId);
                    if (debtIndex !== -1) {
                        appData.debts[debtIndex] = {
                            ...appData.debts[debtIndex],
                            isPaid: false,
                            paidDate: null,
                            updatedAt: new Date().toISOString()
                        };
                    }
                }

                saveData();
                updateDebtsTable();
                showToast('تم حذف الدفعة بنجاح');
            }
        }
    }
}

// عرض مدفوعات الدين
function viewDebtPayments(debtId) {
    const debt = appData.debts.find(d => d.id === debtId);
    if (!debt) return;

    const customer = appData.customers.find(c => c.id === debt.customerId);
    if (!customer) return;

    // تبديل إلى علامة تبويب سجل المدفوعات
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    // إزالة الفئة النشطة من جميع الأزرار
    tabButtons.forEach(btn => btn.classList.remove('active'));

    // إضافة الفئة النشطة إلى زر سجل المدفوعات
    const paymentsTabBtn = document.querySelector('.tab-btn[data-tab="payments-history"]');
    paymentsTabBtn.classList.add('active');

    // إخفاء جميع المحتويات
    tabContents.forEach(content => content.classList.remove('active'));

    // إظهار محتوى سجل المدفوعات
    document.getElementById('payments-history-tab').classList.add('active');

    // تصفية جدول المدفوعات ليعرض فقط مدفوعات الدين المحدد
    const paymentsTable = document.getElementById('payments-history-table').querySelector('tbody');
    paymentsTable.innerHTML = '';

    const debtPayments = appData.debtPayments.filter(payment => payment.debtId === debtId);

    if (debtPayments.length === 0) {
        paymentsTable.innerHTML = '<tr><td colspan="6" class="text-center">لا توجد مدفوعات لهذا الدين</td></tr>';
        return;
    }

    debtPayments.sort((a, b) => new Date(b.date) - new Date(a.date)).forEach(payment => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${customer.name}</td>
            <td>${payment.amount.toFixed(2)}</td>
            <td>${formatDate(payment.date)}</td>
            <td>${payment.method}</td>
            <td>${payment.notes || '-'}</td>
            <td class="action-cell">
                <button class="action-btn edit" data-id="${payment.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete" data-id="${payment.id}" title="حذف"><i class="fas fa-trash"></i></button>
            </td>
        `;
        paymentsTable.appendChild(row);

        // إضافة مستمعي الأحداث للأزرار
        row.querySelector('.edit').addEventListener('click', () => editPayment(payment.id));
        row.querySelector('.delete').addEventListener('click', () => deletePayment(payment.id));
    });
}

// إعداد مستمعي الأحداث العامة
function setupEventListeners() {
    // مستمعي أحداث النوافذ المنبثقة
    window.addEventListener('click', (e) => {
        if (e.target.classList.contains('modal')) {
            closeModal(e.target.id);
        }
    });

    // تسجيل زيارة للزبون
    document.getElementById('record-visit-btn').addEventListener('click', () => {
        const customerId = document.getElementById('customer-info-modal').querySelector('.customer-info-container').getAttribute('data-id');
        const customer = appData.customers.find(c => c.id === customerId);
        if (customer) {
            // تحديث تاريخ آخر زيارة
            const today = new Date().toISOString().split('T')[0];
            customer.lastVisitDate = today;

            // حفظ البيانات وتحديث الواجهة
            saveData();
            updateCustomersTable();

            // تحديث تاريخ آخر زيارة في صفحة معلومات الزبون
            const lastVisitEl = document.getElementById('info-customer-last-visit');
            if (lastVisitEl) {
                lastVisitEl.textContent = formatDate(today);
            }

            // عرض رسالة نجاح
            showToast('تم تسجيل زيارة للزبون بنجاح');
        }
    });

    // تم نقل مستمعي أحداث الإشعارات إلى setupNotifications()

    // إضافة مستمع الحدث للوضع المظلم - تم نقله إلى setupDarkMode()
}

// تحديث قائمة النسخ الاحتياطية
function updateBackupsList() {
    if (!window.electronAPI) return;

    const backupsList = window.electronAPI.getBackupsList();
    const backupSelect = document.getElementById('backup-select');

    if (!backupSelect) return;

    backupSelect.innerHTML = '';

    if (backupsList.length === 0) {
        const option = document.createElement('option');
        option.value = '';
        option.textContent = 'لا توجد نسخ احتياطية';
        backupSelect.appendChild(option);

        const restoreBtn = document.getElementById('restore-backup-btn');
        if (restoreBtn) {
            restoreBtn.disabled = true;
        }
    } else {
        backupsList.forEach(backup => {
            const option = document.createElement('option');
            option.value = backup.name;
            const date = new Date(backup.date);
            option.textContent = `${backup.name} (${date.toLocaleString('ar-SA')})`;
            backupSelect.appendChild(option);
        });

        const restoreBtn = document.getElementById('restore-backup-btn');
        if (restoreBtn) {
            restoreBtn.disabled = false;
        }
    }
}

// تحميل البيانات عند بدء التطبيق
document.addEventListener('DOMContentLoaded', () => {
    // تحميل البيانات
    loadData();

    // تحديث التاريخ والوقت
    updateDateTime();
    setInterval(updateDateTime, 1000);

    // إضافة مستمعي الأحداث للتنقل
    setupNavigation();

    // إضافة مستمعي الأحداث للنماذج
    setupForms();

    // إضافة مستمعي الأحداث للبحث
    setupSearch();

    // إضافة مستمعي الأحداث للإعدادات
    setupSettings();

    // إضافة مستمعي الأحداث للتقويم
    setupCalendarNavigation();

    // إضافة مستمعي الأحداث لطباعة التقارير
    setupPrintReports();

    // إعداد نظام الإشعارات
    setupNotifications();

    // إعداد مستمعي الأحداث العامة
    setupEventListeners();

    // إعداد اختصارات لوحة المفاتيح
    initKeyboardShortcuts();

    // إعداد نظام الوضع المظلم (تم نقله هنا ليكون قبل setupDarkMode)
    // تم حذف checkDarkModePreference واستبدالها بـ setupDarkMode

    // فحص البطاقات والمواعيد والديون (النظام القديم)
    checkCardsForRenewal();
    checkUpcomingAppointments();
    checkDueDebts();

    // تشغيل نظام التنبيهات الذكي الجديد
    setTimeout(() => {
        startSmartAlertsScheduler();
    }, 3000); // تأخير 3 ثوان للتأكد من تحميل البيانات

    // إعداد نظام الوضع المظلم مع تأخير للتأكد من تحميل DOM
    setTimeout(() => {
        console.log('🔄 بدء إعداد نظام الوضع المظلم...');
        setupDarkMode();

        // تفعيل التبديل التلقائي للوضع (اختياري)
        autoToggleTheme();

        // مراقبة تغييرات الوضع
        observeThemeChanges();

        // اختبار الوضع المظلم
        testDarkModeSetup();
    }, 500);

    // فحص البطاقات والمواعيد والديون كل ساعة (النظام القديم)
    setInterval(() => {
        checkCardsForRenewal();
        checkUpcomingAppointments();
        checkDueDebts();
    }, 60 * 60 * 1000);
});

// تحديث التاريخ والوقت
function updateDateTime() {
    const now = new Date();
    document.getElementById('current-date').textContent = now.toLocaleDateString('ar-EG', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric', numberingSystem: 'latn' });
    document.getElementById('current-time').textContent = now.toLocaleTimeString('ar-EG', { numberingSystem: 'latn' });
}

// نظام اختصارات لوحة المفاتيح
function initKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // التحقق من أن المستخدم لا يكتب في حقل إدخال
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' || e.target.isContentEditable) {
            return;
        }

        // اختصارات مع Ctrl
        if (e.ctrlKey) {
            switch (e.key.toLowerCase()) {
                case 's': // Ctrl+S - حفظ
                    e.preventDefault();
                    saveData();
                    showToast('تم حفظ البيانات');
                    break;
                case 'n': // Ctrl+N - إضافة زبون جديد
                    e.preventDefault();
                    const addCustomerBtn = document.getElementById('add-customer-btn');
                    if (addCustomerBtn && addCustomerBtn.style.display !== 'none') {
                        addCustomerBtn.click();
                    }
                    break;
                case 'p': // Ctrl+P - طباعة
                    e.preventDefault();
                    window.print();
                    break;
                case 'f': // Ctrl+F - البحث
                    e.preventDefault();
                    const searchInput = document.querySelector('.search-container input:not([style*="display: none"])');
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.select();
                    }
                    break;
                case 'b': // Ctrl+B - إنشاء نسخة احتياطية
                    e.preventDefault();
                    createManualBackup();
                    break;
                case 'r': // Ctrl+R - تحديث البيانات
                    e.preventDefault();
                    refreshData();
                    break;
                case 't': // Ctrl+T - إرسال نسخة احتياطية إلى تيليجرام
                    e.preventDefault();
                    if (appData.settings.telegram?.enabled) {
                        sendBackupToTelegram('manual');
                    } else {
                        showToast('تيليجرام غير مفعل. اذهب إلى الإعدادات لتفعيله', false);
                    }
                    break;
            }
        }

        // اختصارات بدون Ctrl
        switch (e.key) {
            case 'Escape': // ESC - إغلاق النوافذ المنبثقة
                closeAllModals();
                break;
            case 'F1': // F1 - المساعدة
                e.preventDefault();
                showHelpModal();
                break;
            case 'F5': // F5 - تحديث البيانات
                e.preventDefault();
                refreshData();
                break;
        }

        // اختصارات الأرقام للتنقل السريع (Alt + رقم)
        if (e.altKey && !isNaN(e.key) && e.key !== '0') {
            e.preventDefault();
            const sectionIndex = parseInt(e.key) - 1;
            const navLinks = document.querySelectorAll('nav a[data-section]');
            if (navLinks[sectionIndex]) {
                navLinks[sectionIndex].click();
            }
        }
    });
}

// إنشاء نسخة احتياطية يدوية
function createManualBackup() {
    try {
        const timestamp = new Date().getTime();
        const backupName = `gasShopData_manual_backup_${timestamp}`;

        const backupInfo = {
            timestamp: timestamp,
            date: new Date().toISOString(),
            type: 'manual',
            dataSize: JSON.stringify(appData).length,
            recordsCount: {
                customers: appData.customers?.length || 0,
                vehicles: appData.vehicles?.length || 0,
                gasCards: appData.gasCards?.length || 0,
                appointments: appData.appointments?.length || 0,
                certificates: (appData.installationCertificates?.length || 0) + (appData.monitoringCertificates?.length || 0)
            }
        };

        const backupData = {
            info: backupInfo,
            data: appData
        };

        localStorage.setItem(backupName, JSON.stringify(backupData));

        showToast('تم إنشاء نسخة احتياطية يدوية بنجاح');
        console.log(`تم إنشاء نسخة احتياطية يدوية: ${backupName}`);

        addNotification(
            '💾 نسخة احتياطية يدوية',
            'تم إنشاء نسخة احتياطية يدوية من البيانات',
            'success'
        );

        // إرسال إلى تيليجرام إذا كان مفعلاً
        if (appData.settings.telegram.enabled) {
            sendBackupToTelegram('manual').catch(error => {
                console.error('خطأ في إرسال النسخة الاحتياطية إلى تيليجرام:', error);
            });
        }
    } catch (error) {
        console.error('خطأ في إنشاء النسخة الاحتياطية اليدوية:', error);
        showToast('فشل في إنشاء النسخة الاحتياطية', false);
    }
}

// إغلاق جميع النوافذ المنبثقة
function closeAllModals() {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (modal.style.display === 'block') {
            modal.style.display = 'none';
        }
    });

    // إغلاق القوائم المنسدلة
    const dropdowns = document.querySelectorAll('.dropdown-content');
    dropdowns.forEach(dropdown => {
        dropdown.style.display = 'none';
    });
}

// عرض نافذة المساعدة
function showHelpModal() {
    const helpContent = `
        <h3>🔧 اختصارات لوحة المفاتيح</h3>
        <div class="help-shortcuts">
            <div class="shortcut-group">
                <h4>اختصارات عامة:</h4>
                <p><kbd>Ctrl</kbd> + <kbd>S</kbd> - حفظ البيانات</p>
                <p><kbd>Ctrl</kbd> + <kbd>N</kbd> - إضافة زبون جديد</p>
                <p><kbd>Ctrl</kbd> + <kbd>P</kbd> - طباعة</p>
                <p><kbd>Ctrl</kbd> + <kbd>F</kbd> - البحث</p>
                <p><kbd>Ctrl</kbd> + <kbd>B</kbd> - نسخة احتياطية</p>
                <p><kbd>Ctrl</kbd> + <kbd>R</kbd> - تحديث البيانات</p>
                <p><kbd>Ctrl</kbd> + <kbd>T</kbd> - إرسال إلى تيليجرام</p>
            </div>
            <div class="shortcut-group">
                <h4>اختصارات التنقل:</h4>
                <p><kbd>Alt</kbd> + <kbd>1</kbd> - لوحة التحكم</p>
                <p><kbd>Alt</kbd> + <kbd>2</kbd> - الزبائن</p>
                <p><kbd>Alt</kbd> + <kbd>3</kbd> - بطاقات الغاز</p>
                <p><kbd>Alt</kbd> + <kbd>4</kbd> - المواعيد</p>
                <p><kbd>Alt</kbd> + <kbd>5</kbd> - الشهادات</p>
            </div>
            <div class="shortcut-group">
                <h4>اختصارات أخرى:</h4>
                <p><kbd>Esc</kbd> - إغلاق النوافذ</p>
                <p><kbd>F1</kbd> - المساعدة</p>
                <p><kbd>F5</kbd> - تحديث البيانات</p>
            </div>
        </div>
    `;

    showToast(helpContent, true, 8000);
}

// تحديث البيانات
function refreshData() {
    updateDashboard();
    updateAllTables();
    updateCharts();
    showToast('تم تحديث البيانات');
}

// ===============================
// وظائف تيليجرام للنسخ الاحتياطي
// ===============================

// إرسال رسالة إلى تيليجرام
async function sendTelegramMessage(message) {
    const settings = appData.settings.telegram;

    if (!settings.enabled || !settings.botToken || !settings.chatId) {
        console.log('إعدادات تيليجرام غير مكتملة');
        return false;
    }

    try {
        const url = `https://api.telegram.org/bot${settings.botToken}/sendMessage`;
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                chat_id: settings.chatId,
                text: message,
                parse_mode: 'HTML'
            })
        });

        const result = await response.json();
        return result.ok;
    } catch (error) {
        console.error('خطأ في إرسال رسالة تيليجرام:', error);
        return false;
    }
}

// إرسال ملف إلى تيليجرام
async function sendTelegramDocument(fileName, fileContent, caption = '') {
    const settings = appData.settings.telegram;

    if (!settings.enabled || !settings.botToken || !settings.chatId) {
        console.log('إعدادات تيليجرام غير مكتملة');
        return false;
    }

    try {
        // تحويل البيانات إلى Blob
        const blob = new Blob([fileContent], { type: 'application/json' });

        // إنشاء FormData
        const formData = new FormData();
        formData.append('chat_id', settings.chatId);
        formData.append('document', blob, fileName);
        if (caption) {
            formData.append('caption', caption);
            formData.append('parse_mode', 'HTML');
        }

        const url = `https://api.telegram.org/bot${settings.botToken}/sendDocument`;
        const response = await fetch(url, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        return result.ok;
    } catch (error) {
        console.error('خطأ في إرسال ملف تيليجرام:', error);
        return false;
    }
}

// إرسال نسخة احتياطية إلى تيليجرام
async function sendBackupToTelegram(backupType = 'manual') {
    try {
        const timestamp = new Date().getTime();
        const date = new Date();
        const dateStr = date.toLocaleDateString('ar-SA');
        const timeStr = date.toLocaleTimeString('ar-SA');

        // إنشاء معلومات النسخة الاحتياطية
        const backupInfo = {
            timestamp: timestamp,
            date: date.toISOString(),
            type: backupType,
            version: '2.2.0',
            dataSize: JSON.stringify(appData).length,
            recordsCount: {
                customers: appData.customers?.length || 0,
                vehicles: appData.vehicles?.length || 0,
                gasCards: appData.gasCards?.length || 0,
                appointments: appData.appointments?.length || 0,
                certificates: (appData.installationCertificates?.length || 0) + (appData.monitoringCertificates?.length || 0),
                debts: appData.debts?.length || 0,
                inventory: appData.inventory?.length || 0
            }
        };

        const backupData = {
            info: backupInfo,
            data: appData
        };

        // إنشاء اسم الملف
        const fileName = `backup_${backupType}_${date.getFullYear()}-${(date.getMonth()+1).toString().padStart(2,'0')}-${date.getDate().toString().padStart(2,'0')}_${date.getHours().toString().padStart(2,'0')}-${date.getMinutes().toString().padStart(2,'0')}.json`;

        // إنشاء محتوى الملف
        const fileContent = JSON.stringify(backupData, null, 2);

        // إنشاء رسالة التوضيح
        const caption = `
🏢 <b>نسخة احتياطية - مؤسسة وقود المستقبل</b>

📅 <b>التاريخ:</b> ${dateStr}
🕐 <b>الوقت:</b> ${timeStr}
📦 <b>النوع:</b> ${backupType === 'manual' ? 'يدوية' : 'تلقائية'}
💾 <b>حجم البيانات:</b> ${(fileContent.length / 1024).toFixed(2)} KB

📊 <b>إحصائيات البيانات:</b>
👥 الزبائن: ${backupInfo.recordsCount.customers}
🚗 المركبات: ${backupInfo.recordsCount.vehicles}
🎫 بطاقات الغاز: ${backupInfo.recordsCount.gasCards}
📅 المواعيد: ${backupInfo.recordsCount.appointments}
📜 الشهادات: ${backupInfo.recordsCount.certificates}
💰 الديون: ${backupInfo.recordsCount.debts}
📦 المخزون: ${backupInfo.recordsCount.inventory}

✅ تم إنشاء النسخة الاحتياطية بنجاح
        `.trim();

        // إرسال الملف إلى تيليجرام
        const success = await sendTelegramDocument(fileName, fileContent, caption);

        if (success) {
            showToast('تم إرسال النسخة الاحتياطية إلى تيليجرام بنجاح');
            addNotification(
                '📱 تيليجرام',
                'تم إرسال نسخة احتياطية إلى تيليجرام',
                'success'
            );
            return true;
        } else {
            showToast('فشل في إرسال النسخة الاحتياطية إلى تيليجرام', false);
            return false;
        }
    } catch (error) {
        console.error('خطأ في إرسال النسخة الاحتياطية إلى تيليجرام:', error);
        showToast('خطأ في إرسال النسخة الاحتياطية إلى تيليجرام', false);
        return false;
    }
}

// اختبار اتصال تيليجرام
async function testTelegramConnection() {
    const settings = appData.settings.telegram;

    if (!settings.botToken || !settings.chatId) {
        showToast('يرجى إدخال معلومات البوت أولاً', false);
        return false;
    }

    const testMessage = `
🤖 <b>اختبار الاتصال - مؤسسة وقود المستقبل</b>

✅ تم الاتصال بنجاح!
📅 التاريخ: ${new Date().toLocaleDateString('ar-SA')}
🕐 الوقت: ${new Date().toLocaleTimeString('ar-SA')}

🔧 النظام جاهز لإرسال النسخ الاحتياطية
    `;

    const success = await sendTelegramMessage(testMessage);

    if (success) {
        showToast('تم الاتصال بتيليجرام بنجاح!');
        return true;
    } else {
        showToast('فشل في الاتصال بتيليجرام. تحقق من المعلومات', false);
        return false;
    }
}

// إرسال تقرير يومي إلى تيليجرام
async function sendDailyReportToTelegram() {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];

    // حساب إحصائيات اليوم
    const todayAppointments = appData.appointments.filter(app => app.date === todayStr);
    const todaySales = appData.sales.filter(sale => sale.date === todayStr);
    const todayPurchases = appData.purchases.filter(purchase => purchase.date === todayStr);

    // حساب المبالغ
    const totalSales = todaySales.reduce((sum, sale) => sum + (sale.total || 0), 0);
    const totalPurchases = todayPurchases.reduce((sum, purchase) => sum + (purchase.total || 0), 0);

    // حساب الديون المستحقة
    const overdueDebts = appData.debts.filter(debt => {
        const dueDate = new Date(debt.dueDate);
        return dueDate <= today && debt.status === 'pending';
    });

    const report = `
📊 <b>التقرير اليومي - مؤسسة وقود المستقبل</b>

📅 <b>التاريخ:</b> ${today.toLocaleDateString('ar-SA')}

📈 <b>إحصائيات اليوم:</b>
📅 المواعيد: ${todayAppointments.length}
💰 المبيعات: ${formatCurrency(totalSales)}
🛒 المشتريات: ${formatCurrency(totalPurchases)}
⚠️ الديون المستحقة: ${overdueDebts.length}

📊 <b>إحصائيات عامة:</b>
👥 إجمالي الزبائن: ${appData.customers.length}
🎫 بطاقات الغاز: ${appData.gasCards.length}
📜 الشهادات: ${(appData.installationCertificates?.length || 0) + (appData.monitoringCertificates?.length || 0)}

${overdueDebts.length > 0 ? `\n⚠️ <b>تنبيه:</b> يوجد ${overdueDebts.length} دين مستحق` : '✅ لا توجد ديون مستحقة'}
    `;

    return await sendTelegramMessage(report);
}

// تنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount || 0);
}

// تم حذف الدوال القديمة للوضع المظلم واستبدالها بالنظام الجديد في setupDarkMode()

// إضافة دوال للوضع المظلم إلى النطاق العام للاختبار
window.darkModeUtils = {
    enable: enableDarkMode,
    disable: enableLightMode,
    toggle: function() {
        if (document.body.classList.contains('dark-mode')) {
            enableLightMode();
        } else {
            enableDarkMode();
        }
    },
    test: testDarkModeSetup,
    reset: resetDarkMode,
    status: function() {
        return {
            isDarkMode: document.body.classList.contains('dark-mode'),
            savedTheme: localStorage.getItem('theme'),
            toggleButton: !!document.getElementById('dark-mode-toggle')
        };
    }
};

// إعداد التنقل
function setupNavigation() {
    const navLinks = document.querySelectorAll('nav a');
    navLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();

            // إزالة الفئة النشطة من جميع الروابط
            navLinks.forEach(l => l.classList.remove('active'));

            // إضافة الفئة النشطة إلى الرابط المنقر عليه
            link.classList.add('active');

            // إخفاء جميع الأقسام
            document.querySelectorAll('main section').forEach(section => {
                section.classList.remove('active-section');
            });

            // إظهار القسم المطلوب
            const sectionId = link.getAttribute('data-section');
            document.getElementById(sectionId).classList.add('active-section');

            // تحديث التقويم إذا كان قسم المواعيد
            if (sectionId === 'appointments') {
                updateCalendar();
            }

            // تحديث جدول الإرسال إذا كان قسم جدول الإرسال
            if (sectionId === 'transmission-table' && typeof transmissionManager !== 'undefined') {
                transmissionManager.updateTable();
                transmissionManager.updateSummary();
            }
        });
    });
}

// إعداد النماذج
function setupForms() {
    // نموذج بطاقة الغاز
    setupGasCardForm();

    // نموذج الموعد
    setupAppointmentForm();

    // نموذج الزبون
    setupCustomerForm();

    // نموذج الدين
    setupDebtForm();

    // نموذج الدفعة
    setupPaymentForm();

    // نموذج المورد
    setupSupplierForm();

    // نموذج الصنف
    setupItemForm();

    // إعداد فلاتر المخزون
    setupInventoryFilters();

    // نموذج المبيعات
    setupSalesForm();

    // نموذج المشتريات
    setupPurchasesForm();

    // إعداد فلاتر الديون
    setupDebtFilters();


}

// إعداد نموذج بطاقة الغاز
function setupGasCardForm() {
    const addCardBtn = document.getElementById('add-card-btn');
    const cardModal = document.getElementById('card-modal');
    const cardForm = document.getElementById('card-form');
    const cancelCardBtn = document.getElementById('cancel-card');
    const closeBtn = cardModal.querySelector('.close');

    // فتح النموذج عند النقر على زر الإضافة
    addCardBtn.addEventListener('click', () => {
        document.getElementById('card-modal-title').textContent = 'إضافة بطاقة غاز جديدة';
        cardForm.reset();
        document.getElementById('card-id').value = '';

        // ملء قائمة الزبائن
        const customerSelect = document.getElementById('card-customer');
        customerSelect.innerHTML = '<option value="">اختر زبون</option>';
        appData.customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            customerSelect.appendChild(option);
        });

        // تعيين تاريخ اليوم كتاريخ الإصدار
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('card-issue-date').value = today;

        cardModal.style.display = 'block';
    });

    // تحديث قائمة السيارات عند اختيار زبون
    document.getElementById('card-customer').addEventListener('change', (e) => {
        const customerId = e.target.value;
        const vehicleSelect = document.getElementById('card-vehicle');
        vehicleSelect.innerHTML = '<option value="">اختر سيارة</option>';

        if (customerId) {
            const customerVehicles = appData.vehicles.filter(v => v.customerId === customerId);
            customerVehicles.forEach(vehicle => {
                const option = document.createElement('option');
                option.value = vehicle.id;
                option.textContent = vehicle.plateNumber;
                vehicleSelect.appendChild(option);
            });
        }
    });

    // إغلاق النموذج
    cancelCardBtn.addEventListener('click', () => {
        cardModal.style.display = 'none';
    });

    closeBtn.addEventListener('click', () => {
        cardModal.style.display = 'none';
    });

    // معالجة تقديم النموذج
    cardForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const cardId = document.getElementById('card-id').value;
        const customerId = document.getElementById('card-customer').value;
        const vehicleId = document.getElementById('card-vehicle').value;
        const cardNumber = document.getElementById('card-number').value;
        const issueDate = document.getElementById('card-issue-date').value;
        const expiryDate = document.getElementById('card-expiry-date').value;
        const notes = document.getElementById('card-notes').value;

        if (!customerId || !vehicleId || !cardNumber || !issueDate || !expiryDate) {
            showToast('يرجى ملء جميع الحقول المطلوبة', false);
            return;
        }

        if (new Date(expiryDate) <= new Date(issueDate)) {
            showToast('يجب أن يكون تاريخ الانتهاء بعد تاريخ الإصدار', false);
            return;
        }

        if (cardId) {
            // تحديث بطاقة موجودة
            const cardIndex = appData.gasCards.findIndex(c => c.id === cardId);
            if (cardIndex !== -1) {
                appData.gasCards[cardIndex] = {
                    ...appData.gasCards[cardIndex],
                    customerId,
                    vehicleId,
                    cardNumber,
                    issueDate,
                    expiryDate,
                    notes
                };
                showToast('تم تحديث البطاقة بنجاح');
            }
        } else {
            // إضافة بطاقة جديدة
            const newCard = {
                id: generateId(),
                customerId,
                vehicleId,
                cardNumber,
                issueDate,
                expiryDate,
                notes,
                createdAt: new Date().toISOString()
            };
            appData.gasCards.push(newCard);

            // إضافة العملية إلى جدول الإرسال تلقائياً
            if (typeof addCardRenewalToTransmissionTable === 'function') {
                addCardRenewalToTransmissionTable(newCard);
            }

            showToast('تمت إضافة البطاقة بنجاح');
        }

        // حفظ البيانات وتحديث الواجهة
        saveData();
        updateDashboard();
        updateGasCardsTable();

        // إغلاق النموذج
        cardModal.style.display = 'none';
    });
}

// تحرير بطاقة غاز
function editGasCard(cardId) {
    const card = appData.gasCards.find(c => c.id === cardId);
    if (!card) return;

    document.getElementById('card-modal-title').textContent = 'تعديل بطاقة الغاز';
    document.getElementById('card-id').value = card.id;

    // ملء قائمة الزبائن
    const customerSelect = document.getElementById('card-customer');
    customerSelect.innerHTML = '<option value="">اختر زبون</option>';
    appData.customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = customer.name;
        if (customer.id === card.customerId) {
            option.selected = true;
        }
        customerSelect.appendChild(option);
    });

    // ملء قائمة السيارات
    const vehicleSelect = document.getElementById('card-vehicle');
    vehicleSelect.innerHTML = '<option value="">اختر سيارة</option>';
    const customerVehicles = appData.vehicles.filter(v => v.customerId === card.customerId);
    customerVehicles.forEach(vehicle => {
        const option = document.createElement('option');
        option.value = vehicle.id;
        option.textContent = vehicle.plateNumber;
        if (vehicle.id === card.vehicleId) {
            option.selected = true;
        }
        vehicleSelect.appendChild(option);
    });

    document.getElementById('card-number').value = card.cardNumber;
    document.getElementById('card-issue-date').value = card.issueDate;
    document.getElementById('card-expiry-date').value = card.expiryDate;
    document.getElementById('card-notes').value = card.notes || '';

    document.getElementById('card-modal').style.display = 'block';
}

// حذف بطاقة غاز
function deleteGasCard(cardId) {
    if (confirm('هل أنت متأكد من حذف هذه البطاقة؟')) {
        const cardIndex = appData.gasCards.findIndex(c => c.id === cardId);
        if (cardIndex !== -1) {
            appData.gasCards.splice(cardIndex, 1);
            saveData();
            updateDashboard();
            updateGasCardsTable();
            showToast('تم حذف البطاقة بنجاح');
        }
    }
}

// إعداد نموذج الموعد
function setupAppointmentForm() {
    const addAppointmentBtn = document.getElementById('add-appointment-btn');
    const appointmentModal = document.getElementById('appointment-modal');
    const appointmentForm = document.getElementById('appointment-form');
    const cancelAppointmentBtn = document.getElementById('cancel-appointment');
    const closeBtn = appointmentModal.querySelector('.close');

    // فتح النموذج عند النقر على زر الإضافة
    addAppointmentBtn.addEventListener('click', () => {
        document.getElementById('appointment-modal-title').textContent = 'إضافة موعد جديد';
        appointmentForm.reset();
        document.getElementById('appointment-id').value = '';

        // ملء قائمة الزبائن
        const customerSelect = document.getElementById('appointment-customer');
        customerSelect.innerHTML = '<option value="">اختر زبون</option>';
        appData.customers.forEach(customer => {
            const option = document.createElement('option');
            option.value = customer.id;
            option.textContent = customer.name;
            customerSelect.appendChild(option);
        });

        // تعيين تاريخ اليوم
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('appointment-date').value = today;

        appointmentModal.style.display = 'block';
    });

    // تحديث قائمة السيارات عند اختيار زبون
    document.getElementById('appointment-customer').addEventListener('change', (e) => {
        const customerId = e.target.value;
        const vehicleSelect = document.getElementById('appointment-vehicle');
        vehicleSelect.innerHTML = '<option value="">اختر سيارة</option>';

        if (customerId) {
            const customerVehicles = appData.vehicles.filter(v => v.customerId === customerId);
            customerVehicles.forEach(vehicle => {
                const option = document.createElement('option');
                option.value = vehicle.id;
                option.textContent = vehicle.plateNumber;
                vehicleSelect.appendChild(option);
            });
        }
    });

    // إغلاق النموذج
    cancelAppointmentBtn.addEventListener('click', () => {
        appointmentModal.style.display = 'none';
    });

    closeBtn.addEventListener('click', () => {
        appointmentModal.style.display = 'none';
    });

    // معالجة تقديم النموذج
    appointmentForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const appointmentId = document.getElementById('appointment-id').value;
        const date = document.getElementById('appointment-date').value;
        const time = document.getElementById('appointment-time').value;
        const customerId = document.getElementById('appointment-customer').value;
        const vehicleId = document.getElementById('appointment-vehicle').value;
        const service = document.getElementById('appointment-service').value;
        const notes = document.getElementById('appointment-notes').value;

        if (!date || !time || !customerId || !vehicleId || !service) {
            showToast('يرجى ملء جميع الحقول المطلوبة', false);
            return;
        }

        if (appointmentId) {
            // تحديث موعد موجود
            const appointmentIndex = appData.appointments.findIndex(a => a.id === appointmentId);
            if (appointmentIndex !== -1) {
                appData.appointments[appointmentIndex] = {
                    ...appData.appointments[appointmentIndex],
                    date,
                    time,
                    customerId,
                    vehicleId,
                    service,
                    notes,
                    updatedAt: new Date().toISOString()
                };

                // تحديث تاريخ آخر زيارة للزبون
                const customer = appData.customers.find(c => c.id === customerId);
                if (customer) {
                    // تحديث فقط إذا كان تاريخ الموعد أحدث من تاريخ آخر زيارة
                    if (!customer.lastVisitDate || new Date(date) > new Date(customer.lastVisitDate)) {
                        customer.lastVisitDate = date;
                    }
                }

                showToast('تم تحديث الموعد بنجاح');
            }
        } else {
            // إضافة موعد جديد
            const newAppointment = {
                id: generateId(),
                date,
                time,
                customerId,
                vehicleId,
                service,
                notes,
                createdAt: new Date().toISOString()
            };
            appData.appointments.push(newAppointment);

            // تحديث تاريخ آخر زيارة للزبون
            const customer = appData.customers.find(c => c.id === customerId);
            if (customer) {
                // تحديث فقط إذا كان تاريخ الموعد أحدث من تاريخ آخر زيارة
                if (!customer.lastVisitDate || new Date(date) > new Date(customer.lastVisitDate)) {
                    customer.lastVisitDate = date;
                }
            }

            showToast('تمت إضافة الموعد بنجاح');
        }

        // حفظ البيانات وتحديث الواجهة
        saveData();
        updateDashboard();
        updateAppointmentsTable();
        updateCustomersTable(); // تحديث جدول الزبائن أيضًا
        updateCalendar();

        // إغلاق النموذج
        appointmentModal.style.display = 'none';
    });
}

// تحرير موعد
function editAppointment(appointmentId) {
    const appointment = appData.appointments.find(a => a.id === appointmentId);
    if (!appointment) return;

    document.getElementById('appointment-modal-title').textContent = 'تعديل الموعد';
    document.getElementById('appointment-id').value = appointment.id;
    document.getElementById('appointment-date').value = appointment.date;
    document.getElementById('appointment-time').value = appointment.time;

    // ملء قائمة الزبائن
    const customerSelect = document.getElementById('appointment-customer');
    customerSelect.innerHTML = '<option value="">اختر زبون</option>';
    appData.customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = customer.name;
        if (customer.id === appointment.customerId) {
            option.selected = true;
        }
        customerSelect.appendChild(option);
    });

    // ملء قائمة السيارات
    const vehicleSelect = document.getElementById('appointment-vehicle');
    vehicleSelect.innerHTML = '<option value="">اختر سيارة</option>';
    const customerVehicles = appData.vehicles.filter(v => v.customerId === appointment.customerId);
    customerVehicles.forEach(vehicle => {
        const option = document.createElement('option');
        option.value = vehicle.id;
        option.textContent = vehicle.plateNumber;
        if (vehicle.id === appointment.vehicleId) {
            option.selected = true;
        }
        vehicleSelect.appendChild(option);
    });

    document.getElementById('appointment-service').value = appointment.service;
    document.getElementById('appointment-notes').value = appointment.notes || '';

    document.getElementById('appointment-modal').style.display = 'block';
}

// حذف موعد
function deleteAppointment(appointmentId) {
    if (confirm('هل أنت متأكد من حذف هذا الموعد؟')) {
        const appointmentIndex = appData.appointments.findIndex(a => a.id === appointmentId);
        if (appointmentIndex !== -1) {
            appData.appointments.splice(appointmentIndex, 1);
            saveData();
            updateDashboard();
            updateAppointmentsTable();
            updateCalendar();
            showToast('تم حذف الموعد بنجاح');
        }
    }
}

// إعداد نموذج الزبون
function setupCustomerForm() {
    const addCustomerBtn = document.getElementById('add-customer-btn');
    const customerModal = document.getElementById('customer-modal');
    const customerForm = document.getElementById('customer-form');
    const cancelCustomerBtn = document.getElementById('cancel-customer');
    const closeBtn = customerModal.querySelector('.close');
    const addVehicleBtn = document.getElementById('add-vehicle');

    // فتح النموذج عند النقر على زر الإضافة
    addCustomerBtn.addEventListener('click', () => {
        document.getElementById('customer-modal-title').textContent = 'إضافة زبون جديد';
        customerForm.reset();
        document.getElementById('customer-id').value = '';

        // تعيين القيم الافتراضية

        // إضافة مستمع لتغيير تسمية تاريخ العملية
        const operationTypeSelect = document.getElementById('operation-type');
        const operationDateLabel = document.getElementById('operation-date-label');

        if (operationTypeSelect && operationDateLabel) {
            operationTypeSelect.addEventListener('change', function() {
                const selectedType = this.value;
                switch(selectedType) {
                    case 'تركيب':
                        operationDateLabel.textContent = 'تاريخ التركيب / Date d\'Installation:';
                        break;
                    case 'مراقبة':
                        operationDateLabel.textContent = 'تاريخ المراقبة / Date de Contrôle:';
                        break;
                    case 'تجديد':
                        operationDateLabel.textContent = 'تاريخ التجديد / Date de Renouvellement:';
                        break;
                    default:
                        operationDateLabel.textContent = 'تاريخ العملية / Date d\'Opération:';
                        break;
                }
            });
        }

        customerModal.style.display = 'block';
    });

    // إغلاق النموذج
    cancelCustomerBtn.addEventListener('click', () => {
        customerModal.style.display = 'none';
    });

    closeBtn.addEventListener('click', () => {
        customerModal.style.display = 'none';
    });

    // معالجة تقديم النموذج
    customerForm.addEventListener('submit', (e) => {
        e.preventDefault();

        const customerId = document.getElementById('customer-id').value;
        const name = document.getElementById('customer-name').value;
        const phone = document.getElementById('customer-phone').value;
        const address = document.getElementById('customer-address').value;
        const notes = document.getElementById('customer-notes').value;

        // بيانات السيارة
        const plateNumber = document.getElementById('vehicle-plate-number').value;
        const brand = document.getElementById('vehicle-brand').value;
        const model = document.getElementById('vehicle-model').value;
        const chassisNumber = document.getElementById('vehicle-chassis-number').value;
        const year = document.getElementById('vehicle-year').value;

        // بيانات الخزان
        const tankType = document.getElementById('tank-type').value;
        const tankBrand = document.getElementById('tank-brand').value;
        const tankSerialNumber = document.getElementById('tank-serial-number').value;
        const tankCapacity = document.getElementById('tank-capacity').value;
        const tankManufactureDate = document.getElementById('tank-manufacture-date').value;

        if (!name || !phone || !address || !plateNumber || !brand || !model || !tankType || !tankBrand || !tankSerialNumber || !tankCapacity) {
            showToast('يرجى ملء جميع الحقول المطلوبة المميزة بـ *', false);
            return;
        }

        // التحقق من وجود الزبون (للزبائن الجدد فقط)
        if (!customerId) {
            const existingCustomer = appData.customers.find(c =>
                c.phone === phone ||
                (email && c.email === email)
            );

            if (existingCustomer) {
                if (confirm(`يوجد زبون مسجل بنفس رقم الهاتف أو البريد الإلكتروني: ${existingCustomer.name}. هل تريد تعديل بياناته بدلاً من إضافة زبون جديد؟`)) {
                    // فتح نموذج تعديل الزبون الموجود
                    editCustomer(existingCustomer.id);
                    return;
                }
            }
        }

        // إنشاء بيانات السيارة والخزان
        const vehicleId = customerId ?
            (appData.vehicles.find(v => v.customerId === customerId)?.id || generateId()) :
            generateId();
        const tankId = customerId ?
            (appData.gasTanks.find(t => t.vehicleId === vehicleId)?.id || generateId()) :
            generateId();

        const vehicle = {
            id: vehicleId,
            plateNumber,
            brand,
            model,
            chassisNumber,
            year: year ? parseInt(year) : null,
            customerId: customerId || generateId(),
            createdAt: customerId ?
                (appData.vehicles.find(v => v.id === vehicleId)?.createdAt || new Date().toISOString()) :
                new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        const gasTank = {
            id: tankId,
            vehicleId: vehicleId,
            type: tankType,
            brand: tankBrand,
            serialNumber: tankSerialNumber,
            capacity: parseFloat(tankCapacity),
            manufactureDate: tankManufactureDate,
            shape: tankType, // شكل الخزان
            createdAt: customerId ?
                (appData.gasTanks.find(t => t.id === tankId)?.createdAt || new Date().toISOString()) :
                new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        const vehicles = [vehicle];
        const gasTanks = [gasTank];

        // التحقق من وجود سيارة بنفس رقم اللوحة
        for (const vehicle of vehicles) {
            if (!vehicle.id || !appData.vehicles.some(v => v.id === vehicle.id)) { // فقط للسيارات الجديدة
                const existingVehicle = appData.vehicles.find(v =>
                    v.plateNumber === vehicle.plateNumber &&
                    v.customerId !== (customerId || 'new')
                );

                if (existingVehicle) {
                    const existingCustomer = appData.customers.find(c => c.id === existingVehicle.customerId);
                    if (!confirm(`السيارة برقم لوحة ${vehicle.plateNumber} مسجلة بالفعل للزبون ${existingCustomer ? existingCustomer.name : 'غير معروف'}. هل تريد المتابعة؟`)) {
                        return;
                    }
                }
            }
        }

        if (customerId) {
            // تحديث زبون موجود
            const customerIndex = appData.customers.findIndex(c => c.id === customerId);
            if (customerIndex !== -1) {
                appData.customers[customerIndex] = {
                    ...appData.customers[customerIndex],
                    name,
                    phone,
                    address,
                    notes,
                    updatedAt: new Date().toISOString()
                };

                // تحديث السيارات والخزانات
                // حذف السيارات القديمة للزبون
                const oldVehicleIds = appData.vehicles.filter(v => v.customerId === customerId).map(v => v.id);
                appData.vehicles = appData.vehicles.filter(v => v.customerId !== customerId);
                // حذف الخزانات القديمة المرتبطة بالسيارات القديمة
                appData.gasTanks = appData.gasTanks.filter(tank => !oldVehicleIds.includes(tank.vehicleId));
                // إضافة السيارات والخزانات الجديدة
                appData.vehicles.push(...vehicles);
                appData.gasTanks.push(...gasTanks);

                showToast('تم تحديث بيانات الزبون بنجاح');
            }
        } else {
            // إضافة زبون جديد
            const newCustomerId = generateId();
            const newCustomer = {
                id: newCustomerId,
                name,
                phone,
                address,
                notes,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };

            // تحديث معرف الزبون في السيارات
            vehicles.forEach(vehicle => {
                vehicle.customerId = newCustomerId;
            });

            appData.customers.push(newCustomer);
            appData.vehicles.push(...vehicles);
            appData.gasTanks.push(...gasTanks);

            // معالجة نوع العملية المطلوبة
            const operationType = document.getElementById('operation-type')?.value;
            const operationDate = document.getElementById('operation-date')?.value || new Date().toISOString().split('T')[0];
            const operationNotes = document.getElementById('operation-notes')?.value || '';

            if (operationType) {
                if (operationType === 'تجديد') {
                    // إضافة بطاقة غاز جديدة مع الملاحظات
                    const newGasCard = {
                        id: 'card-' + Date.now(),
                        customerId: newCustomerId,
                        vehicleId: vehicles[0]?.id,
                        cardNumber: 'GC-' + Date.now(),
                        vehicleNumber: vehicles[0]?.plateNumber,
                        customerName: newCustomer.name,
                        tankNumber: gasTanks[0]?.serialNumber || '',
                        serialNumber: gasTanks[0]?.serialNumber || '',
                        issueDate: operationDate,
                        expiryDate: new Date(new Date(operationDate).getTime() + (365 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0],
                        notes: operationNotes, // الملاحظات ستظهر في إدارة بطاقات الغاز
                        operationType: 'تجديد بطاقة', // نوع العملية للمرجع
                        operationDate: operationDate, // تاريخ العملية
                        createdAt: new Date().toISOString()
                    };
                    appData.gasCards.push(newGasCard);
                    showToast('تمت إضافة الزبون وبطاقة الغاز بنجاح - الملاحظات محفوظة في إدارة البطاقات');
                } else if (operationType === 'تركيب' || operationType === 'مراقبة') {
                    // إضافة العملية إلى جدول الإرسال
                    if (typeof transmissionManager !== 'undefined') {
                        const transmissionEntry = {
                            type: operationType,
                            tankNumber: gasTanks[0]?.serialNumber || '',
                            carType: vehicles[0]?.type + ' ' + vehicles[0]?.model || '',
                            serialNumber: gasTanks[0]?.serialNumber || '',
                            registrationNumber: vehicles[0]?.plateNumber,
                            ownerName: newCustomer.name,
                            phoneNumber: newCustomer.phone,
                            operationDate: operationDate,
                            notes: operationNotes,
                            source: 'customer_form',
                            sourceId: newCustomerId
                        };
                        transmissionManager.addEntry(transmissionEntry);
                    }
                    showToast(`تمت إضافة الزبون وعملية ${operationType} بنجاح`);
                } else {
                    showToast('تمت إضافة الزبون بنجاح');
                }
            } else {
                showToast('تمت إضافة الزبون بنجاح');
            }
        }

        // حفظ البيانات وتحديث الواجهة
        saveData();
        updateDashboard();
        updateCustomersTable();

        // إغلاق النموذج
        customerModal.style.display = 'none';
    });
}

// إضافة حقل سيارة مع معلومات الخزان
function addVehicleField(vehicle = null, gasTank = null) {
    const container = document.getElementById('vehicles-container');
    const vehicleId = vehicle ? vehicle.id : '';
    const tankId = gasTank ? gasTank.id : '';

    const vehicleItem = document.createElement('div');
    vehicleItem.className = 'vehicle-item';
    if (vehicleId) {
        vehicleItem.setAttribute('data-id', vehicleId);
    }
    if (tankId) {
        vehicleItem.setAttribute('data-tank-id', tankId);
    }

    vehicleItem.innerHTML = `
        <div class="vehicle-header">
            <h4>سيارة وخزان ${container.children.length + 1}</h4>
            <button type="button" class="remove-vehicle btn danger" title="حذف السيارة والخزان">
                <i class="fas fa-trash"></i>
            </button>
        </div>

        <!-- معلومات السيارة -->
        <div class="vehicle-section">
            <h5><i class="fas fa-car"></i> معلومات السيارة</h5>
            <div class="form-row">
                <div class="form-group">
                    <label>رقم اللوحة:</label>
                    <input type="text" class="vehicle-plate" value="${vehicle ? vehicle.plateNumber : ''}" required>
                </div>
                <div class="form-group">
                    <label>الماركة:</label>
                    <input type="text" class="vehicle-brand" value="${vehicle ? vehicle.brand : ''}" required>
                </div>
                <div class="form-group">
                    <label>الطراز:</label>
                    <input type="text" class="vehicle-model" value="${vehicle ? vehicle.model : ''}" required>
                </div>
                <div class="form-group">
                    <label>سنة الصنع:</label>
                    <input type="number" class="vehicle-year" value="${vehicle ? vehicle.year : ''}" min="1900" max="${new Date().getFullYear()}" required>
                </div>
            </div>
        </div>

        <!-- معلومات الخزان -->
        <div class="tank-section">
            <h5><i class="fas fa-gas-pump"></i> معلومات الخزان</h5>
            <div class="form-row">
                <div class="form-group">
                    <label>نوع الخزان:</label>
                    <select class="tank-type" required>
                        <option value="">اختر نوع الخزان</option>
                        <option value="أسطواني" ${gasTank && gasTank.type === 'أسطواني' ? 'selected' : ''}>أسطواني</option>
                        <option value="دائري" ${gasTank && gasTank.type === 'دائري' ? 'selected' : ''}>دائري</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>الرقم التسلسلي للخزان:</label>
                    <input type="text" class="tank-serial" value="${gasTank ? gasTank.serialNumber : ''}" required>
                </div>
                <div class="form-group">
                    <label>حجم الخزان (لتر):</label>
                    <input type="number" class="tank-capacity" value="${gasTank ? gasTank.capacity : ''}" min="1" step="0.1" required>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>شهر التصنيع:</label>
                    <select class="tank-manufacture-month" required>
                        <option value="">اختر الشهر</option>
                        <option value="1" ${gasTank && gasTank.manufactureMonth === 1 ? 'selected' : ''}>يناير</option>
                        <option value="2" ${gasTank && gasTank.manufactureMonth === 2 ? 'selected' : ''}>فبراير</option>
                        <option value="3" ${gasTank && gasTank.manufactureMonth === 3 ? 'selected' : ''}>مارس</option>
                        <option value="4" ${gasTank && gasTank.manufactureMonth === 4 ? 'selected' : ''}>أبريل</option>
                        <option value="5" ${gasTank && gasTank.manufactureMonth === 5 ? 'selected' : ''}>مايو</option>
                        <option value="6" ${gasTank && gasTank.manufactureMonth === 6 ? 'selected' : ''}>يونيو</option>
                        <option value="7" ${gasTank && gasTank.manufactureMonth === 7 ? 'selected' : ''}>يوليو</option>
                        <option value="8" ${gasTank && gasTank.manufactureMonth === 8 ? 'selected' : ''}>أغسطس</option>
                        <option value="9" ${gasTank && gasTank.manufactureMonth === 9 ? 'selected' : ''}>سبتمبر</option>
                        <option value="10" ${gasTank && gasTank.manufactureMonth === 10 ? 'selected' : ''}>أكتوبر</option>
                        <option value="11" ${gasTank && gasTank.manufactureMonth === 11 ? 'selected' : ''}>نوفمبر</option>
                        <option value="12" ${gasTank && gasTank.manufactureMonth === 12 ? 'selected' : ''}>ديسمبر</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>سنة التصنيع:</label>
                    <input type="number" class="tank-manufacture-year" value="${gasTank ? gasTank.manufactureYear : ''}" min="1990" max="${new Date().getFullYear()}" required>
                </div>
                <div class="form-group">
                    <label>ملاحظات الخزان:</label>
                    <textarea class="tank-notes" placeholder="أي ملاحظات إضافية حول الخزان">${gasTank ? gasTank.notes || '' : ''}</textarea>
                </div>
            </div>
        </div>
    `;

    container.appendChild(vehicleItem);

    // إضافة مستمع الحدث لزر الحذف
    vehicleItem.querySelector('.remove-vehicle').addEventListener('click', () => {
        if (container.children.length > 1) {
            container.removeChild(vehicleItem);
        } else {
            showToast('يجب إبقاء سيارة وخزان واحد على الأقل', false);
        }
    });
}

// تحرير زبون
function editCustomer(customerId) {
    const customer = appData.customers.find(c => c.id === customerId);
    if (!customer) return;

    document.getElementById('customer-modal-title').textContent = 'تعديل بيانات الزبون';
    document.getElementById('customer-id').value = customer.id;
    document.getElementById('customer-name').value = customer.name;
    document.getElementById('customer-phone').value = customer.phone;
    document.getElementById('customer-address').value = customer.address || '';
    document.getElementById('customer-notes').value = customer.notes || '';

    // ملء بيانات السيارة والخزان
    const customerVehicles = appData.vehicles.filter(v => v.customerId === customer.id);
    if (customerVehicles.length > 0) {
        const vehicle = customerVehicles[0]; // أخذ أول سيارة
        const gasTank = appData.gasTanks.find(tank => tank.vehicleId === vehicle.id);

        // ملء بيانات السيارة
        document.getElementById('vehicle-plate-number').value = vehicle.plateNumber || '';
        document.getElementById('vehicle-brand').value = vehicle.brand || '';
        document.getElementById('vehicle-model').value = vehicle.model || '';
        document.getElementById('vehicle-chassis-number').value = vehicle.chassisNumber || '';
        document.getElementById('vehicle-year').value = vehicle.year || '';

        // ملء بيانات الخزان
        if (gasTank) {
            document.getElementById('tank-type').value = gasTank.type || gasTank.shape || '';
            document.getElementById('tank-brand').value = gasTank.brand || '';
            document.getElementById('tank-serial-number').value = gasTank.serialNumber || '';
            document.getElementById('tank-capacity').value = gasTank.capacity || '';
            document.getElementById('tank-manufacture-date').value = gasTank.manufactureDate || '';
        }
    }

    document.getElementById('customer-modal').style.display = 'block';
}

// عرض تفاصيل الزبون
function viewCustomer(customerId) {
    // يمكن تنفيذ هذه الوظيفة لعرض تفاصيل الزبون في نافذة منبثقة أو صفحة منفصلة
    editCustomer(customerId); // استخدام نفس نافذة التحرير للعرض
}

// حذف زبون
function deleteCustomer(customerId) {
    if (confirm('هل أنت متأكد من حذف هذا الزبون وجميع بياناته؟')) {
        // التحقق من وجود بطاقات أو مواعيد مرتبطة بالزبون
        const hasCards = appData.gasCards.some(card => card.customerId === customerId);
        const hasAppointments = appData.appointments.some(app => app.customerId === customerId);

        if (hasCards || hasAppointments) {
            if (!confirm('هذا الزبون لديه بطاقات غاز أو مواعيد مسجلة. هل تريد حذفه بالفعل؟')) {
                return;
            }
        }

        // حذف الزبون
        const customerIndex = appData.customers.findIndex(c => c.id === customerId);
        if (customerIndex !== -1) {
            appData.customers.splice(customerIndex, 1);

            // حذف سيارات الزبون
            const customerVehicleIds = appData.vehicles.filter(v => v.customerId === customerId).map(v => v.id);
            appData.vehicles = appData.vehicles.filter(v => v.customerId !== customerId);

            // حذف خزانات الزبون
            appData.gasTanks = appData.gasTanks.filter(tank => !customerVehicleIds.includes(tank.vehicleId));

            // حذف بطاقات الزبون
            appData.gasCards = appData.gasCards.filter(card => card.customerId !== customerId);

            // حذف مواعيد الزبون
            appData.appointments = appData.appointments.filter(app => app.customerId !== customerId);

            saveData();
            updateDashboard();
            updateAllTables();
            showToast('تم حذف الزبون وجميع بياناته بنجاح');
        }
    }
}

// إنشاء محتوى تقرير الديون
function generateDebtsReport() {
    // إنشاء محتوى التقرير
    let reportContent = `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <title>تقرير الديون</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 20px;
                    direction: rtl;
                }
                h1, h2, h3 {
                    text-align: center;
                }
                .report-header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                    padding-bottom: 10px;
                    border-bottom: 1px solid #ddd;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                }
                th {
                    background-color: #f2f2f2;
                }
                .text-danger {
                    color: #e74c3c;
                }
                .text-warning {
                    color: #f39c12;
                }
                .text-success {
                    color: #2ecc71;
                }
                .summary {
                    margin-top: 30px;
                    padding: 10px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 0.8em;
                    color: #666;
                }
                .no-print {
                    display: block;
                }
                @media print {
                    .no-print {
                        display: none;
                    }
                    button {
                        display: none;
                    }
                }
            </style>
        </head>
        <body>
            <div class="no-print" style="text-align: center; margin-bottom: 20px;">
                <button onclick="window.print();" style="padding: 10px 20px; cursor: pointer;">طباعة التقرير</button>
            </div>

            <h1>تقرير الديون</h1>

            <div class="report-header">
                <div>
                    <strong>اسم المحل:</strong> ${appData.settings.shopName}
                </div>
                <div>
                    <strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الزبون</th>
                        <th>المبلغ</th>
                        <th>المبلغ المتبقي</th>
                        <th>تاريخ الإنشاء</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>الحالة</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // إضافة بيانات الديون
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const debtReminderDays = appData.settings.debtReminderDays;

    // ترتيب الديون حسب تاريخ الاستحقاق
    const sortedDebts = [...appData.debts].sort((a, b) => {
        return new Date(a.dueDate) - new Date(b.dueDate);
    });

    sortedDebts.forEach((debt, index) => {
        const customer = appData.customers.find(c => c.id === debt.customerId);

        if (customer) {
            // حساب المبلغ المتبقي
            const payments = appData.debtPayments.filter(payment => payment.debtId === debt.id);
            const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
            const remainingAmount = debt.amount - totalPaid;

            // تحديد حالة الدين
            let status = '';
            let statusClass = '';

            if (debt.isPaid) {
                status = 'مسدد';
                statusClass = 'text-success';
            } else {
                const dueDate = new Date(debt.dueDate);
                dueDate.setHours(0, 0, 0, 0);
                const daysLeft = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

                if (daysLeft < 0) {
                    status = 'متأخر';
                    statusClass = 'text-danger';
                } else if (daysLeft <= debtReminderDays) {
                    status = 'مستحق قريباً';
                    statusClass = 'text-warning';
                } else {
                    status = 'نشط';
                }
            }

            reportContent += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${customer.name}</td>
                    <td>${debt.amount.toFixed(2)}</td>
                    <td>${debt.isPaid ? '0.00' : remainingAmount.toFixed(2)}</td>
                    <td>${formatDate(debt.createdAt)}</td>
                    <td>${formatDate(debt.dueDate)}</td>
                    <td class="${statusClass}">${status}</td>
                </tr>
            `;
        }
    });

    // إضافة ملخص
    const totalDebtAmount = appData.debts.reduce((sum, debt) => sum + debt.amount, 0);

    const paidDebts = appData.debts.filter(debt => debt.isPaid).length;
    const paidDebtAmount = appData.debts.filter(debt => debt.isPaid).reduce((sum, debt) => sum + debt.amount, 0);

    const activeDebts = appData.debts.filter(debt => !debt.isPaid).length;
    const activeDebtAmount = appData.debts.filter(debt => !debt.isPaid).reduce((sum, debt) => sum + debt.amount, 0);

    const overdueDebts = appData.debts.filter(debt => {
        if (debt.isPaid) return false;
        const dueDate = new Date(debt.dueDate);
        dueDate.setHours(0, 0, 0, 0);
        return dueDate < today;
    }).length;

    const overdueDebtAmount = appData.debts.filter(debt => {
        if (debt.isPaid) return false;
        const dueDate = new Date(debt.dueDate);
        dueDate.setHours(0, 0, 0, 0);
        return dueDate < today;
    }).reduce((sum, debt) => sum + debt.amount, 0);

    // حساب المبلغ المتبقي الإجمالي
    let totalRemainingAmount = 0;
    appData.debts.forEach(debt => {
        if (!debt.isPaid) {
            const payments = appData.debtPayments.filter(payment => payment.debtId === debt.id);
            const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
            totalRemainingAmount += (debt.amount - totalPaid);
        }
    });

    reportContent += `
                </tbody>
            </table>

            <div class="summary">
                <h3>ملخص الديون</h3>
                <p>إجمالي الديون: ${appData.debts.length} (${totalDebtAmount.toFixed(2)})</p>
                <p>الديون المسددة: ${paidDebts} (${paidDebtAmount.toFixed(2)})</p>
                <p>الديون النشطة: ${activeDebts} (${activeDebtAmount.toFixed(2)})</p>
                <p>الديون المتأخرة: ${overdueDebts} (${overdueDebtAmount.toFixed(2)})</p>
                <p>إجمالي المبلغ المتبقي: ${totalRemainingAmount.toFixed(2)}</p>
            </div>

            <div class="footer">
                تم إنشاء هذا التقرير بواسطة نظام إدارة محل تركيب وتصليح الغاز للسيارات<br>
                ${new Date().toLocaleString('ar-EG', { numberingSystem: 'latn' })}
            </div>
        </body>
        </html>
    `;

    return reportContent;
}

// طباعة تقرير الديون
function printDebtsReport() {
    // إنشاء نافذة الطباعة
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // الحصول على التاريخ الحالي
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // إنشاء محتوى التقرير
    let reportContent = `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <title>تقرير الديون</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 20px;
                    direction: rtl;
                }
                h1, h2, h3 {
                    text-align: center;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                }
                th {
                    background-color: #f2f2f2;
                }
                .report-header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                }
                .text-danger {
                    color: #e74c3c;
                    font-weight: bold;
                }
                .text-warning {
                    color: #f39c12;
                    font-weight: bold;
                }
                .summary {
                    margin-top: 20px;
                    padding: 10px;
                    background-color: #f9f9f9;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 0.8em;
                    color: #666;
                }
                .no-print {
                    display: block;
                }
                @media print {
                    .no-print {
                        display: none;
                    }
                    button {
                        display: none;
                    }
                }
            </style>
        </head>
        <body>
            <div class="no-print" style="text-align: center; margin-bottom: 20px;">
                <button onclick="window.print();" style="padding: 10px 20px; cursor: pointer;">طباعة التقرير</button>
            </div>

            <h1>تقرير الديون</h1>

            <div class="report-header">
                <div>
                    <strong>اسم المحل:</strong> ${appData.settings.shopName}
                </div>
                <div>
                    <strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}
                </div>
            </div>

            <h3>الديون النشطة</h3>
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الزبون</th>
                        <th>المبلغ الأصلي</th>
                        <th>المبلغ المتبقي</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>الأيام المتبقية</th>
                        <th>سبب الدين</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // الديون النشطة
    const activeDebts = appData.debts.filter(debt => !debt.isPaid);
    let totalActiveDebtAmount = 0;
    let totalRemainingAmount = 0;
    let overdueDebtsCount = 0;

    activeDebts.sort((a, b) => new Date(a.dueDate) - new Date(b.dueDate)).forEach((debt, index) => {
        const customer = appData.customers.find(c => c.id === debt.customerId);
        if (customer) {
            // حساب المبلغ المتبقي
            const payments = appData.debtPayments.filter(payment => payment.debtId === debt.id);
            const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
            const remainingAmount = debt.amount - totalPaid;

            // حساب الأيام المتبقية
            const dueDate = new Date(debt.dueDate);
            dueDate.setHours(0, 0, 0, 0);
            const daysLeft = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));
            let daysLeftText = `${daysLeft} يوم`;
            let daysLeftClass = '';

            if (daysLeft < 0) {
                daysLeftText = `متأخر ${Math.abs(daysLeft)} يوم`;
                daysLeftClass = 'text-danger';
                overdueDebtsCount++;
            } else if (daysLeft <= appData.settings.debtReminderDays) {
                daysLeftClass = 'text-warning';
            }

            totalActiveDebtAmount += debt.amount;
            totalRemainingAmount += remainingAmount;

            reportContent += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${customer.name}</td>
                    <td>${debt.amount.toFixed(2)}</td>
                    <td>${remainingAmount.toFixed(2)}</td>
                    <td>${formatDate(debt.dueDate)}</td>
                    <td class="${daysLeftClass}">${daysLeftText}</td>
                    <td>${debt.reason || '-'}</td>
                </tr>
            `;
        }
    });

    reportContent += `
                </tbody>
            </table>

            <h3>الديون المسددة</h3>
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الزبون</th>
                        <th>المبلغ</th>
                        <th>تاريخ الإنشاء</th>
                        <th>تاريخ السداد</th>
                        <th>سبب الدين</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // الديون المسددة
    const paidDebts = appData.debts.filter(debt => debt.isPaid);
    let totalPaidDebtAmount = 0;

    paidDebts.sort((a, b) => new Date(b.paidDate) - new Date(a.paidDate)).forEach((debt, index) => {
        const customer = appData.customers.find(c => c.id === debt.customerId);
        if (customer) {
            totalPaidDebtAmount += debt.amount;

            reportContent += `
                <tr>
                    <td>${index + 1}</td>
                    <td>${customer.name}</td>
                    <td>${debt.amount.toFixed(2)}</td>
                    <td>${formatDate(debt.createdAt)}</td>
                    <td>${formatDate(debt.paidDate)}</td>
                    <td>${debt.reason || '-'}</td>
                </tr>
            `;
        }
    });

    reportContent += `
                </tbody>
            </table>

            <div class="summary">
                <h3>ملخص الديون</h3>
                <p><strong>إجمالي عدد الديون النشطة:</strong> ${activeDebts.length}</p>
                <p><strong>إجمالي عدد الديون المتأخرة:</strong> ${overdueDebtsCount}</p>
                <p><strong>إجمالي عدد الديون المسددة:</strong> ${paidDebts.length}</p>
                <p><strong>إجمالي مبالغ الديون النشطة:</strong> ${totalActiveDebtAmount.toFixed(2)}</p>
                <p><strong>إجمالي المبالغ المتبقية:</strong> ${totalRemainingAmount.toFixed(2)}</p>
                <p><strong>إجمالي مبالغ الديون المسددة:</strong> ${totalPaidDebtAmount.toFixed(2)}</p>
            </div>

            <div class="footer">
                تم إنشاء هذا التقرير بواسطة نظام إدارة محل تركيب وتصليح الغاز للسيارات<br>
                ${new Date().toLocaleString('ar-EG', { numberingSystem: 'latn' })}
            </div>
        </body>
        </html>
    `;

    // كتابة المحتوى في نافذة الطباعة
    printWindow.document.open();
    printWindow.document.write(reportContent);
    printWindow.document.close();
}

// طباعة التقرير الشهري للديون
function printMonthlyDebtsReport() {
    // إنشاء نافذة الطباعة
    const printWindow = window.open('', '_blank', 'width=800,height=600');

    // الحصول على التاريخ الحالي
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // الحصول على الشهر الحالي والسنة
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    // أسماء الأشهر بالفرنسية
    const months = [
        'جانفي', 'فيفري', 'مارس', 'أفريل', 'ماي', 'جوان',
        'جويلية', 'أوت', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    // إنشاء محتوى التقرير
    let reportContent = `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <title>التقرير الشهري للديون - ${months[currentMonth]} ${currentYear}</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 20px;
                    direction: rtl;
                }
                h1, h2, h3 {
                    text-align: center;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: right;
                }
                th {
                    background-color: #f2f2f2;
                }
                .report-header {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                }
                .text-danger {
                    color: #e74c3c;
                    font-weight: bold;
                }
                .text-warning {
                    color: #f39c12;
                    font-weight: bold;
                }
                .text-success {
                    color: #2ecc71;
                    font-weight: bold;
                }
                .summary {
                    margin-top: 20px;
                    padding: 10px;
                    background-color: #f9f9f9;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 0.8em;
                    color: #666;
                }
                .no-print {
                    display: block;
                }
                @media print {
                    .no-print {
                        display: none;
                    }
                    button {
                        display: none;
                    }
                }
            </style>
        </head>
        <body>
            <div class="no-print" style="text-align: center; margin-bottom: 20px;">
                <button onclick="window.print();" style="padding: 10px 20px; cursor: pointer;">طباعة التقرير</button>
            </div>

            <h1>التقرير الشهري للديون</h1>
            <h2>${months[currentMonth]} ${currentYear}</h2>

            <div class="report-header">
                <div>
                    <strong>اسم المحل:</strong> ${appData.settings.shopName}
                </div>
                <div>
                    <strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-EG', { numberingSystem: 'latn' })}
                </div>
            </div>

            <h3>الديون المضافة هذا الشهر</h3>
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الزبون</th>
                        <th>المبلغ</th>
                        <th>تاريخ الإنشاء</th>
                        <th>تاريخ الاستحقاق</th>
                        <th>الحالة</th>
                        <th>سبب الدين</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // الديون المضافة هذا الشهر
    const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
    const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0);

    const monthlyDebts = appData.debts.filter(debt => {
        const createdDate = new Date(debt.createdAt);
        return createdDate >= firstDayOfMonth && createdDate <= lastDayOfMonth;
    });

    let totalMonthlyDebtAmount = 0;

    if (monthlyDebts.length === 0) {
        reportContent += `
            <tr>
                <td colspan="7" style="text-align: center;">لا توجد ديون مضافة هذا الشهر</td>
            </tr>
        `;
    } else {
        monthlyDebts.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt)).forEach((debt, index) => {
            const customer = appData.customers.find(c => c.id === debt.customerId);
            if (customer) {
                totalMonthlyDebtAmount += debt.amount;

                // تحديد حالة الدين
                let status = '';
                let statusClass = '';

                if (debt.isPaid) {
                    status = 'مسدد';
                    statusClass = 'text-success';
                } else {
                    const dueDate = new Date(debt.dueDate);
                    dueDate.setHours(0, 0, 0, 0);
                    const daysLeft = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));

                    if (daysLeft < 0) {
                        status = 'متأخر';
                        statusClass = 'text-danger';
                    } else if (daysLeft <= appData.settings.debtReminderDays) {
                        status = 'مستحق قريباً';
                        statusClass = 'text-warning';
                    } else {
                        status = 'نشط';
                    }
                }

                reportContent += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${customer.name}</td>
                        <td>${debt.amount.toFixed(2)}</td>
                        <td>${formatDate(debt.createdAt)}</td>
                        <td>${formatDate(debt.dueDate)}</td>
                        <td class="${statusClass}">${status}</td>
                        <td>${debt.reason || '-'}</td>
                    </tr>
                `;
            }
        });
    }

    reportContent += `
                </tbody>
            </table>

            <h3>الدفعات المستلمة هذا الشهر</h3>
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الزبون</th>
                        <th>المبلغ</th>
                        <th>تاريخ الدفع</th>
                        <th>طريقة الدفع</th>
                        <th>ملاحظات</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // الدفعات المستلمة هذا الشهر
    const monthlyPayments = appData.debtPayments.filter(payment => {
        const paymentDate = new Date(payment.date);
        return paymentDate >= firstDayOfMonth && paymentDate <= lastDayOfMonth;
    });

    let totalMonthlyPaymentAmount = 0;

    if (monthlyPayments.length === 0) {
        reportContent += `
            <tr>
                <td colspan="6" style="text-align: center;">لا توجد دفعات مستلمة هذا الشهر</td>
            </tr>
        `;
    } else {
        monthlyPayments.sort((a, b) => new Date(a.date) - new Date(b.date)).forEach((payment, index) => {
            const debt = appData.debts.find(d => d.id === payment.debtId);
            if (debt) {
                const customer = appData.customers.find(c => c.id === debt.customerId);
                if (customer) {
                    totalMonthlyPaymentAmount += payment.amount;

                    reportContent += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${customer.name}</td>
                            <td>${payment.amount.toFixed(2)}</td>
                            <td>${formatDate(payment.date)}</td>
                            <td>${payment.method}</td>
                            <td>${payment.notes || '-'}</td>
                        </tr>
                    `;
                }
            }
        });
    }

    reportContent += `
                </tbody>
            </table>

            <h3>الديون المسددة هذا الشهر</h3>
            <table>
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم الزبون</th>
                        <th>المبلغ</th>
                        <th>تاريخ الإنشاء</th>
                        <th>تاريخ السداد</th>
                        <th>سبب الدين</th>
                    </tr>
                </thead>
                <tbody>
    `;

    // الديون المسددة هذا الشهر
    const monthlyPaidDebts = appData.debts.filter(debt => {
        if (!debt.isPaid || !debt.paidDate) return false;
        const paidDate = new Date(debt.paidDate);
        return paidDate >= firstDayOfMonth && paidDate <= lastDayOfMonth;
    });

    let totalMonthlyPaidDebtAmount = 0;

    if (monthlyPaidDebts.length === 0) {
        reportContent += `
            <tr>
                <td colspan="6" style="text-align: center;">لا توجد ديون مسددة هذا الشهر</td>
            </tr>
        `;
    } else {
        monthlyPaidDebts.sort((a, b) => new Date(a.paidDate) - new Date(b.paidDate)).forEach((debt, index) => {
            const customer = appData.customers.find(c => c.id === debt.customerId);
            if (customer) {
                totalMonthlyPaidDebtAmount += debt.amount;

                reportContent += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${customer.name}</td>
                        <td>${debt.amount.toFixed(2)}</td>
                        <td>${formatDate(debt.createdAt)}</td>
                        <td>${formatDate(debt.paidDate)}</td>
                        <td>${debt.reason || '-'}</td>
                    </tr>
                `;
            }
        });
    }

    reportContent += `
                </tbody>
            </table>

            <div class="summary">
                <h3>ملخص الشهر</h3>
                <p><strong>إجمالي الديون المضافة:</strong> ${monthlyDebts.length} دين بقيمة ${totalMonthlyDebtAmount.toFixed(2)}</p>
                <p><strong>إجمالي الدفعات المستلمة:</strong> ${monthlyPayments.length} دفعة بقيمة ${totalMonthlyPaymentAmount.toFixed(2)}</p>
                <p><strong>إجمالي الديون المسددة:</strong> ${monthlyPaidDebts.length} دين بقيمة ${totalMonthlyPaidDebtAmount.toFixed(2)}</p>
                <p><strong>صافي التغير في الديون:</strong> ${(totalMonthlyDebtAmount - totalMonthlyPaymentAmount).toFixed(2)}</p>
            </div>

            <div class="footer">
                تم إنشاء هذا التقرير بواسطة نظام إدارة محل تركيب وتصليح الغاز للسيارات<br>
                ${new Date().toLocaleString('ar-EG', { numberingSystem: 'latn' })}
            </div>
        </body>
        </html>
    `;

    // كتابة المحتوى في نافذة الطباعة
    printWindow.document.open();
    printWindow.document.write(reportContent);
    printWindow.document.close();
}

// ===============================
// وظائف إدارة الموردين
// ===============================

// تحديث جدول الموردين
function updateSuppliersTable() {
    const table = document.getElementById('suppliers-table').querySelector('tbody');
    table.innerHTML = '';

    appData.suppliers.forEach(supplier => {
        // حساب آخر عملية شراء
        const supplierPurchases = appData.purchases.filter(p => p.supplierId === supplier.id);
        let lastPurchase = '-';
        let totalPurchases = 0;

        if (supplierPurchases.length > 0) {
            const sortedPurchases = supplierPurchases.sort((a, b) => new Date(b.date) - new Date(a.date));
            lastPurchase = formatDate(sortedPurchases[0].date);
            totalPurchases = supplierPurchases.reduce((sum, purchase) => sum + purchase.total, 0);
        }

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${supplier.name}</td>
            <td>${supplier.phone}</td>
            <td>${supplier.address || '-'}</td>
            <td>${supplier.products || '-'}</td>
            <td>${lastPurchase}</td>
            <td>${formatCurrency(totalPurchases)}</td>
            <td><span class="status-badge ${supplier.status === 'نشط' ? 'status-active' : 'status-inactive'}">${supplier.status}</span></td>
            <td class="action-cell">
                <button class="action-btn edit" data-id="${supplier.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete" data-id="${supplier.id}" title="حذف"><i class="fas fa-trash"></i></button>
            </td>
        `;
        table.appendChild(row);

        // إضافة مستمعي الأحداث للأزرار
        row.querySelector('.edit').addEventListener('click', () => editSupplier(supplier.id));
        row.querySelector('.delete').addEventListener('click', () => deleteSupplier(supplier.id));
    });
}

// إضافة مورد جديد
function addSupplier() {
    document.getElementById('supplier-modal-title').textContent = 'إضافة مورد جديد';
    document.getElementById('supplier-form').reset();
    document.getElementById('supplier-id').value = '';
    document.getElementById('supplier-modal').style.display = 'block';
}

// تعديل مورد
function editSupplier(supplierId) {
    const supplier = appData.suppliers.find(s => s.id === supplierId);
    if (!supplier) return;

    document.getElementById('supplier-modal-title').textContent = 'تعديل المورد';
    document.getElementById('supplier-id').value = supplier.id;
    document.getElementById('supplier-name').value = supplier.name;
    document.getElementById('supplier-phone').value = supplier.phone;
    document.getElementById('supplier-email').value = supplier.email || '';
    document.getElementById('supplier-address').value = supplier.address || '';
    document.getElementById('supplier-products').value = supplier.products || '';
    document.getElementById('supplier-notes').value = supplier.notes || '';
    document.getElementById('supplier-modal').style.display = 'block';
}

// حذف مورد
function deleteSupplier(supplierId) {
    const supplier = appData.suppliers.find(s => s.id === supplierId);
    if (!supplier) return;

    // التحقق من وجود مشتريات مرتبطة بالمورد
    const relatedPurchases = appData.purchases.filter(p => p.supplierId === supplierId);

    let confirmMessage = `هل أنت متأكد من حذف المورد "${supplier.name}"؟`;
    if (relatedPurchases.length > 0) {
        confirmMessage += `\n\nتحذير: يوجد ${relatedPurchases.length} عملية شراء مرتبطة بهذا المورد.`;
    }

    if (confirm(confirmMessage)) {
        appData.suppliers = appData.suppliers.filter(s => s.id !== supplierId);
        saveData();
        updateSuppliersTable();
        updateDashboard();
        showToast('تم حذف المورد بنجاح');
    }
}

// حفظ المورد
function saveSupplier(event) {
    event.preventDefault();

    const supplierId = document.getElementById('supplier-id').value;
    const supplierData = {
        name: document.getElementById('supplier-name').value.trim(),
        phone: document.getElementById('supplier-phone').value.trim(),
        email: document.getElementById('supplier-email').value.trim(),
        address: document.getElementById('supplier-address').value.trim(),
        products: document.getElementById('supplier-products').value.trim(),
        notes: document.getElementById('supplier-notes').value.trim(),
        status: 'نشط'
    };

    // التحقق من صحة البيانات
    if (!supplierData.name) {
        showToast('يرجى إدخال اسم المورد', false);
        return;
    }

    if (!supplierData.phone) {
        showToast('يرجى إدخال رقم الهاتف', false);
        return;
    }

    if (supplierId) {
        // تعديل مورد موجود
        const supplierIndex = appData.suppliers.findIndex(s => s.id === supplierId);
        if (supplierIndex !== -1) {
            appData.suppliers[supplierIndex] = { ...appData.suppliers[supplierIndex], ...supplierData };
            showToast('تم تحديث المورد بنجاح');
        }
    } else {
        // إضافة مورد جديد
        const newSupplier = {
            id: generateId(),
            ...supplierData,
            createdAt: new Date().toISOString()
        };
        appData.suppliers.push(newSupplier);
        showToast('تم إضافة المورد بنجاح');
    }

    saveData();
    updateSuppliersTable();
    updateDashboard();
    document.getElementById('supplier-modal').style.display = 'none';
}

// ===============================
// وظائف إدارة المخزون
// ===============================

// تحديث جدول المخزون
function updateInventoryTable() {
    const table = document.getElementById('inventory-table').querySelector('tbody');
    table.innerHTML = '';

    // الحصول على قيم الفلاتر
    const categoryFilter = document.getElementById('category-filter')?.value || '';
    const typeFilter = document.getElementById('type-filter')?.value || '';
    const stockStatusFilter = document.getElementById('stock-status-filter')?.value || 'all';
    const searchTerm = document.getElementById('search-inventory')?.value.toLowerCase() || '';

    // فلترة البيانات
    let filteredInventory = appData.inventory.filter(item => {
        // فلتر الفئة (إدخال يدوي)
        if (categoryFilter && categoryFilter.trim() !== '' && !item.category.toLowerCase().includes(categoryFilter.toLowerCase())) {
            return false;
        }

        // فلتر النوع (إدخال يدوي)
        if (typeFilter && typeFilter.trim() !== '' && item.type && !item.type.toLowerCase().includes(typeFilter.toLowerCase())) {
            return false;
        }

        // فلتر حالة المخزون
        if (stockStatusFilter !== 'all') {
            if (stockStatusFilter === 'out-of-stock' && item.quantity > 0) return false;
            if (stockStatusFilter === 'low-stock' && (item.quantity === 0 || item.quantity > item.minQuantity)) return false;
            if (stockStatusFilter === 'in-stock' && item.quantity <= item.minQuantity) return false;
        }

        // فلتر البحث
        if (searchTerm && !item.name.toLowerCase().includes(searchTerm) && !item.code.toLowerCase().includes(searchTerm)) {
            return false;
        }

        return true;
    });

    filteredInventory.forEach(item => {
        let status = '';
        let statusClass = '';

        if (item.quantity === 0) {
            status = 'نفد المخزون';
            statusClass = 'status-out-of-stock';
        } else if (item.quantity <= item.minQuantity) {
            status = 'مخزون منخفض';
            statusClass = 'status-low-stock';
        } else {
            status = 'متوفر';
            statusClass = 'status-in-stock';
        }

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.code}</td>
            <td>${item.name}</td>
            <td><span class="category-badge">${item.category}</span></td>
            <td><span class="type-badge">${item.type || '-'}</span></td>
            <td>${item.quantity}</td>
            <td>${item.minQuantity}</td>
            <td>${formatCurrency(item.purchasePrice)}</td>
            <td>${formatCurrency(item.salePrice)}</td>
            <td><span class="status-badge ${statusClass}">${status}</span></td>
            <td class="action-cell">
                <button class="action-btn edit" data-id="${item.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete" data-id="${item.id}" title="حذف"><i class="fas fa-trash"></i></button>
            </td>
        `;
        table.appendChild(row);

        // إضافة مستمعي الأحداث للأزرار
        row.querySelector('.edit').addEventListener('click', () => editItem(item.id));
        row.querySelector('.delete').addEventListener('click', () => deleteItem(item.id));
    });

    // عرض رسالة إذا لم توجد نتائج
    if (filteredInventory.length === 0) {
        const row = document.createElement('tr');
        row.innerHTML = '<td colspan="9" style="text-align: center; color: #6c757d; padding: 20px;">لا توجد أصناف تطابق معايير البحث</td>';
        table.appendChild(row);
    }
}

// إضافة صنف جديد
function addItem() {
    document.getElementById('item-modal-title').textContent = 'إضافة صنف جديد';
    document.getElementById('item-form').reset();
    document.getElementById('item-id').value = '';
    document.getElementById('item-modal').style.display = 'block';
}

// تعديل صنف
function editItem(itemId) {
    const item = appData.inventory.find(i => i.id === itemId);
    if (!item) return;

    document.getElementById('item-modal-title').textContent = 'تعديل الصنف';
    document.getElementById('item-id').value = item.id;
    document.getElementById('item-code').value = item.code;
    document.getElementById('item-name').value = item.name;
    document.getElementById('item-category').value = item.category;
    document.getElementById('item-type').value = item.type || '';
    document.getElementById('item-quantity').value = item.quantity;
    document.getElementById('item-min-quantity').value = item.minQuantity;
    document.getElementById('item-purchase-price').value = item.purchasePrice;
    document.getElementById('item-sale-price').value = item.salePrice;
    document.getElementById('item-description').value = item.description || '';
    document.getElementById('item-modal').style.display = 'block';
}

// حذف صنف
function deleteItem(itemId) {
    const item = appData.inventory.find(i => i.id === itemId);
    if (!item) return;

    if (confirm(`هل أنت متأكد من حذف الصنف "${item.name}"؟`)) {
        appData.inventory = appData.inventory.filter(i => i.id !== itemId);
        saveData();
        updateInventoryTable();
        updateDashboard();
        showToast('تم حذف الصنف بنجاح');
    }
}

// حفظ الصنف
function saveItem(event) {
    event.preventDefault();

    const itemId = document.getElementById('item-id').value;
    const itemData = {
        code: document.getElementById('item-code').value.trim(),
        name: document.getElementById('item-name').value.trim(),
        category: document.getElementById('item-category').value,
        type: document.getElementById('item-type').value.trim(),
        quantity: parseInt(document.getElementById('item-quantity').value) || 0,
        minQuantity: parseInt(document.getElementById('item-min-quantity').value) || 1,
        purchasePrice: parseFloat(document.getElementById('item-purchase-price').value) || 0,
        salePrice: parseFloat(document.getElementById('item-sale-price').value) || 0,
        description: document.getElementById('item-description').value.trim()
    };

    // التحقق من صحة البيانات
    if (!itemData.code) {
        showToast('يرجى إدخال كود الصنف', false);
        return;
    }

    if (!itemData.name) {
        showToast('يرجى إدخال اسم الصنف', false);
        return;
    }

    if (!itemData.category) {
        showToast('يرجى اختيار الفئة', false);
        return;
    }

    // التحقق من عدم تكرار الكود
    const existingItem = appData.inventory.find(i => i.code === itemData.code && i.id !== itemId);
    if (existingItem) {
        showToast('كود الصنف موجود مسبقاً', false);
        return;
    }

    if (itemId) {
        // تعديل صنف موجود
        const itemIndex = appData.inventory.findIndex(i => i.id === itemId);
        if (itemIndex !== -1) {
            appData.inventory[itemIndex] = { ...appData.inventory[itemIndex], ...itemData };
            showToast('تم تحديث الصنف بنجاح');
        }
    } else {
        // إضافة صنف جديد
        const newItem = {
            id: generateId(),
            ...itemData,
            createdAt: new Date().toISOString()
        };
        appData.inventory.push(newItem);
        showToast('تم إضافة الصنف بنجاح');
    }

    saveData();
    updateInventoryTable();
    updateDashboard();
    document.getElementById('item-modal').style.display = 'none';
}

// دالة مساعدة لتنسيق العملة
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-DZ', {
        style: 'currency',
        currency: 'DZD',
        minimumFractionDigits: 2
    }).format(amount).replace('DZD', 'د.ج');
}

// دالة مساعدة للحصول على اسم الفئة
function getCategoryName(category) {
    const categories = {
        'gas-parts': 'قطع غيار الغاز',
        'tools': 'أدوات',
        'accessories': 'إكسسوارات',
        'consumables': 'مواد استهلاكية'
    };
    return categories[category] || category;
}

// ===============================
// وظائف إدارة المبيعات
// ===============================

// تحديث جدول المبيعات
function updateSalesTable() {
    const table = document.getElementById('sales-table').querySelector('tbody');
    table.innerHTML = '';

    appData.sales.forEach(sale => {
        const customer = appData.customers.find(c => c.id === sale.customerId);

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${sale.invoiceNumber}</td>
            <td>${formatDate(sale.date)}</td>
            <td>${customer ? customer.name : 'غير محدد'}</td>
            <td>${formatCurrency(sale.total)}</td>
            <td>${sale.paymentMethod}</td>
            <td><span class="status-badge status-active">${sale.status || 'مكتملة'}</span></td>
            <td class="action-cell">
                <button class="action-btn view" data-id="${sale.id}" title="عرض"><i class="fas fa-eye"></i></button>
                <button class="action-btn edit" data-id="${sale.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete" data-id="${sale.id}" title="حذف"><i class="fas fa-trash"></i></button>
            </td>
        `;
        table.appendChild(row);

        // إضافة مستمعي الأحداث للأزرار
        row.querySelector('.view').addEventListener('click', () => viewSaleDetails(sale.id));
        row.querySelector('.edit').addEventListener('click', () => editSale(sale.id));
        row.querySelector('.delete').addEventListener('click', () => deleteSale(sale.id));
    });

    updateSalesSummary();
}

// تحديث ملخص المبيعات
function updateSalesSummary() {
    const totalAmount = appData.sales.reduce((sum, sale) => sum + sale.total, 0);
    const totalCount = appData.sales.length;
    const averageAmount = totalCount > 0 ? totalAmount / totalCount : 0;

    document.getElementById('total-sales-amount').textContent = formatCurrency(totalAmount);
    document.getElementById('total-sales-count').textContent = totalCount;
    document.getElementById('average-sale-amount').textContent = formatCurrency(averageAmount);
}

// إضافة فاتورة بيع جديدة
function addSale() {
    document.getElementById('sale-modal-title').textContent = 'فاتورة بيع جديدة';
    document.getElementById('sale-form').reset();
    document.getElementById('sale-id').value = '';
    document.getElementById('sale-date').value = new Date().toISOString().split('T')[0];

    // تحديث قوائم الزبائن والأصناف
    updateCustomerSelect('sale-customer');
    updateItemSelect('sale-item-select');

    // مسح جدول الأصناف
    document.getElementById('sale-items-table').querySelector('tbody').innerHTML = '';
    updateSaleTotal();

    document.getElementById('sale-modal').style.display = 'block';
}

// تحديث قائمة الزبائن
function updateCustomerSelect(selectId) {
    const select = document.getElementById(selectId);
    select.innerHTML = '<option value="">اختر الزبون</option>';

    appData.customers.forEach(customer => {
        const option = document.createElement('option');
        option.value = customer.id;
        option.textContent = customer.name;
        select.appendChild(option);
    });
}

// تحديث قائمة السيارات - دالة عامة
function updateVehicleSelect(customerId, selectId) {
    const select = document.getElementById(selectId);
    if (!select) return;

    select.innerHTML = '<option value="">اختر السيارة</option>';

    if (customerId) {
        const customerVehicles = appData.vehicles.filter(vehicle => vehicle.customerId === customerId);
        customerVehicles.forEach(vehicle => {
            const option = document.createElement('option');
            option.value = vehicle.id;
            option.textContent = `${vehicle.plateNumber} - ${vehicle.brand || vehicle.make} ${vehicle.model}`;
            select.appendChild(option);
        });
    }
}

// تحديث قائمة الأصناف
function updateItemSelect(selectId) {
    const select = document.getElementById(selectId);
    select.innerHTML = '<option value="">اختر الصنف</option>';

    appData.inventory.filter(item => item.quantity > 0).forEach(item => {
        const option = document.createElement('option');
        option.value = item.id;
        option.textContent = `${item.name} (متوفر: ${item.quantity})`;
        select.appendChild(option);
    });
}

// إضافة صنف إلى فاتورة البيع
function addSaleItem() {
    const itemId = document.getElementById('sale-item-select').value;
    const quantity = parseInt(document.getElementById('sale-item-quantity').value);

    if (!itemId || !quantity || quantity <= 0) {
        showToast('يرجى اختيار الصنف وإدخال الكمية', false);
        return;
    }

    const item = appData.inventory.find(i => i.id === itemId);
    if (!item) {
        showToast('الصنف غير موجود', false);
        return;
    }

    if (quantity > item.quantity) {
        showToast(`الكمية المطلوبة أكبر من المتوفر (${item.quantity})`, false);
        return;
    }

    // التحقق من عدم إضافة نفس الصنف مرتين
    const tbody = document.getElementById('sale-items-table').querySelector('tbody');
    const existingRow = Array.from(tbody.rows).find(row => row.dataset.itemId === itemId);

    if (existingRow) {
        showToast('هذا الصنف موجود بالفعل في الفاتورة', false);
        return;
    }

    const total = quantity * item.salePrice;

    const row = document.createElement('tr');
    row.dataset.itemId = itemId;
    row.innerHTML = `
        <td>${item.name}</td>
        <td>${quantity}</td>
        <td>${formatCurrency(item.salePrice)}</td>
        <td>${formatCurrency(total)}</td>
        <td><button type="button" class="btn btn-sm" onclick="removeSaleItem(this)">حذف</button></td>
    `;

    tbody.appendChild(row);

    // مسح الحقول
    document.getElementById('sale-item-select').value = '';
    document.getElementById('sale-item-quantity').value = '';

    updateSaleTotal();
}

// حذف صنف من فاتورة البيع
function removeSaleItem(button) {
    button.closest('tr').remove();
    updateSaleTotal();
}

// تحديث إجمالي فاتورة البيع
function updateSaleTotal() {
    const tbody = document.getElementById('sale-items-table').querySelector('tbody');
    let subtotal = 0;

    Array.from(tbody.rows).forEach(row => {
        const totalCell = row.cells[3].textContent;
        const amount = parseFloat(totalCell.replace(/[^\d.-]/g, ''));
        subtotal += amount;
    });

    const tax = subtotal * 0.19; // ضريبة 19%
    const total = subtotal + tax;

    document.getElementById('sale-subtotal').textContent = formatCurrency(subtotal);
    document.getElementById('sale-tax').textContent = formatCurrency(tax);
    document.getElementById('sale-total').textContent = formatCurrency(total);
}

// حفظ فاتورة البيع
function saveSale(event) {
    event.preventDefault();

    const saleId = document.getElementById('sale-id').value;
    const customerId = document.getElementById('sale-customer').value;
    const date = document.getElementById('sale-date').value;
    const paymentMethod = document.getElementById('sale-payment-method').value;
    const notes = document.getElementById('sale-notes').value.trim();

    // التحقق من صحة البيانات
    if (!customerId) {
        showToast('يرجى اختيار الزبون', false);
        return;
    }

    if (!date) {
        showToast('يرجى إدخال التاريخ', false);
        return;
    }

    const tbody = document.getElementById('sale-items-table').querySelector('tbody');
    if (tbody.rows.length === 0) {
        showToast('يرجى إضافة أصناف للفاتورة', false);
        return;
    }

    // جمع بيانات الأصناف
    const items = [];
    Array.from(tbody.rows).forEach(row => {
        const itemId = row.dataset.itemId;
        const item = appData.inventory.find(i => i.id === itemId);
        const quantity = parseInt(row.cells[1].textContent);
        const price = parseFloat(row.cells[2].textContent.replace(/[^\d.-]/g, ''));
        const total = parseFloat(row.cells[3].textContent.replace(/[^\d.-]/g, ''));

        items.push({
            itemId,
            name: item.name,
            quantity,
            price,
            total
        });
    });

    const subtotal = parseFloat(document.getElementById('sale-subtotal').textContent.replace(/[^\d.-]/g, ''));
    const tax = parseFloat(document.getElementById('sale-tax').textContent.replace(/[^\d.-]/g, ''));
    const total = parseFloat(document.getElementById('sale-total').textContent.replace(/[^\d.-]/g, ''));

    const saleData = {
        customerId,
        date,
        paymentMethod,
        items,
        subtotal,
        tax,
        total,
        notes,
        status: 'مكتملة'
    };

    if (saleId) {
        // تعديل فاتورة موجودة
        const saleIndex = appData.sales.findIndex(s => s.id === saleId);
        if (saleIndex !== -1) {
            // إرجاع الكميات السابقة للمخزون
            const oldSale = appData.sales[saleIndex];
            oldSale.items.forEach(oldItem => {
                const inventoryItem = appData.inventory.find(i => i.id === oldItem.itemId);
                if (inventoryItem) {
                    inventoryItem.quantity += oldItem.quantity;
                }
            });

            appData.sales[saleIndex] = { ...appData.sales[saleIndex], ...saleData };
            showToast('تم تحديث الفاتورة بنجاح');
        }
    } else {
        // إضافة فاتورة جديدة
        const newSale = {
            id: generateId(),
            invoiceNumber: generateInvoiceNumber('S'),
            ...saleData,
            createdAt: new Date().toISOString()
        };
        appData.sales.push(newSale);
        showToast('تم إضافة الفاتورة بنجاح');
    }

    // تحديث المخزون
    items.forEach(item => {
        const inventoryItem = appData.inventory.find(i => i.id === item.itemId);
        if (inventoryItem) {
            inventoryItem.quantity -= item.quantity;
        }
    });

    saveData();
    updateSalesTable();
    updateInventoryTable();
    updateDashboard();
    document.getElementById('sale-modal').style.display = 'none';
}

// ===============================
// وظائف إدارة المشتريات
// ===============================

// تحديث جدول المشتريات
function updatePurchasesTable() {
    const table = document.getElementById('purchases-table').querySelector('tbody');
    table.innerHTML = '';

    appData.purchases.forEach(purchase => {
        const supplier = appData.suppliers.find(s => s.id === purchase.supplierId);

        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${purchase.invoiceNumber}</td>
            <td>${formatDate(purchase.date)}</td>
            <td>${supplier ? supplier.name : 'غير محدد'}</td>
            <td>${formatCurrency(purchase.total)}</td>
            <td>${purchase.paymentMethod}</td>
            <td><span class="status-badge status-active">${purchase.status || 'مكتملة'}</span></td>
            <td class="action-cell">
                <button class="action-btn view" data-id="${purchase.id}" title="عرض"><i class="fas fa-eye"></i></button>
                <button class="action-btn edit" data-id="${purchase.id}" title="تعديل"><i class="fas fa-edit"></i></button>
                <button class="action-btn delete" data-id="${purchase.id}" title="حذف"><i class="fas fa-trash"></i></button>
            </td>
        `;
        table.appendChild(row);

        // إضافة مستمعي الأحداث للأزرار
        row.querySelector('.view').addEventListener('click', () => viewPurchaseDetails(purchase.id));
        row.querySelector('.edit').addEventListener('click', () => editPurchase(purchase.id));
        row.querySelector('.delete').addEventListener('click', () => deletePurchase(purchase.id));
    });

    updatePurchasesSummary();
}

// تحديث ملخص المشتريات
function updatePurchasesSummary() {
    const totalAmount = appData.purchases.reduce((sum, purchase) => sum + purchase.total, 0);
    const totalCount = appData.purchases.length;
    const averageAmount = totalCount > 0 ? totalAmount / totalCount : 0;

    document.getElementById('total-purchases-amount').textContent = formatCurrency(totalAmount);
    document.getElementById('total-purchases-count').textContent = totalCount;
    document.getElementById('average-purchase-amount').textContent = formatCurrency(averageAmount);
}

// إضافة فاتورة شراء جديدة
function addPurchase() {
    document.getElementById('purchase-modal-title').textContent = 'فاتورة شراء جديدة';
    document.getElementById('purchase-form').reset();
    document.getElementById('purchase-id').value = '';
    document.getElementById('purchase-date').value = new Date().toISOString().split('T')[0];

    // تحديث قوائم الموردين والأصناف
    updateSupplierSelect('purchase-supplier');
    updateItemSelect('purchase-item-select');

    // مسح جدول الأصناف
    document.getElementById('purchase-items-table').querySelector('tbody').innerHTML = '';
    updatePurchaseTotal();

    document.getElementById('purchase-modal').style.display = 'block';
}

// تحديث قائمة الموردين
function updateSupplierSelect(selectId) {
    const select = document.getElementById(selectId);
    select.innerHTML = '<option value="">اختر المورد</option>';

    appData.suppliers.filter(supplier => supplier.status === 'نشط').forEach(supplier => {
        const option = document.createElement('option');
        option.value = supplier.id;
        option.textContent = supplier.name;
        select.appendChild(option);
    });
}

// إضافة صنف إلى فاتورة الشراء
function addPurchaseItem() {
    const itemId = document.getElementById('purchase-item-select').value;
    const quantity = parseInt(document.getElementById('purchase-item-quantity').value);
    const price = parseFloat(document.getElementById('purchase-item-price').value);

    if (!itemId || !quantity || quantity <= 0 || !price || price <= 0) {
        showToast('يرجى إدخال جميع البيانات المطلوبة', false);
        return;
    }

    const item = appData.inventory.find(i => i.id === itemId);
    if (!item) {
        showToast('الصنف غير موجود', false);
        return;
    }

    // التحقق من عدم إضافة نفس الصنف مرتين
    const tbody = document.getElementById('purchase-items-table').querySelector('tbody');
    const existingRow = Array.from(tbody.rows).find(row => row.dataset.itemId === itemId);

    if (existingRow) {
        showToast('هذا الصنف موجود بالفعل في الفاتورة', false);
        return;
    }

    const total = quantity * price;

    const row = document.createElement('tr');
    row.dataset.itemId = itemId;
    row.innerHTML = `
        <td>${item.name}</td>
        <td>${quantity}</td>
        <td>${formatCurrency(price)}</td>
        <td>${formatCurrency(total)}</td>
        <td><button type="button" class="btn btn-sm" onclick="removePurchaseItem(this)">حذف</button></td>
    `;

    tbody.appendChild(row);

    // مسح الحقول
    document.getElementById('purchase-item-select').value = '';
    document.getElementById('purchase-item-quantity').value = '';
    document.getElementById('purchase-item-price').value = '';

    updatePurchaseTotal();
}

// حذف صنف من فاتورة الشراء
function removePurchaseItem(button) {
    button.closest('tr').remove();
    updatePurchaseTotal();
}

// تحديث إجمالي فاتورة الشراء
function updatePurchaseTotal() {
    const tbody = document.getElementById('purchase-items-table').querySelector('tbody');
    let subtotal = 0;

    Array.from(tbody.rows).forEach(row => {
        const totalCell = row.cells[3].textContent;
        const amount = parseFloat(totalCell.replace(/[^\d.-]/g, ''));
        subtotal += amount;
    });

    const tax = subtotal * 0.19; // ضريبة 19%
    const total = subtotal + tax;

    document.getElementById('purchase-subtotal').textContent = formatCurrency(subtotal);
    document.getElementById('purchase-tax').textContent = formatCurrency(tax);
    document.getElementById('purchase-total').textContent = formatCurrency(total);
}

// دالة مساعدة لتوليد رقم الفاتورة
function generateInvoiceNumber(prefix) {
    const date = new Date();
    const year = date.getFullYear().toString().slice(-2);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');

    return `${prefix}${year}${month}${day}${random}`;
}

// ===============================
// إعداد النماذج الجديدة
// ===============================

// إعداد نموذج المورد
function setupSupplierForm() {
    const addSupplierBtn = document.getElementById('add-supplier-btn');
    const supplierModal = document.getElementById('supplier-modal');
    const supplierForm = document.getElementById('supplier-form');
    const cancelSupplierBtn = document.getElementById('cancel-supplier');
    const closeBtn = supplierModal.querySelector('.close');

    // فتح النموذج عند النقر على زر الإضافة
    addSupplierBtn.addEventListener('click', addSupplier);

    // إغلاق النموذج
    cancelSupplierBtn.addEventListener('click', () => {
        supplierModal.style.display = 'none';
    });

    closeBtn.addEventListener('click', () => {
        supplierModal.style.display = 'none';
    });

    // حفظ المورد
    supplierForm.addEventListener('submit', saveSupplier);
}

// إعداد نموذج الصنف
function setupItemForm() {
    const addItemBtn = document.getElementById('add-item-btn');
    const itemModal = document.getElementById('item-modal');
    const itemForm = document.getElementById('item-form');
    const cancelItemBtn = document.getElementById('cancel-item');
    const closeBtn = itemModal.querySelector('.close');

    // فتح النموذج عند النقر على زر الإضافة
    addItemBtn.addEventListener('click', addItem);

    // إغلاق النموذج
    cancelItemBtn.addEventListener('click', () => {
        itemModal.style.display = 'none';
    });

    closeBtn.addEventListener('click', () => {
        itemModal.style.display = 'none';
    });

    // حفظ الصنف
    itemForm.addEventListener('submit', saveItem);
}

// إعداد فلاتر المخزون
function setupInventoryFilters() {
    const categoryFilter = document.getElementById('category-filter');
    const typeFilter = document.getElementById('type-filter');
    const stockStatusFilter = document.getElementById('stock-status-filter');
    const searchInventory = document.getElementById('search-inventory');

    // إضافة مستمعي الأحداث للفلاتر
    if (categoryFilter) {
        categoryFilter.addEventListener('input', updateInventoryTable);
    }

    if (typeFilter) {
        typeFilter.addEventListener('input', updateInventoryTable);
    }

    if (stockStatusFilter) {
        stockStatusFilter.addEventListener('change', updateInventoryTable);
    }

    if (searchInventory) {
        searchInventory.addEventListener('input', updateInventoryTable);
    }
}

function setupStockAdjustmentForm() {
    // إعداد تعديل المخزون
    const adjustStockBtn = document.getElementById('adjust-stock-btn');
    const stockAdjustmentModal = document.getElementById('stock-adjustment-modal');
    const stockAdjustmentForm = document.getElementById('stock-adjustment-form');
    const cancelAdjustmentBtn = document.getElementById('cancel-adjustment');
    const closeAdjustmentBtn = stockAdjustmentModal.querySelector('.close');

    adjustStockBtn.addEventListener('click', () => {
        // تحديث قائمة الأصناف
        const itemSelect = document.getElementById('adjustment-item');
        itemSelect.innerHTML = '<option value="">اختر الصنف</option>';

        appData.inventory.forEach(item => {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = `${item.name} (${item.code})`;
            itemSelect.appendChild(option);
        });

        stockAdjustmentModal.style.display = 'block';
    });

    // تحديث معلومات الصنف عند الاختيار
    document.getElementById('adjustment-item').addEventListener('change', (e) => {
        const itemId = e.target.value;
        const item = appData.inventory.find(i => i.id === itemId);

        if (item) {
            document.getElementById('current-quantity').textContent = item.quantity;
            document.getElementById('adjustment-item-info').style.display = 'block';
        } else {
            document.getElementById('adjustment-item-info').style.display = 'none';
        }
    });

    cancelAdjustmentBtn.addEventListener('click', () => {
        stockAdjustmentModal.style.display = 'none';
    });

    closeAdjustmentBtn.addEventListener('click', () => {
        stockAdjustmentModal.style.display = 'none';
    });

    stockAdjustmentForm.addEventListener('submit', saveStockAdjustment);
}

// إعداد نموذج المبيعات
function setupSalesForm() {
    const newSaleBtn = document.getElementById('new-sale-btn');
    const saleModal = document.getElementById('sale-modal');
    const saleForm = document.getElementById('sale-form');
    const cancelSaleBtn = document.getElementById('cancel-sale');
    const closeBtn = saleModal.querySelector('.close');
    const addSaleItemBtn = document.getElementById('add-sale-item');

    // فتح النموذج عند النقر على زر الإضافة
    newSaleBtn.addEventListener('click', addSale);

    // إضافة صنف للفاتورة
    addSaleItemBtn.addEventListener('click', addSaleItem);

    // إغلاق النموذج
    cancelSaleBtn.addEventListener('click', () => {
        saleModal.style.display = 'none';
    });

    closeBtn.addEventListener('click', () => {
        saleModal.style.display = 'none';
    });

    // حفظ الفاتورة
    saleForm.addEventListener('submit', saveSale);
}

// إعداد نموذج المشتريات
function setupPurchasesForm() {
    const newPurchaseBtn = document.getElementById('new-purchase-btn');
    const purchaseModal = document.getElementById('purchase-modal');
    const purchaseForm = document.getElementById('purchase-form');
    const cancelPurchaseBtn = document.getElementById('cancel-purchase');
    const closeBtn = purchaseModal.querySelector('.close');
    const addPurchaseItemBtn = document.getElementById('add-purchase-item');

    // فتح النموذج عند النقر على زر الإضافة
    newPurchaseBtn.addEventListener('click', addPurchase);

    // إضافة صنف للفاتورة
    addPurchaseItemBtn.addEventListener('click', addPurchaseItem);

    // إغلاق النموذج
    cancelPurchaseBtn.addEventListener('click', () => {
        purchaseModal.style.display = 'none';
    });

    closeBtn.addEventListener('click', () => {
        purchaseModal.style.display = 'none';
    });

    // حفظ الفاتورة
    purchaseForm.addEventListener('submit', savePurchase);
}

// حفظ تعديل المخزون
function saveStockAdjustment(event) {
    event.preventDefault();

    const itemId = document.getElementById('adjustment-item').value;
    const adjustmentType = document.getElementById('adjustment-type').value;
    const quantity = parseInt(document.getElementById('adjustment-quantity').value);
    const reason = document.getElementById('adjustment-reason').value;
    const notes = document.getElementById('adjustment-notes').value.trim();

    if (!itemId || !adjustmentType || !quantity || quantity <= 0 || !reason) {
        showToast('يرجى إدخال جميع البيانات المطلوبة', false);
        return;
    }

    const item = appData.inventory.find(i => i.id === itemId);
    if (!item) {
        showToast('الصنف غير موجود', false);
        return;
    }

    const oldQuantity = item.quantity;
    let newQuantity = oldQuantity;

    switch (adjustmentType) {
        case 'add':
            newQuantity = oldQuantity + quantity;
            break;
        case 'subtract':
            newQuantity = oldQuantity - quantity;
            if (newQuantity < 0) {
                showToast('لا يمكن أن تكون الكمية أقل من الصفر', false);
                return;
            }
            break;
        case 'set':
            newQuantity = quantity;
            break;
    }

    // تحديث المخزون
    item.quantity = newQuantity;

    // حفظ سجل التعديل
    const adjustment = {
        id: generateId(),
        itemId,
        itemName: item.name,
        oldQuantity,
        newQuantity,
        adjustmentType,
        quantity,
        reason,
        notes,
        createdAt: new Date().toISOString()
    };

    appData.stockAdjustments.push(adjustment);

    saveData();
    updateInventoryTable();
    updateDashboard();
    document.getElementById('stock-adjustment-modal').style.display = 'none';
    showToast('تم تعديل المخزون بنجاح');
}

// حفظ فاتورة الشراء
function savePurchase(event) {
    event.preventDefault();

    const purchaseId = document.getElementById('purchase-id').value;
    const supplierId = document.getElementById('purchase-supplier').value;
    const date = document.getElementById('purchase-date').value;
    const paymentMethod = document.getElementById('purchase-payment-method').value;
    const notes = document.getElementById('purchase-notes').value.trim();

    // التحقق من صحة البيانات
    if (!supplierId) {
        showToast('يرجى اختيار المورد', false);
        return;
    }

    if (!date) {
        showToast('يرجى إدخال التاريخ', false);
        return;
    }

    const tbody = document.getElementById('purchase-items-table').querySelector('tbody');
    if (tbody.rows.length === 0) {
        showToast('يرجى إضافة أصناف للفاتورة', false);
        return;
    }

    // جمع بيانات الأصناف
    const items = [];
    Array.from(tbody.rows).forEach(row => {
        const itemId = row.dataset.itemId;
        const item = appData.inventory.find(i => i.id === itemId);
        const quantity = parseInt(row.cells[1].textContent);
        const price = parseFloat(row.cells[2].textContent.replace(/[^\d.-]/g, ''));
        const total = parseFloat(row.cells[3].textContent.replace(/[^\d.-]/g, ''));

        items.push({
            itemId,
            name: item.name,
            quantity,
            price,
            total
        });
    });

    const subtotal = parseFloat(document.getElementById('purchase-subtotal').textContent.replace(/[^\d.-]/g, ''));
    const tax = parseFloat(document.getElementById('purchase-tax').textContent.replace(/[^\d.-]/g, ''));
    const total = parseFloat(document.getElementById('purchase-total').textContent.replace(/[^\d.-]/g, ''));

    const purchaseData = {
        supplierId,
        date,
        paymentMethod,
        items,
        subtotal,
        tax,
        total,
        notes,
        status: 'مكتملة'
    };

    if (purchaseId) {
        // تعديل فاتورة موجودة
        const purchaseIndex = appData.purchases.findIndex(p => p.id === purchaseId);
        if (purchaseIndex !== -1) {
            // إرجاع الكميات السابقة من المخزون
            const oldPurchase = appData.purchases[purchaseIndex];
            oldPurchase.items.forEach(oldItem => {
                const inventoryItem = appData.inventory.find(i => i.id === oldItem.itemId);
                if (inventoryItem) {
                    inventoryItem.quantity -= oldItem.quantity;
                }
            });

            appData.purchases[purchaseIndex] = { ...appData.purchases[purchaseIndex], ...purchaseData };
            showToast('تم تحديث الفاتورة بنجاح');
        }
    } else {
        // إضافة فاتورة جديدة
        const newPurchase = {
            id: generateId(),
            invoiceNumber: generateInvoiceNumber('P'),
            ...purchaseData,
            createdAt: new Date().toISOString()
        };
        appData.purchases.push(newPurchase);
        showToast('تم إضافة الفاتورة بنجاح');
    }

    // تحديث المخزون
    items.forEach(item => {
        const inventoryItem = appData.inventory.find(i => i.id === item.itemId);
        if (inventoryItem) {
            inventoryItem.quantity += item.quantity;
            // تحديث سعر الشراء إذا كان مختلفاً
            if (item.price !== inventoryItem.purchasePrice) {
                inventoryItem.purchasePrice = item.price;
            }
        }
    });

    saveData();
    updatePurchasesTable();
    updateInventoryTable();
    updateDashboard();
    document.getElementById('purchase-modal').style.display = 'none';
}



















// تم حذف التعريف المكرر - استخدم التعريف الموحد أدناه





























// إضافة بيانات تجريبية للاختبار
function addSampleDataIfNeeded() {
    // إضافة زبون تجريبي إذا لم يكن موجود
    if (appData.customers.length === 0) {
        const sampleCustomer = {
            id: 'customer-1',
            name: 'أحمد محمد',
            phone: '0555123456',
            address: 'الرياض، المملكة العربية السعودية',
            createdAt: new Date().toISOString()
        };
        appData.customers.push(sampleCustomer);

        // إضافة سيارة تجريبية
        const sampleVehicle = {
            id: 'vehicle-1',
            customerId: 'customer-1',
            plateNumber: '1506615-26',
            brand: 'Dacia',
            model: 'Logan',
            year: '2020',
            createdAt: new Date().toISOString()
        };
        appData.vehicles.push(sampleVehicle);

        // إضافة خزان تجريبي
        const sampleTank = {
            id: 'tank-1',
            vehicleId: 'vehicle-1',
            customerId: 'customer-1',
            gasType: 'GPL',
            capacity: 60,
            serialNumber: '1506623',
            brand: 'TMS',
            shape: 'cylindrical',
            createdAt: new Date().toISOString()
        };
        appData.gasTanks.push(sampleTank);



        // حفظ البيانات
        saveData();

        // تحديث الجداول
        updateCustomersTable();
        updateVehiclesTable();

        console.log('تم إضافة بيانات تجريبية للاختبار');
    }
}

// ===== وظائف إدارة الشهادات =====

// إضافة بيانات الشهادات إلى appData
if (!appData.installationCertificates) appData.installationCertificates = [];
if (!appData.monitoringCertificates) appData.monitoringCertificates = [];

// فتح نافذة البحث عن الزبون لطباعة الشهادة
function openCustomerSearchModal() {
    const modal = document.getElementById('customer-search-modal');
    modal.style.display = 'block';

    // مسح نتائج البحث السابقة
    document.getElementById('customer-search-input').value = '';
    document.getElementById('customer-search-results').style.display = 'none';
}

// البحث عن الزبائن
function searchCustomersForCertificate() {
    const searchTerm = document.getElementById('customer-search-input').value.trim().toLowerCase();

    if (searchTerm.length < 2) {
        showToast('يرجى إدخال حرفين على الأقل للبحث', false);
        return;
    }

    // البحث في بيانات الزبائن
    const results = appData.customers.filter(customer => {
        const customerMatch = customer.name.toLowerCase().includes(searchTerm) ||
                            customer.phone.includes(searchTerm);

        // البحث في السيارات المرتبطة بالزبون
        const vehicleMatch = appData.vehicles.some(vehicle =>
            vehicle.customerId === customer.id &&
            vehicle.plateNumber.toLowerCase().includes(searchTerm)
        );

        return customerMatch || vehicleMatch;
    });

    displayCustomerSearchResults(results);
}

// عرض نتائج البحث
function displayCustomerSearchResults(customers) {
    const resultsContainer = document.getElementById('customer-search-results');
    const tableBody = document.getElementById('customer-search-table').querySelector('tbody');

    tableBody.innerHTML = '';

    if (customers.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="5" class="text-center">لا توجد نتائج للبحث</td></tr>';
    } else {
        customers.forEach(customer => {
            // الحصول على سيارات الزبون
            const customerVehicles = appData.vehicles.filter(v => v.customerId === customer.id);

            customerVehicles.forEach(vehicle => {
                // الحصول على خزان الغاز للسيارة
                const tank = appData.gasTanks.find(t => t.vehicleId === vehicle.id);

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${customer.name}</td>
                    <td>${customer.phone}</td>
                    <td>${vehicle.plateNumber}</td>
                    <td>${tank ? getTankTypeText(tank.shape) : 'غير محدد'}</td>
                    <td class="action-cell">
                        <button class="btn primary" onclick="selectCustomerForCertificate('${customer.id}', '${vehicle.id}')">
                            <i class="fas fa-certificate"></i> طباعة الشهادة
                        </button>
                    </td>
                `;
                tableBody.appendChild(row);
            });
        });
    }

    resultsContainer.style.display = 'block';
}

// اختيار زبون لطباعة الشهادة
function selectCustomerForCertificate(customerId, vehicleId) {
    const customer = appData.customers.find(c => c.id === customerId);
    const vehicle = appData.vehicles.find(v => v.id === vehicleId);
    const tank = appData.gasTanks.find(t => t.vehicleId === vehicleId);

    if (!customer || !vehicle) {
        showToast('خطأ في البيانات', false);
        return;
    }

    // إغلاق نافذة البحث
    document.getElementById('customer-search-modal').style.display = 'none';

    // فتح نافذة طباعة الشهادة
    openCertificatePrintModal(customer, vehicle, tank);
}

// فتح نافذة طباعة الشهادة
function openCertificatePrintModal(customer, vehicle, tank) {
    const modal = document.getElementById('certificate-print-modal');
    modal.style.display = 'block';

    // تخزين البيانات للاستخدام لاحقاً
    modal.customerData = customer;
    modal.vehicleData = vehicle;
    modal.tankData = tank;

    // إنشاء معاينة الشهادة
    generateCertificatePreview('installation');
}

// إنشاء معاينة الشهادة
function generateCertificatePreview(certificateType) {
    const modal = document.getElementById('certificate-print-modal');
    const customer = modal.customerData;
    const vehicle = modal.vehicleData;
    const tank = modal.tankData;

    const previewContainer = document.getElementById('certificate-preview');

    // تحديث عنوان النافذة
    const modalTitle = modal.querySelector('h2');
    modalTitle.innerHTML = certificateType === 'installation'
        ? '<i class="fas fa-certificate"></i> طباعة شهادة التركيب'
        : '<i class="fas fa-certificate"></i> طباعة شهادة المراقبة الدورية';

    // إنشاء محتوى الشهادة
    const certificateHTML = createCertificateHTML(certificateType, customer, vehicle, tank);
    previewContainer.innerHTML = certificateHTML;
}

// إنشاء HTML للشهادة
function createCertificateHTML(type, customer, vehicle, tank) {
    const currentDate = new Date();
    const certificateNumber = generateCertificateNumber(type);

    const isInstallation = type === 'installation';
    const title = isInstallation ? 'شهادة تركيب نظام الغاز' : 'شهادة المراقبة الدورية';
    const subtitle = isInstallation ? 'CERTIFICAT D\'INSTALLATION SYSTÈME GAZ' : 'CERTIFICAT DE CONTRÔLE PÉRIODIQUE';

    return `
        <div class="certificate">
            <!-- رأس الشهادة -->
            <div class="certificate-header">
                <div class="header-top">
                    <div class="company-info">
                        <h1 class="company-name">مؤسسة وقود المستقبل</h1>
                        <h2 class="company-name-fr">FUTURE FUEL CORPORATION</h2>
                        <p class="company-details">تركيب وصيانة أنظمة الغاز للسيارات</p>
                        <p class="company-details-fr">Installation et maintenance des systèmes de gaz pour véhicules</p>
                        <p class="company-contact">الهاتف: +213 XXX XXX XXX | العنوان: الجزائر</p>
                        <p class="company-contact-fr">Tél: +213 XXX XXX XXX | Adresse: Algérie</p>
                    </div>
                    <div class="certificate-number-box">
                        <div class="cert-number">رقم الشهادة</div>
                        <div class="cert-number-fr">N° Certificat</div>
                        <div class="cert-number-value">${certificateNumber}</div>
                    </div>
                </div>

                <div class="certificate-title-section">
                    <h1 class="certificate-main-title">${title}</h1>
                    <h2 class="certificate-main-title-fr">${subtitle}</h2>
                </div>
            </div>

            <!-- جسم الشهادة -->
            <div class="certificate-body">
                <div class="certificate-intro">
                    <p class="intro-text">
                        ${isInstallation ?
                            'نشهد بأنه تم تركيب نظام الغاز للمركبة المذكورة أدناه وفقاً للمواصفات والمعايير المعتمدة' :
                            'نشهد بأنه تم فحص ومراقبة نظام الغاز للمركبة المذكورة أدناه وأنه مطابق للمواصفات المطلوبة'
                        }
                    </p>
                    <p class="intro-text-fr">
                        ${isInstallation ?
                            'Nous certifions que le système de gaz du véhicule mentionné ci-dessous a été installé conformément aux spécifications et normes approuvées' :
                            'Nous certifions que le système de gaz du véhicule mentionné ci-dessous a été inspecté et contrôlé et qu\'il est conforme aux spécifications requises'
                        }
                    </p>
                </div>

                <!-- جدول معلومات المركبة والمالك -->
                <table class="certificate-table">
                    <thead>
                        <tr>
                            <th colspan="4" class="table-header">معلومات المالك والمركبة / Informations du propriétaire et du véhicule</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="label">اسم المالك<br><span class="label-fr">Nom du propriétaire</span></td>
                            <td class="value">${customer.name}</td>
                            <td class="label">رقم الهاتف<br><span class="label-fr">N° Téléphone</span></td>
                            <td class="value">${customer.phone}</td>
                        </tr>
                        <tr>
                            <td class="label">العنوان<br><span class="label-fr">Adresse</span></td>
                            <td class="value" colspan="3">${customer.address}</td>
                        </tr>
                        <tr>
                            <td class="label">رقم التسجيل<br><span class="label-fr">N° Immatriculation</span></td>
                            <td class="value">${vehicle.plateNumber}</td>
                            <td class="label">ماركة المركبة<br><span class="label-fr">Marque du véhicule</span></td>
                            <td class="value">${vehicle.brand}</td>
                        </tr>
                        <tr>
                            <td class="label">نوع المركبة<br><span class="label-fr">Type du véhicule</span></td>
                            <td class="value">${vehicle.model}</td>
                            <td class="label">سنة الصنع<br><span class="label-fr">Année de fabrication</span></td>
                            <td class="value">${vehicle.year || 'غير محدد'}</td>
                        </tr>
                    </tbody>
                </table>

                ${tank ? `
                <!-- جدول معلومات الخزان -->
                <table class="certificate-table">
                    <thead>
                        <tr>
                            <th colspan="4" class="table-header">معلومات خزان الغاز / Informations du réservoir de gaz</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="label">نوع الخزان<br><span class="label-fr">Type de réservoir</span></td>
                            <td class="value">${getTankTypeText(tank.shape)}</td>
                            <td class="label">ماركة الخزان<br><span class="label-fr">Marque du réservoir</span></td>
                            <td class="value">${tank.brand}</td>
                        </tr>
                        <tr>
                            <td class="label">الرقم التسلسلي<br><span class="label-fr">N° de série</span></td>
                            <td class="value">${tank.serialNumber}</td>
                            <td class="label">السعة<br><span class="label-fr">Capacité</span></td>
                            <td class="value">${tank.capacity} لتر / L</td>
                        </tr>
                        <tr>
                            <td class="label">تاريخ الصنع<br><span class="label-fr">Date de fabrication</span></td>
                            <td class="value">${tank.manufactureDate || 'غير محدد'}</td>
                            <td class="label">نوع الغاز<br><span class="label-fr">Type de gaz</span></td>
                            <td class="value">${tank.gasType || 'GPL'}</td>
                        </tr>
                    </tbody>
                </table>

                <!-- جدول معلومات التركيب/المراقبة -->
                <table class="certificate-table">
                    <thead>
                        <tr>
                            <th colspan="4" class="table-header">
                                ${isInstallation ? 'معلومات التركيب / Informations d\'installation' : 'معلومات المراقبة / Informations de contrôle'}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="label">
                                ${isInstallation ? 'تاريخ التركيب' : 'تاريخ المراقبة'}<br>
                                <span class="label-fr">${isInstallation ? 'Date d\'installation' : 'Date de contrôle'}</span>
                            </td>
                            <td class="value">${formatDateForCertificate(currentDate)}</td>
                            <td class="label">صالح حتى<br><span class="label-fr">Valide jusqu'au</span></td>
                            <td class="value">${formatDateForCertificate(getNextInspectionDate(currentDate, isInstallation))}</td>
                        </tr>
                        <tr>
                            <td class="label">حالة النظام<br><span class="label-fr">État du système</span></td>
                            <td class="value">مطابق للمواصفات<br><span class="value-fr">Conforme aux spécifications</span></td>
                            <td class="label">المهندس المسؤول<br><span class="label-fr">Ingénieur responsable</span></td>
                            <td class="value">م. أحمد محمد<br><span class="value-fr">Ing. Ahmed Mohamed</span></td>
                        </tr>
                    </tbody>
                </table>
                ` : ''}

                <!-- ملاحظات -->
                <div class="certificate-notes">
                    <h4>ملاحظات مهمة / Notes importantes:</h4>
                    <ul>
                        <li>يجب إجراء فحص دوري كل ${isInstallation ? '12 شهر' : '6 أشهر'} / Un contrôle périodique doit être effectué tous les ${isInstallation ? '12 mois' : '6 mois'}</li>
                        <li>هذه الشهادة غير قابلة للتحويل / Ce certificat n'est pas transférable</li>
                        <li>يجب الاحتفاظ بهذه الشهادة في المركبة / Ce certificat doit être conservé dans le véhicule</li>
                    </ul>
                </div>
            </div>

            <!-- تذييل الشهادة -->
            <div class="certificate-footer">
                <div class="footer-section">
                    <div class="signature-area">
                        <div class="signature-box">
                            <div class="signature-line"></div>
                            <div class="signature-label">توقيع المهندس المختص</div>
                            <div class="signature-label-fr">Signature de l'ingénieur spécialisé</div>
                        </div>
                        <div class="stamp-box">
                            <div class="stamp-area">
                                <div class="stamp-text">ختم المؤسسة</div>
                                <div class="stamp-text-fr">Cachet de l'entreprise</div>
                            </div>
                        </div>
                    </div>

                    <div class="issue-date">
                        <p><strong>تاريخ الإصدار:</strong> ${formatDateForCertificate(currentDate)}</p>
                        <p><strong>Date d'émission:</strong> ${formatDateForCertificate(currentDate)}</p>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// الحصول على نص نوع الخزان
function getTankTypeText(shape) {
    const types = {
        'cylindrical': 'أسطواني / Cylindrique',
        'toroidal': 'دائري / Toroïdal',
        'rectangular': 'مستطيل / Rectangulaire',
        'circular': 'دائري / Circulaire'
    };
    return types[shape] || 'غير محدد / Non spécifié';
}

// إنشاء رقم الشهادة
function generateCertificateNumber(type) {
    const year = new Date().getFullYear();
    const prefix = type === 'installation' ? 'INST' : 'CTRL';
    const count = type === 'installation'
        ? appData.installationCertificates.length + 1
        : appData.monitoringCertificates.length + 1;

    return `${prefix}-${year}-${count.toString().padStart(4, '0')}`;
}

// تنسيق التاريخ للشهادة
function formatDateForCertificate(date) {
    const months = [
        'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
        'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
    ];

    const arabicMonths = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    const day = date.getDate();
    const monthFr = months[date.getMonth()];
    const monthAr = arabicMonths[date.getMonth()];
    const year = date.getFullYear();

    return `${day} ${monthAr} ${year} / ${day} ${monthFr} ${year}`;
}

// حساب تاريخ المراقبة القادمة
function getNextInspectionDate(currentDate, isInstallation) {
    const nextDate = new Date(currentDate);
    if (isInstallation) {
        // شهادة التركيب صالحة لسنة واحدة
        nextDate.setFullYear(nextDate.getFullYear() + 1);
    } else {
        // شهادة المراقبة صالحة لـ 6 أشهر
        nextDate.setMonth(nextDate.getMonth() + 6);
    }
    return nextDate;
}

// التحقق من صحة بيانات الشهادة
function validateCertificateData(customer, vehicle, tank) {
    const errors = [];

    // التحقق من بيانات الزبون
    if (!customer.name || customer.name.trim() === '') {
        errors.push('اسم الزبون مطلوب');
    }
    if (!customer.phone || customer.phone.trim() === '') {
        errors.push('رقم هاتف الزبون مطلوب');
    }
    if (!customer.address || customer.address.trim() === '') {
        errors.push('عنوان الزبون مطلوب');
    }

    // التحقق من بيانات المركبة
    if (!vehicle.plateNumber || vehicle.plateNumber.trim() === '') {
        errors.push('رقم لوحة المركبة مطلوب');
    }
    if (!vehicle.brand || vehicle.brand.trim() === '') {
        errors.push('ماركة المركبة مطلوبة');
    }
    if (!vehicle.model || vehicle.model.trim() === '') {
        errors.push('نوع المركبة مطلوب');
    }

    // التحقق من بيانات الخزان
    if (tank) {
        if (!tank.serialNumber || tank.serialNumber.trim() === '') {
            errors.push('الرقم التسلسلي للخزان مطلوب');
        }
        if (!tank.brand || tank.brand.trim() === '') {
            errors.push('ماركة الخزان مطلوبة');
        }
        if (!tank.capacity || tank.capacity <= 0) {
            errors.push('سعة الخزان مطلوبة ويجب أن تكون أكبر من صفر');
        }
    }

    return errors;
}

// طباعة الشهادة
function printCertificate() {
    const modal = document.getElementById('certificate-print-modal');
    const customer = modal.customerData;
    const vehicle = modal.vehicleData;
    const tank = modal.tankData;

    // التحقق من وجود نوع الشهادة المحدد
    const certificateTypeElement = document.querySelector('input[name="certificate-type"]:checked');
    if (!certificateTypeElement) {
        showToast('يرجى اختيار نوع الشهادة', false);
        return;
    }

    const certificateType = certificateTypeElement.value;

    // التحقق من صحة البيانات
    const validationErrors = validateCertificateData(customer, vehicle, tank);

    if (validationErrors.length > 0) {
        const errorMessage = 'يرجى تصحيح الأخطاء التالية:\n\n' + validationErrors.join('\n');
        alert(errorMessage);
        return;
    }

    // حفظ بيانات الشهادة
    saveCertificateData(certificateType, customer, vehicle, tank);

    // طباعة الشهادة
    window.print();

    showToast('تم إرسال الشهادة للطباعة بنجاح', true);
}

// حفظ بيانات الشهادة
function saveCertificateData(type, customer, vehicle, tank) {
    const certificateData = {
        id: generateId(),
        type: type,
        customerId: customer.id,
        vehicleId: vehicle.id,
        tankId: tank ? tank.id : null,
        certificateNumber: generateCertificateNumber(type),
        issueDate: new Date().toISOString(),
        expiryDate: getNextInspectionDate(new Date(), type === 'installation').toISOString(),
        createdAt: new Date().toISOString()
    };

    if (type === 'installation') {
        appData.installationCertificates.push(certificateData);

        // إضافة العملية إلى جدول الإرسال تلقائياً
        if (typeof addInstallationToTransmissionTable === 'function') {
            addInstallationToTransmissionTable(certificateData);
        }
    } else {
        appData.monitoringCertificates.push(certificateData);

        // إضافة العملية إلى جدول الإرسال تلقائياً
        if (typeof addMonitoringToTransmissionTable === 'function') {
            addMonitoringToTransmissionTable(certificateData);
        }
    }

    saveData();
    updateCertificatesTables();
}

// تحديث جداول الشهادات
function updateCertificatesTables() {
    updateInstallationCertificatesTable();
    updateMonitoringCertificatesTable();
    updateCertificateReminders();
}

// تحديث جدول شهادات التركيب
function updateInstallationCertificatesTable() {
    const table = document.getElementById('installation-certificates-table').querySelector('tbody');
    table.innerHTML = '';

    appData.installationCertificates.forEach(cert => {
        const customer = appData.customers.find(c => c.id === cert.customerId);
        const vehicle = appData.vehicles.find(v => v.id === cert.vehicleId);

        if (customer && vehicle) {
            const expiryDate = new Date(cert.expiryDate);
            const today = new Date();
            let status = '';
            let statusClass = '';

            if (expiryDate < today) {
                status = 'منتهية';
                statusClass = 'status-expired';
            } else if (expiryDate <= new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)) {
                status = 'قريبة الانتهاء';
                statusClass = 'status-expiring';
            } else {
                status = 'سارية';
                statusClass = 'status-active';
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${customer.name}</td>
                <td>${vehicle.plateNumber}</td>
                <td>${formatDate(cert.issueDate)}</td>
                <td>${formatDate(cert.expiryDate)}</td>
                <td class="${statusClass}">${status}</td>
                <td class="action-cell">
                    <button class="action-btn view" onclick="viewCertificate('${cert.id}', 'installation')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn print" onclick="reprintCertificate('${cert.id}', 'installation')" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            `;
            table.appendChild(row);
        }
    });
}

// تحديث جدول شهادات المراقبة
function updateMonitoringCertificatesTable() {
    const table = document.getElementById('monitoring-certificates-table').querySelector('tbody');
    table.innerHTML = '';

    appData.monitoringCertificates.forEach(cert => {
        const customer = appData.customers.find(c => c.id === cert.customerId);
        const vehicle = appData.vehicles.find(v => v.id === cert.vehicleId);

        if (customer && vehicle) {
            const expiryDate = new Date(cert.expiryDate);
            const today = new Date();
            let status = '';
            let statusClass = '';

            if (expiryDate < today) {
                status = 'منتهية';
                statusClass = 'status-expired';
            } else if (expiryDate <= new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000)) {
                status = 'قريبة الانتهاء';
                statusClass = 'status-expiring';
            } else {
                status = 'سارية';
                statusClass = 'status-active';
            }

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${customer.name}</td>
                <td>${vehicle.plateNumber}</td>
                <td>${formatDate(cert.issueDate)}</td>
                <td>${formatDate(cert.expiryDate)}</td>
                <td class="${statusClass}">${status}</td>
                <td class="action-cell">
                    <button class="action-btn view" onclick="viewCertificate('${cert.id}', 'monitoring')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn print" onclick="reprintCertificate('${cert.id}', 'monitoring')" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            `;
            table.appendChild(row);
        }
    });
}

// تحديث تذكيرات الشهادات
function updateCertificateReminders() {
    const today = new Date();
    const urgentThreshold = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 أيام
    const warningThreshold = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000); // 30 يوم

    // جمع جميع الشهادات
    const allCertificates = [
        ...appData.installationCertificates.map(cert => ({...cert, type: 'installation'})),
        ...appData.monitoringCertificates.map(cert => ({...cert, type: 'monitoring'}))
    ];

    // تصفية الشهادات العاجلة والقريبة
    const urgentCertificates = allCertificates.filter(cert => {
        const expiryDate = new Date(cert.expiryDate);
        return expiryDate <= urgentThreshold && expiryDate >= today;
    });

    const upcomingCertificates = allCertificates.filter(cert => {
        const expiryDate = new Date(cert.expiryDate);
        return expiryDate <= warningThreshold && expiryDate > urgentThreshold;
    });

    // تحديث جدول التذكيرات العاجلة
    updateReminderTable('urgent-reminders-table', urgentCertificates);

    // تحديث جدول التذكيرات القريبة
    updateReminderTable('upcoming-reminders-table', upcomingCertificates);
}

// تحديث جدول التذكيرات
function updateReminderTable(tableId, certificates) {
    const table = document.getElementById(tableId).querySelector('tbody');
    table.innerHTML = '';

    if (certificates.length === 0) {
        table.innerHTML = '<tr><td colspan="5" class="text-center">لا توجد تذكيرات</td></tr>';
        return;
    }

    certificates.forEach(cert => {
        const customer = appData.customers.find(c => c.id === cert.customerId);
        const vehicle = appData.vehicles.find(v => v.id === cert.vehicleId);

        if (customer && vehicle) {
            const expiryDate = new Date(cert.expiryDate);
            const today = new Date();
            const daysLeft = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));

            const certificateTypeText = cert.type === 'installation' ? 'شهادة التركيب' : 'شهادة المراقبة';

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${customer.name}</td>
                <td>${certificateTypeText}</td>
                <td>${formatDate(cert.expiryDate)}</td>
                <td>${daysLeft} يوم</td>
                <td class="action-cell">
                    <button class="action-btn primary" onclick="reprintCertificate('${cert.id}', '${cert.type}')" title="طباعة جديدة">
                        <i class="fas fa-print"></i>
                    </button>
                </td>
            `;
            table.appendChild(row);
        }
    });
}

// إعادة طباعة شهادة موجودة
function reprintCertificate(certificateId, type) {
    const certificates = type === 'installation' ? appData.installationCertificates : appData.monitoringCertificates;
    const certificate = certificates.find(cert => cert.id === certificateId);

    if (!certificate) {
        showToast('لم يتم العثور على الشهادة', false);
        return;
    }

    const customer = appData.customers.find(c => c.id === certificate.customerId);
    const vehicle = appData.vehicles.find(v => v.id === certificate.vehicleId);
    const tank = appData.gasTanks.find(t => t.id === certificate.tankId);

    if (!customer || !vehicle) {
        showToast('خطأ في بيانات الشهادة', false);
        return;
    }

    // فتح نافذة طباعة الشهادة مع البيانات الموجودة
    openCertificatePrintModal(customer, vehicle, tank);

    // تحديد نوع الشهادة
    const radioButton = document.querySelector(`input[name="certificate-type"][value="${type}"]`);
    if (radioButton) {
        radioButton.checked = true;
        generateCertificatePreview(type);
    }
}

// عرض شهادة
function viewCertificate(certificateId, type) {
    // نفس وظيفة إعادة الطباعة ولكن للعرض فقط
    reprintCertificate(certificateId, type);
}

// حفظ الشهادة كـ PDF
function saveCertificateAsPDF() {
    const certificateElement = document.querySelector('.certificate');
    if (!certificateElement) {
        showToast('لا توجد شهادة للحفظ', false);
        return;
    }

    // إنشاء نافذة جديدة للطباعة
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>شهادة - مؤسسة وقود المستقبل</title>
            <link rel="stylesheet" href="styles/styles.css">
            <style>
                body { margin: 0; padding: 20px; background: white; }
                .certificate { margin: 0 auto; }
            </style>
        </head>
        <body>
            ${certificateElement.outerHTML}
            <script>
                window.onload = function() {
                    window.print();
                    setTimeout(() => window.close(), 1000);
                };
            </script>
        </body>
        </html>
    `);
    printWindow.document.close();
}

// إضافة مستمعي الأحداث للشهادات
document.addEventListener('DOMContentLoaded', function() {
    // أزرار البحث عن الزبون
    const searchCustomerBtn = document.getElementById('search-customer-for-certificate');
    if (searchCustomerBtn) {
        searchCustomerBtn.addEventListener('click', openCustomerSearchModal);
    }

    const searchBtn = document.getElementById('search-customers-btn');
    if (searchBtn) {
        searchBtn.addEventListener('click', searchCustomersForCertificate);
    }

    // البحث عند الضغط على Enter
    const searchInput = document.getElementById('customer-search-input');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchCustomersForCertificate();
            }
        });
    }

    // أزرار طباعة الشهادة
    const printBtn = document.getElementById('print-certificate-btn');
    if (printBtn) {
        printBtn.addEventListener('click', printCertificate);
    }

    const savePDFBtn = document.getElementById('save-certificate-pdf');
    if (savePDFBtn) {
        savePDFBtn.addEventListener('click', saveCertificateAsPDF);
    }

    // تغيير نوع الشهادة
    const certificateTypeRadios = document.querySelectorAll('input[name="certificate-type"]');
    certificateTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                generateCertificatePreview(this.value);
            }
        });
    });

    // أزرار إغلاق النوافذ
    const cancelSearchBtn = document.getElementById('cancel-customer-search');
    if (cancelSearchBtn) {
        cancelSearchBtn.addEventListener('click', function() {
            document.getElementById('customer-search-modal').style.display = 'none';
        });
    }

    const cancelPrintBtn = document.getElementById('cancel-certificate-print');
    if (cancelPrintBtn) {
        cancelPrintBtn.addEventListener('click', function() {
            document.getElementById('certificate-print-modal').style.display = 'none';
        });
    }

    // تبويبات الشهادات
    const certificateTabBtns = document.querySelectorAll('.certificates-tabs .tab-btn');
    certificateTabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // إزالة الفئة النشطة من جميع التبويبات
            certificateTabBtns.forEach(b => b.classList.remove('active'));
            document.querySelectorAll('.certificates-tabs .tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // إضافة الفئة النشطة للتبويب المحدد
            this.classList.add('active');
            const targetContent = document.getElementById(targetTab + '-tab');
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // تحديث الجداول حسب التبويب
            if (targetTab === 'installation-certificates') {
                updateInstallationCertificatesTable();
            } else if (targetTab === 'monitoring-certificates') {
                updateMonitoringCertificatesTable();
            } else if (targetTab === 'certificate-reminders') {
                updateCertificateReminders();
            }
        });
    });

    // تحديث جداول الشهادات عند تحميل الصفحة
    if (typeof updateCertificatesTables === 'function') {
        updateCertificatesTables();
    }
});

// إضافة الشهادات إلى تحديث البيانات
function updateAllTables() {
    updateGasCardsTable();
    updateAppointmentsTable();
    updateCustomersTable();
    updateDebtsTable();
    updateSuppliersTable();
    updateInventoryTable();
    updateSalesTable();
    updatePurchasesTable();
    updateCalendar();

    // إضافة تحديث جداول الشهادات
    if (typeof updateCertificatesTables === 'function') {
        updateCertificatesTables();
    }
}

// إضافة الشهادات إلى تحميل البيانات
function loadData() {
    try {
        // التحقق من وجود واجهة Electron
        if (window.electronAPI) {
            // تحميل البيانات من ملف
            const savedData = window.electronAPI.loadData();
            if (savedData) {
                appData = savedData;
            } else {
                // إذا لم يتم العثور على ملف البيانات، استخدم localStorage كاحتياطي
                loadFromLocalStorage();
            }
        } else {
            // استخدام localStorage إذا لم تكن واجهة Electron متاحة
            loadFromLocalStorage();
        }

        // التحقق من صحة البيانات
        if (!appData.customers) appData.customers = [];
        if (!appData.vehicles) appData.vehicles = [];
        if (!appData.gasTanks) appData.gasTanks = [];
        if (!appData.gasCards) appData.gasCards = [];
        if (!appData.appointments) appData.appointments = [];
        if (!appData.debts) appData.debts = [];
        if (!appData.debtPayments) appData.debtPayments = [];
        if (!appData.suppliers) appData.suppliers = [];
        if (!appData.inventory) appData.inventory = [];
        if (!appData.sales) appData.sales = [];
        if (!appData.purchases) appData.purchases = [];
        if (!appData.stockAdjustments) appData.stockAdjustments = [];
        if (!appData.notifications) appData.notifications = [];

        // إضافة بيانات الشهادات
        if (!appData.installationCertificates) appData.installationCertificates = [];
        if (!appData.monitoringCertificates) appData.monitoringCertificates = [];

        if (!appData.settings) {
            appData.settings = {
                shopName: 'مؤسسة وقود المستقبل',
                reminderDays: 30,
                workingHoursStart: '08:00',
                workingHoursEnd: '18:00',
                debtReminderDays: 7
            };
        } else {
            // إضافة إعدادات جديدة إذا لم تكن موجودة
            if (appData.settings.debtReminderDays === undefined) {
                appData.settings.debtReminderDays = 7;
            }
        }

        // إضافة حقل createdAt إذا لم يكن موجوداً
        const now = new Date().toISOString();
        appData.customers.forEach(customer => {
            if (!customer.createdAt) customer.createdAt = now;

            // إضافة حقل lastVisitDate إذا لم يكن موجوداً
            if (!customer.lastVisitDate) {
                // البحث عن آخر موعد للزبون
                const customerAppointments = appData.appointments.filter(app => app.customerId === customer.id);
                if (customerAppointments.length > 0) {
                    const sortedAppointments = customerAppointments.sort((a, b) => new Date(b.date) - new Date(a.date));
                    customer.lastVisitDate = sortedAppointments[0].date;
                }
            }
        });
        appData.vehicles.forEach(vehicle => {
            if (!vehicle.createdAt) vehicle.createdAt = now;
        });
        appData.gasCards.forEach(card => {
            if (!card.createdAt) card.createdAt = now;
        });
        appData.appointments.forEach(appointment => {
            if (!appointment.createdAt) appointment.createdAt = now;
        });

        updateDashboard();
        updateAllTables();
        console.log('تم تحميل البيانات بنجاح');
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
        resetData();
    }
}

// ===== دوال API للتطبيق الويب =====

// إنشاء نسخة احتياطية على الخادم
async function createServerBackup() {
    try {
        const response = await fetch('/api/backup/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            console.log('تم إنشاء نسخة احتياطية على الخادم:', result.filename);
            showToast('تم إنشاء نسخة احتياطية على الخادم', true);
            return true;
        }
        return false;
    } catch (error) {
        console.warn('فشل في إنشاء نسخة احتياطية على الخادم:', error);
        return false;
    }
}

// تحميل البيانات من الخادم
async function loadDataFromServer() {
    try {
        const response = await fetch('/api/data/load');
        const result = await response.json();

        if (result.success && result.data) {
            appData = result.data;
            updateAllTables();
            updateDashboard();
            showToast('تم تحميل البيانات من الخادم بنجاح', true);
            return true;
        }
        return false;
    } catch (error) {
        console.error('خطأ في تحميل البيانات من الخادم:', error);
        return false;
    }
}

// تصدير البيانات من الخادم
async function exportDataFromServer() {
    try {
        const response = await fetch('/api/data/export');

        if (response.ok) {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `future-fuel-data-${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            showToast('تم تصدير البيانات بنجاح', true);
            return true;
        } else {
            throw new Error('فشل في تصدير البيانات');
        }
    } catch (error) {
        console.error('خطأ في تصدير البيانات:', error);
        showToast('فشل في تصدير البيانات من الخادم', false);
        return false;
    }
}

// الحصول على إحصائيات النظام
async function getSystemStats() {
    try {
        const response = await fetch('/api/system/stats');
        const result = await response.json();

        if (result.success) {
            return result.data;
        }
        return null;
    } catch (error) {
        console.error('خطأ في الحصول على إحصائيات النظام:', error);
        return null;
    }
}

// فحص حالة الخادم
async function checkServerHealth() {
    try {
        const response = await fetch('/api/health');
        const result = await response.json();

        if (result.success) {
            console.log('حالة الخادم: متصل');
            return true;
        }
        return false;
    } catch (error) {
        console.warn('الخادم غير متصل، سيتم العمل في الوضع المحلي');
        return false;
    }
}

// الحصول على معلومات التطبيق
async function getAppInfo() {
    try {
        const response = await fetch('/api/info');
        const result = await response.json();

        if (result.success) {
            return result.data;
        }
        return null;
    } catch (error) {
        console.error('خطأ في الحصول على معلومات التطبيق:', error);
        return null;
    }
}

// تحديث واجهة المستخدم لإظهار حالة الاتصال
function updateConnectionStatus(isConnected) {
    const statusElement = document.getElementById('connection-status');
    if (statusElement) {
        if (isConnected) {
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> متصل بالخادم';
            statusElement.className = 'connection-status connected';
        } else {
            statusElement.innerHTML = '<i class="fas fa-wifi-slash"></i> وضع محلي';
            statusElement.className = 'connection-status disconnected';
        }
    }
}

// فحص الاتصال بالخادم دورياً
setInterval(async () => {
    const isConnected = await checkServerHealth();
    updateConnectionStatus(isConnected);
}, 30000); // كل 30 ثانية

// فحص الاتصال عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async () => {
    const isConnected = await checkServerHealth();
    updateConnectionStatus(isConnected);
});

// ===== وظائف جدول الإرسال =====

// متغير لتتبع العملية قيد التعديل
let editingTransmissionEntryId = null;

// عرض نافذة إضافة/تعديل عملية في جدول الإرسال
function showTransmissionEntryModal(entryId = null) {
    editingTransmissionEntryId = entryId;

    const modal = document.getElementById('transmission-entry-modal');
    const title = document.getElementById('transmission-modal-title');
    const form = document.getElementById('transmission-entry-form');

    if (entryId && typeof transmissionManager !== 'undefined') {
        // تعديل عملية موجودة
        title.textContent = 'تعديل العملية';
        const entry = transmissionManager.transmissionData.find(e => e.id === entryId);
        if (entry) {
            document.getElementById('transmission-entry-type').value = entry.type;
            document.getElementById('transmission-entry-operation-date').value = entry.operationDate;
            document.getElementById('transmission-entry-owner-name').value = entry.ownerName;
            document.getElementById('transmission-entry-phone').value = entry.phoneNumber || '';
            document.getElementById('transmission-entry-registration-number').value = entry.registrationNumber;
            document.getElementById('transmission-entry-car-type').value = entry.carType || '';
            document.getElementById('transmission-entry-tank-number').value = entry.tankNumber || '';
            document.getElementById('transmission-entry-serial-number').value = entry.serialNumber || '';
            document.getElementById('transmission-entry-notes').value = entry.notes || '';
        }
    } else {
        // إضافة عملية جديدة
        title.textContent = 'إضافة عملية جديدة';
        form.reset();
        document.getElementById('transmission-entry-operation-date').value = new Date().toISOString().split('T')[0];
    }

    // إضافة مستمع لتغيير تسمية تاريخ العملية في نافذة جدول الإرسال
    const transmissionTypeSelect = document.getElementById('transmission-entry-type');
    const transmissionDateLabel = document.getElementById('transmission-operation-date-label');

    if (transmissionTypeSelect && transmissionDateLabel) {
        // إزالة المستمع السابق إن وجد
        transmissionTypeSelect.removeEventListener('change', updateTransmissionDateLabel);
        // إضافة المستمع الجديد
        transmissionTypeSelect.addEventListener('change', updateTransmissionDateLabel);

        // تحديث التسمية الأولية
        updateTransmissionDateLabel.call(transmissionTypeSelect);
    }

    modal.style.display = 'block';
}

// إغلاق نافذة جدول الإرسال
function closeTransmissionEntryModal() {
    document.getElementById('transmission-entry-modal').style.display = 'none';
    editingTransmissionEntryId = null;
}

// تحديث تسمية تاريخ العملية في نافذة جدول الإرسال
function updateTransmissionDateLabel() {
    const transmissionDateLabel = document.getElementById('transmission-operation-date-label');
    if (!transmissionDateLabel) return;

    const selectedType = this.value;
    switch(selectedType) {
        case 'تركيب':
            transmissionDateLabel.textContent = 'تاريخ التركيب:';
            break;
        case 'مراقبة':
            transmissionDateLabel.textContent = 'تاريخ المراقبة:';
            break;
        default:
            transmissionDateLabel.textContent = 'تاريخ العملية:';
            break;
    }
}

// عرض نافذة الملاحظات
function showNotesModal(title, notes) {
    // إنشاء نافذة منبثقة للملاحظات
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${title}</h3>
                <button type="button" class="close-btn" onclick="this.closest('.modal').remove()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="notes-content" style="
                    background: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 8px;
                    padding: 15px;
                    margin: 10px 0;
                    min-height: 100px;
                    white-space: pre-wrap;
                    font-family: inherit;
                    line-height: 1.5;
                ">
                    ${notes || 'لا توجد ملاحظات'}
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn secondary" onclick="this.closest('.modal').remove()">
                    إغلاق
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // إغلاق النافذة عند النقر خارجها
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// حفظ عملية جدول الإرسال
function saveTransmissionEntry(event) {
    event.preventDefault();

    const entryData = {
        type: document.getElementById('transmission-entry-type').value,
        operationDate: document.getElementById('transmission-entry-operation-date').value,
        ownerName: document.getElementById('transmission-entry-owner-name').value,
        phoneNumber: document.getElementById('transmission-entry-phone').value,
        registrationNumber: document.getElementById('transmission-entry-registration-number').value,
        carType: document.getElementById('transmission-entry-car-type').value,
        tankNumber: document.getElementById('transmission-entry-tank-number').value,
        itemType: document.getElementById('transmission-entry-item-type').value,
        serialNumber: document.getElementById('transmission-entry-serial-number').value,
        notes: document.getElementById('transmission-entry-notes').value
    };

    if (typeof transmissionManager !== 'undefined') {
        if (editingTransmissionEntryId) {
            // تحديث عملية موجودة
            if (transmissionManager.updateEntry(editingTransmissionEntryId, entryData)) {
                showToast('تم تحديث العملية بنجاح', true);
            } else {
                showToast('حدث خطأ أثناء تحديث العملية', false);
            }
        } else {
            // إضافة عملية جديدة
            transmissionManager.addEntry(entryData);
            showToast('تم إضافة العملية بنجاح', true);
        }
    }

    closeTransmissionEntryModal();
}

// إضافة عملية تلقائياً عند إنشاء شهادة تركيب
function addInstallationToTransmissionTable(certificateData) {
    if (!certificateData) return;

    const entryData = {
        type: 'تركيب',
        operationDate: certificateData.operationDate || new Date().toISOString().split('T')[0],
        ownerName: certificateData.customerName,
        phoneNumber: certificateData.phoneNumber || '',
        registrationNumber: certificateData.vehicleNumber,
        carType: certificateData.vehicleType || '',
        tankNumber: certificateData.tankNumber || '',
        itemType: certificateData.tankType || '',
        serialNumber: certificateData.serialNumber || '',
        status: 'مكتمل',
        source: 'installation_certificate',
        sourceId: certificateData.id
    };

    // التحقق من عدم وجود العملية مسبقاً
    const exists = appData.transmissionTable.some(entry =>
        entry.sourceId === certificateData.id && entry.source === 'installation_certificate'
    );

    if (!exists) {
        transmissionManager.addEntry(entryData);
        console.log('تم إضافة عملية التركيب إلى جدول الإرسال تلقائياً');
    }
}

// إضافة عملية تلقائياً عند إنشاء شهادة مراقبة دورية
function addMonitoringToTransmissionTable(certificateData) {
    if (!certificateData) return;

    const entryData = {
        type: 'مراقبة',
        operationDate: certificateData.lastMonitoringDate || new Date().toISOString().split('T')[0],
        ownerName: certificateData.customerName,
        phoneNumber: certificateData.phoneNumber || '',
        registrationNumber: certificateData.vehicleNumber,
        carType: certificateData.vehicleType || '',
        tankNumber: certificateData.tankNumber || '',
        serialNumber: certificateData.serialNumber || '',
        status: 'مكتمل',
        source: 'monitoring_certificate',
        sourceId: certificateData.id
    };

    // التحقق من عدم وجود العملية مسبقاً
    const exists = appData.transmissionTable.some(entry =>
        entry.sourceId === certificateData.id && entry.source === 'monitoring_certificate'
    );

    if (!exists) {
        transmissionManager.addEntry(entryData);
        console.log('تم إضافة عملية المراقبة إلى جدول الإرسال تلقائياً');
    }
}

// إضافة عملية تلقائياً عند تجديد بطاقة غاز
function addCardRenewalToTransmissionTable(cardData) {
    if (!cardData) return;

    // البحث عن بيانات الزبون والسيارة
    const customer = appData.customers.find(c => c.id === cardData.customerId);
    const vehicle = appData.vehicles.find(v => v.id === cardData.vehicleId);

    const entryData = {
        type: 'تجديد',
        operationDate: cardData.issueDate || new Date().toISOString().split('T')[0],
        ownerName: customer?.name || cardData.customerName,
        phoneNumber: customer?.phone || '',
        registrationNumber: cardData.vehicleNumber || vehicle?.plateNumber,
        carType: vehicle?.type || '',
        tankNumber: cardData.tankNumber || '',
        serialNumber: cardData.serialNumber || '',
        status: 'مكتمل',
        source: 'card_renewal',
        sourceId: cardData.id
    };

    // التحقق من عدم وجود العملية مسبقاً
    const exists = appData.transmissionTable.some(entry =>
        entry.sourceId === cardData.id && entry.source === 'card_renewal'
    );

    if (!exists) {
        transmissionManager.addEntry(entryData);
        console.log('تم إضافة عملية تجديد البطاقة إلى جدول الإرسال تلقائياً');
    }
}

// إعداد مستمعي الأحداث لجدول الإرسال
document.addEventListener('DOMContentLoaded', () => {
    // نموذج إضافة/تعديل عملية
    const transmissionForm = document.getElementById('transmission-entry-form');
    if (transmissionForm) {
        transmissionForm.addEventListener('submit', saveTransmissionEntry);
    }

    // إغلاق النافذة بالضغط خارجها
    const transmissionModal = document.getElementById('transmission-entry-modal');
    if (transmissionModal) {
        transmissionModal.addEventListener('click', (e) => {
            if (e.target === transmissionModal) {
                closeTransmissionEntryModal();
            }
        });
    }

    // إغلاق النافذة بمفتاح Escape
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && transmissionModal && transmissionModal.style.display === 'block') {
            closeTransmissionEntryModal();
        }
    });

    // تحديث جدول الإرسال عند تحميل الصفحة
    if (typeof transmissionManager !== 'undefined') {
        transmissionManager.updateTable();
        transmissionManager.updateSummary();
    }
});

// دوال أدوات الاختبار والتطوير
function showLicenseCodes() {
    const licenseCodes = [
        'FUEL-2024-MAIN-0001 - الترخيص الرئيسي (سنة واحدة)',
        'FUEL-2024-DEMO-0001 - الترخيص التجريبي (30 يوم)',
        'FUEL-2024-TEST-0001 - ترخيص الاختبار (7 أيام)',
        'ABCD-1234-EFGH-5678 - ترخيص المطور (غير محدود)',
        'TEST-CODE-DEMO-2024 - ترخيص العرض (15 يوم)'
    ];

    const message = `أكواد الترخيص التجريبية المتاحة:\n\n${licenseCodes.join('\n\n')}`;

    showNotification('أكواد الترخيص التجريبية', message, 'info', 10000);

    // نسخ أول كود إلى الحافظة
    if (navigator.clipboard) {
        navigator.clipboard.writeText('FUEL-2024-MAIN-0001').then(() => {
            showNotification('تم النسخ', 'تم نسخ الكود الرئيسي إلى الحافظة', 'success');
        });
    }
}

function resetLoginData() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع بيانات تسجيل الدخول؟\n\nسيتم مسح:\n- أكواد الترخيص المحفوظة\n- بيانات المستخدم المحفوظة\n- إعدادات تسجيل الدخول')) {
        // مسح بيانات تسجيل الدخول
        localStorage.removeItem('appLicense');
        localStorage.removeItem('rememberedUser');
        localStorage.removeItem('activationRequests');
        localStorage.removeItem('userSession');

        showNotification('تم الإعادة', 'تم إعادة تعيين بيانات تسجيل الدخول بنجاح', 'success');
    }
}

function checkWindows8Compatibility() {
    const userAgent = navigator.userAgent;
    const isWindows = userAgent.includes('Windows');
    const isWindows8 = userAgent.includes('Windows NT 6.2') || userAgent.includes('Windows NT 6.3');

    let message = 'معلومات النظام:\n\n';
    message += `نظام التشغيل: ${userAgent}\n\n`;

    if (isWindows8) {
        message += '✅ تم اكتشاف Windows 8/8.1\n';
        message += 'الحالة: متوافق مع التحسينات الخاصة\n\n';
        message += 'الملفات المساعدة المتاحة:\n';
        message += '• check-compatibility.bat\n';
        message += '• fix-windows8-compatibility.bat\n';
        message += '• setup-windows8.bat\n';
        message += '• تعليمات-Windows8.txt';
    } else if (isWindows) {
        message += '✅ نظام Windows متوافق\n';
        message += 'الحالة: يعمل بالإعدادات العادية';
    } else {
        message += '⚠️ نظام غير Windows\n';
        message += 'الحالة: قد تحتاج لتحسينات إضافية';
    }

    showNotification('فحص التوافق', message, 'info', 8000);
}

// دالة مساعدة لعرض الإشعارات المطولة
function showNotification(title, message, type = 'info', duration = 5000) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-header">
            <h4>${title}</h4>
            <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="notification-body">
            <pre>${message}</pre>
        </div>
    `;

    // إضافة الأنماط
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#2ecc71' : type === 'error' ? '#e74c3c' : type === 'warning' ? '#f39c12' : '#3498db'};
        color: white;
        padding: 0;
        border-radius: 10px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        max-width: 400px;
        min-width: 300px;
        animation: slideInRight 0.3s ease-out;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    `;

    // إضافة أنماط للعناصر الداخلية
    const style = document.createElement('style');
    style.textContent = `
        .notification-header {
            padding: 15px 20px 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .notification-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }
        .notification-close {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            font-size: 14px;
            padding: 5px;
            border-radius: 50%;
            transition: background 0.2s;
        }
        .notification-close:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        .notification-body {
            padding: 15px 20px;
        }
        .notification-body pre {
            margin: 0;
            white-space: pre-wrap;
            font-family: inherit;
            font-size: 14px;
            line-height: 1.5;
        }
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
    `;

    if (!document.querySelector('#notification-styles')) {
        style.id = 'notification-styles';
        document.head.appendChild(style);
    }

    // إضافة الإشعار إلى الصفحة
    document.body.appendChild(notification);

    // إزالة الإشعار تلقائياً
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
}