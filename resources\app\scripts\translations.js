// نظام الترجمة المتعدد اللغات - عربي/فرنسي
// Système de traduction multilingue - Arabe/Français

const translations = {
    ar: {
        // العنوان الرئيسي
        appTitle: "نظام إدارة مؤسسة وقود المستقبل",
        appSubtitle: "Future Fuel Corporation Management System",
        
        // القائمة الرئيسية
        nav: {
            dashboard: "الرئيسية",
            gasCards: "بطاقات الغاز",
            appointments: "المواعيد",
            customers: "الزبائن",
            suppliers: "الموردين",
            inventory: "المخزون",
            sales: "المبيعات",
            purchases: "المشتريات",
            debts: "الديون",
            certificates: "الشهادات",
            transmissionTable: "جدول الإرسال",
            settings: "الإعدادات"
        },
        
        // لوحة التحكم
        dashboard: {
            title: "لوحة التحكم",
            gasCards: "بطاقات الغاز",
            totalCards: "عدد البطاقات",
            cardsToRenew: "بطاقات تحتاج للتجديد",
            appointments: "المواعيد",
            todayAppointments: "مواعيد اليوم",
            upcomingAppointments: "إجمالي المواعيد القادمة",
            customers: "الزبائن",
            totalCustomers: "إجمالي الزبائن",
            newCustomers: "زبائن جدد هذا الشهر",
            certificates: "الشهادات",
            totalCertificates: "إجمالي الشهادات",
            expiringSoon: "تنتهي قريباً"
        },
        
        // الأزرار العامة
        buttons: {
            add: "إضافة",
            edit: "تعديل",
            delete: "حذف",
            save: "حفظ",
            cancel: "إلغاء",
            search: "بحث",
            print: "طباعة",
            export: "تصدير",
            import: "استيراد",
            refresh: "تحديث",
            close: "إغلاق",
            submit: "إرسال",
            reset: "إعادة تعيين"
        },
        
        // النماذج
        forms: {
            name: "الاسم",
            firstName: "الاسم الأول",
            lastName: "اسم العائلة",
            email: "البريد الإلكتروني",
            phone: "رقم الهاتف",
            address: "العنوان",
            city: "المدينة",
            postalCode: "الرمز البريدي",
            date: "التاريخ",
            time: "الوقت",
            notes: "ملاحظات",
            status: "الحالة",
            type: "النوع",
            category: "الفئة",
            description: "الوصف",
            amount: "المبلغ",
            quantity: "الكمية",
            price: "السعر",
            total: "الإجمالي"
        },
        
        // الرسائل
        messages: {
            success: "تم بنجاح",
            error: "حدث خطأ",
            warning: "تحذير",
            info: "معلومات",
            confirm: "تأكيد",
            deleteConfirm: "هل أنت متأكد من الحذف؟",
            saveSuccess: "تم الحفظ بنجاح",
            deleteSuccess: "تم الحذف بنجاح",
            updateSuccess: "تم التحديث بنجاح",
            noData: "لا توجد بيانات",
            loading: "جاري التحميل...",
            processing: "جاري المعالجة..."
        },
        
        // الحالات
        status: {
            active: "نشط",
            inactive: "غير نشط",
            pending: "في الانتظار",
            completed: "مكتمل",
            cancelled: "ملغي",
            expired: "منتهي الصلاحية",
            valid: "صالح",
            invalid: "غير صالح"
        },
        
        // الأقسام
        sections: {
            gasCards: {
                title: "إدارة بطاقات الغاز",
                addCard: "إضافة بطاقة جديدة",
                cardNumber: "رقم البطاقة",
                expiryDate: "تاريخ الانتهاء",
                cardType: "نوع البطاقة",
                ownerName: "اسم المالك"
            },
            appointments: {
                title: "إدارة المواعيد",
                addAppointment: "إضافة موعد جديد",
                appointmentDate: "تاريخ الموعد",
                appointmentTime: "وقت الموعد",
                customerName: "اسم الزبون",
                serviceType: "نوع الخدمة"
            },
            customers: {
                title: "إدارة الزبائن",
                addCustomer: "إضافة زبون جديد",
                customerInfo: "معلومات الزبون",
                contactInfo: "معلومات الاتصال",
                customerHistory: "تاريخ الزبون"
            },
            certificates: {
                title: "إدارة الشهادات",
                addCertificate: "إضافة شهادة جديدة",
                certificateNumber: "رقم الشهادة",
                issueDate: "تاريخ الإصدار",
                expiryDate: "تاريخ الانتهاء",
                certificateType: "نوع الشهادة"
            }
        },
        
        // الإعدادات
        settings: {
            title: "الإعدادات",
            language: "اللغة",
            theme: "المظهر",
            notifications: "الإشعارات",
            backup: "النسخ الاحتياطي",
            security: "الأمان",
            about: "حول البرنامج"
        },
        
        // أخرى
        other: {
            connectionStatus: "حالة الاتصال",
            connected: "متصل",
            disconnected: "غير متصل",
            checkingConnection: "فحص الاتصال...",
            darkMode: "الوضع المظلم",
            lightMode: "الوضع الفاتح",
            notifications: "الإشعارات",
            profile: "الملف الشخصي",
            logout: "تسجيل الخروج"
        }
    },
    
    fr: {
        // Titre principal
        appTitle: "Système de Gestion Future Fuel Corporation",
        appSubtitle: "Future Fuel Corporation Management System",
        
        // Menu principal
        nav: {
            dashboard: "Tableau de bord",
            gasCards: "Cartes de gaz",
            appointments: "Rendez-vous",
            customers: "Clients",
            suppliers: "Fournisseurs",
            inventory: "Inventaire",
            sales: "Ventes",
            purchases: "Achats",
            debts: "Dettes",
            certificates: "Certificats",
            transmissionTable: "Table de transmission",
            settings: "Paramètres"
        },
        
        // Tableau de bord
        dashboard: {
            title: "Tableau de bord",
            gasCards: "Cartes de gaz",
            totalCards: "Nombre de cartes",
            cardsToRenew: "Cartes à renouveler",
            appointments: "Rendez-vous",
            todayAppointments: "Rendez-vous d'aujourd'hui",
            upcomingAppointments: "Total des rendez-vous à venir",
            customers: "Clients",
            totalCustomers: "Total des clients",
            newCustomers: "Nouveaux clients ce mois",
            certificates: "Certificats",
            totalCertificates: "Total des certificats",
            expiringSoon: "Expire bientôt"
        },
        
        // Boutons généraux
        buttons: {
            add: "Ajouter",
            edit: "Modifier",
            delete: "Supprimer",
            save: "Enregistrer",
            cancel: "Annuler",
            search: "Rechercher",
            print: "Imprimer",
            export: "Exporter",
            import: "Importer",
            refresh: "Actualiser",
            close: "Fermer",
            submit: "Soumettre",
            reset: "Réinitialiser"
        },
        
        // Formulaires
        forms: {
            name: "Nom",
            firstName: "Prénom",
            lastName: "Nom de famille",
            email: "Email",
            phone: "Téléphone",
            address: "Adresse",
            city: "Ville",
            postalCode: "Code postal",
            date: "Date",
            time: "Heure",
            notes: "Notes",
            status: "Statut",
            type: "Type",
            category: "Catégorie",
            description: "Description",
            amount: "Montant",
            quantity: "Quantité",
            price: "Prix",
            total: "Total"
        },
        
        // Messages
        messages: {
            success: "Succès",
            error: "Erreur",
            warning: "Avertissement",
            info: "Information",
            confirm: "Confirmation",
            deleteConfirm: "Êtes-vous sûr de vouloir supprimer?",
            saveSuccess: "Enregistré avec succès",
            deleteSuccess: "Supprimé avec succès",
            updateSuccess: "Mis à jour avec succès",
            noData: "Aucune donnée",
            loading: "Chargement...",
            processing: "Traitement en cours..."
        },
        
        // Statuts
        status: {
            active: "Actif",
            inactive: "Inactif",
            pending: "En attente",
            completed: "Terminé",
            cancelled: "Annulé",
            expired: "Expiré",
            valid: "Valide",
            invalid: "Invalide"
        },
        
        // Sections
        sections: {
            gasCards: {
                title: "Gestion des cartes de gaz",
                addCard: "Ajouter une nouvelle carte",
                cardNumber: "Numéro de carte",
                expiryDate: "Date d'expiration",
                cardType: "Type de carte",
                ownerName: "Nom du propriétaire"
            },
            appointments: {
                title: "Gestion des rendez-vous",
                addAppointment: "Ajouter un nouveau rendez-vous",
                appointmentDate: "Date du rendez-vous",
                appointmentTime: "Heure du rendez-vous",
                customerName: "Nom du client",
                serviceType: "Type de service"
            },
            customers: {
                title: "Gestion des clients",
                addCustomer: "Ajouter un nouveau client",
                customerInfo: "Informations client",
                contactInfo: "Informations de contact",
                customerHistory: "Historique client"
            },
            certificates: {
                title: "Gestion des certificats",
                addCertificate: "Ajouter un nouveau certificat",
                certificateNumber: "Numéro de certificat",
                issueDate: "Date d'émission",
                expiryDate: "Date d'expiration",
                certificateType: "Type de certificat"
            }
        },
        
        // Paramètres
        settings: {
            title: "Paramètres",
            language: "Langue",
            theme: "Thème",
            notifications: "Notifications",
            backup: "Sauvegarde",
            security: "Sécurité",
            about: "À propos"
        },
        
        // Autres
        other: {
            connectionStatus: "État de connexion",
            connected: "Connecté",
            disconnected: "Déconnecté",
            checkingConnection: "Vérification de la connexion...",
            darkMode: "Mode sombre",
            lightMode: "Mode clair",
            notifications: "Notifications",
            profile: "Profil",
            logout: "Déconnexion"
        }
    }
};

// متغير اللغة الحالية
let currentLanguage = localStorage.getItem('language') || 'ar';

// دالة الحصول على الترجمة
function t(key) {
    const keys = key.split('.');
    let value = translations[currentLanguage];
    
    for (const k of keys) {
        if (value && value[k]) {
            value = value[k];
        } else {
            console.warn(`Translation key not found: ${key}`);
            return key;
        }
    }
    
    return value;
}

// دالة تغيير اللغة
function changeLanguage(lang) {
    if (translations[lang]) {
        currentLanguage = lang;
        localStorage.setItem('language', lang);
        updatePageLanguage();
        updateDirection();
    }
}

// دالة تحديث اتجاه الصفحة
function updateDirection() {
    const html = document.documentElement;
    const body = document.body;
    
    if (currentLanguage === 'ar') {
        html.setAttribute('lang', 'ar');
        html.setAttribute('dir', 'rtl');
        body.style.direction = 'rtl';
    } else {
        html.setAttribute('lang', 'fr');
        html.setAttribute('dir', 'ltr');
        body.style.direction = 'ltr';
    }
}

// دالة تحديث لغة الصفحة
function updatePageLanguage() {
    // تحديث العنوان
    document.title = t('appTitle');
    
    // تحديث العناصر بـ data-translate
    document.querySelectorAll('[data-translate]').forEach(element => {
        const key = element.getAttribute('data-translate');
        element.textContent = t(key);
    });
    
    // تحديث العناصر بـ data-translate-placeholder
    document.querySelectorAll('[data-translate-placeholder]').forEach(element => {
        const key = element.getAttribute('data-translate-placeholder');
        element.placeholder = t(key);
    });
    
    // تحديث العناصر بـ data-translate-title
    document.querySelectorAll('[data-translate-title]').forEach(element => {
        const key = element.getAttribute('data-translate-title');
        element.title = t(key);
    });
}

// تهيئة اللغة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateDirection();
    updatePageLanguage();
});

// تصدير الدوال للاستخدام العام
window.t = t;
window.changeLanguage = changeLanguage;
window.currentLanguage = currentLanguage;
