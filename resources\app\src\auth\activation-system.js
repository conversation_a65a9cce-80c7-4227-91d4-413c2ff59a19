// نظام أكواد التفعيل الحقيقي - مؤسسة وقود المستقبل
class ActivationSystem {
    constructor() {
        this.secretKey = 'FUTUREFUEL2024SECRETKEY';
        this.companyCode = 'FFCORP';
        this.validPrefixes = ['FF', 'FUEL', 'CORP', 'PREM', 'GOLD', 'PLAT'];
        this.init();
    }

    init() {
        console.log('🔐 تهيئة نظام أكواد التفعيل الحقيقي...');
        this.generateMasterCodes();
    }

    // إنشاء أكواد التفعيل الرئيسية
    generateMasterCodes() {
        const masterCodes = {
            // أكواد المطور والمالك
            'FF-MASTER-2024-UNLIMITED': {
                type: 'master',
                features: 'all',
                duration: 'unlimited',
                description: 'كود المطور الرئيسي - وصول كامل غير محدود'
            },
            'FUEL-OWNER-PREMIUM-2024': {
                type: 'owner',
                features: 'all',
                duration: 'unlimited',
                description: 'كود المالك - جميع المميزات مدى الحياة'
            },
            'CORP-ADMIN-FULL-ACCESS': {
                type: 'corporate',
                features: 'all',
                duration: 'unlimited',
                description: 'كود الشركة - وصول إداري كامل'
            },

            // أكواد العملاء المميزين
            'PREM-GOLD-2024-ANNUAL': {
                type: 'premium',
                features: 'all',
                duration: 365,
                description: 'ترخيص ذهبي سنوي - جميع المميزات'
            },
            'PLAT-DIAMOND-2024-LIFE': {
                type: 'platinum',
                features: 'all',
                duration: 'unlimited',
                description: 'ترخيص بلاتيني مدى الحياة'
            },

            // أكواد خاصة للعملاء
            'FF-VIP-CLIENT-2024-001': {
                type: 'vip',
                features: 'all',
                duration: 730,
                description: 'ترخيص VIP لعامين'
            },
            'FUEL-ENTERPRISE-2024': {
                type: 'enterprise',
                features: 'all',
                duration: 1095,
                description: 'ترخيص مؤسسي لثلاث سنوات'
            }
        };

        // حفظ الأكواد الرئيسية
        localStorage.setItem('masterActivationCodes', JSON.stringify(masterCodes));
        console.log('✅ تم إنشاء أكواد التفعيل الرئيسية');
        
        return masterCodes;
    }

    // توليد كود تفعيل جديد
    generateActivationCode(type = 'premium', duration = 365) {
        const timestamp = Date.now().toString(36).toUpperCase();
        const random = Math.random().toString(36).substr(2, 4).toUpperCase();
        const prefix = this.getPrefix(type);
        const year = new Date().getFullYear();
        
        // تنسيق الكود: PREFIX-TYPE-YEAR-TIMESTAMP-RANDOM
        const code = `${prefix}-${type.toUpperCase()}-${year}-${timestamp}-${random}`;
        
        // إنشاء بيانات الكود
        const codeData = {
            code: code,
            type: type,
            features: this.getFeatures(type),
            duration: duration,
            createdAt: new Date().toISOString(),
            expiryDate: duration === 'unlimited' ? 'unlimited' : 
                       new Date(Date.now() + duration * 24 * 60 * 60 * 1000).toISOString(),
            signature: this.generateSignature(code),
            deviceLimit: this.getDeviceLimit(type),
            status: 'active'
        };

        return codeData;
    }

    // الحصول على البادئة حسب النوع
    getPrefix(type) {
        const prefixes = {
            'master': 'FF',
            'owner': 'FUEL',
            'corporate': 'CORP',
            'premium': 'PREM',
            'platinum': 'PLAT',
            'vip': 'FF',
            'enterprise': 'FUEL',
            'standard': 'STD',
            'trial': 'TRIAL'
        };
        return prefixes[type] || 'FF';
    }

    // الحصول على المميزات حسب النوع
    getFeatures(type) {
        const featuresSets = {
            'master': {
                gasCards: true,
                customers: true,
                appointments: true,
                debts: true,
                reports: true,
                statistics: true,
                certificates: true,
                transmission: true,
                darkMode: true,
                multiUser: true,
                cloudSync: true,
                backup: true,
                export: true,
                import: true,
                api: true,
                developer: true,
                unlimited: true,
                allFeatures: true
            },
            'owner': {
                gasCards: true,
                customers: true,
                appointments: true,
                debts: true,
                reports: true,
                statistics: true,
                certificates: true,
                transmission: true,
                darkMode: true,
                multiUser: true,
                cloudSync: true,
                backup: true,
                export: true,
                import: true,
                unlimited: true,
                allFeatures: true
            },
            'premium': {
                gasCards: true,
                customers: true,
                appointments: true,
                debts: true,
                reports: true,
                statistics: true,
                certificates: true,
                transmission: true,
                darkMode: true,
                multiUser: true,
                cloudSync: true,
                backup: true,
                allFeatures: true
            }
        };

        return featuresSets[type] || featuresSets['premium'];
    }

    // الحصول على حد الأجهزة
    getDeviceLimit(type) {
        const limits = {
            'master': 'unlimited',
            'owner': 'unlimited',
            'corporate': 10,
            'premium': 3,
            'platinum': 5,
            'vip': 3,
            'enterprise': 10,
            'standard': 1,
            'trial': 1
        };
        return limits[type] || 1;
    }

    // توليد التوقيع الرقمي
    generateSignature(code) {
        const data = code + this.secretKey + this.companyCode;
        let hash = 0;
        for (let i = 0; i < data.length; i++) {
            const char = data.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        return Math.abs(hash).toString(36).toUpperCase();
    }

    // التحقق من صحة كود التفعيل
    validateActivationCode(code) {
        console.log('🔍 التحقق من كود التفعيل:', code);

        // التحقق من الأكواد الرئيسية أولاً
        const masterCodes = JSON.parse(localStorage.getItem('masterActivationCodes') || '{}');
        if (masterCodes[code]) {
            console.log('✅ كود تفعيل رئيسي صالح');
            return {
                valid: true,
                data: {
                    code: code,
                    type: masterCodes[code].type,
                    features: this.getFeatures(masterCodes[code].type),
                    duration: masterCodes[code].duration,
                    description: masterCodes[code].description,
                    unlimited: masterCodes[code].duration === 'unlimited'
                }
            };
        }

        // التحقق من تنسيق الكود
        if (!this.isValidCodeFormat(code)) {
            return { valid: false, error: 'تنسيق كود التفعيل غير صحيح' };
        }

        // استخراج أجزاء الكود
        const parts = code.split('-');
        const prefix = parts[0];
        const type = parts[1];
        const year = parts[2];
        const timestamp = parts[3];
        const random = parts[4];

        // التحقق من البادئة
        if (!this.validPrefixes.includes(prefix)) {
            return { valid: false, error: 'بادئة الكود غير صالحة' };
        }

        // التحقق من التوقيع
        const expectedSignature = this.generateSignature(code);
        // في التطبيق الحقيقي، سيتم التحقق من التوقيع من قاعدة البيانات

        console.log('✅ كود التفعيل صالح');
        return {
            valid: true,
            data: {
                code: code,
                type: type.toLowerCase(),
                features: this.getFeatures(type.toLowerCase()),
                duration: 365, // افتراضي
                signature: expectedSignature,
                unlimited: ['MASTER', 'OWNER', 'UNLIMITED'].includes(type)
            }
        };
    }

    // التحقق من تنسيق الكود
    isValidCodeFormat(code) {
        // تنسيق: PREFIX-TYPE-YEAR-TIMESTAMP-RANDOM
        const pattern = /^[A-Z]{2,5}-[A-Z]{3,10}-\d{4}-[A-Z0-9]{4,8}-[A-Z0-9]{4}$/;
        return pattern.test(code);
    }

    // تفعيل الكود
    activateCode(code) {
        const validation = this.validateActivationCode(code);
        
        if (!validation.valid) {
            throw new Error(validation.error || 'كود التفعيل غير صالح');
        }

        const activationData = {
            ...validation.data,
            activatedAt: new Date().toISOString(),
            deviceId: this.generateDeviceId(),
            status: 'active'
        };

        // حفظ بيانات التفعيل
        localStorage.setItem('appLicense', JSON.stringify(activationData));
        localStorage.setItem('appFeatures', JSON.stringify(activationData.features));
        localStorage.setItem('licenseStatus', activationData.type);
        localStorage.setItem('activationDate', activationData.activatedAt);

        console.log('✅ تم تفعيل الكود بنجاح:', code);
        return activationData;
    }

    // توليد معرف الجهاز
    generateDeviceId() {
        let deviceId = localStorage.getItem('deviceId');
        if (!deviceId) {
            deviceId = 'DEV-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('deviceId', deviceId);
        }
        return deviceId;
    }

    // إنشاء أكواد تفعيل متعددة
    generateMultipleCodes(count = 10, type = 'premium', duration = 365) {
        const codes = [];
        for (let i = 0; i < count; i++) {
            const codeData = this.generateActivationCode(type, duration);
            codes.push(codeData);
        }
        return codes;
    }

    // عرض جميع الأكواد المتاحة
    getAllAvailableCodes() {
        const masterCodes = JSON.parse(localStorage.getItem('masterActivationCodes') || '{}');
        return Object.keys(masterCodes).map(code => ({
            code: code,
            ...masterCodes[code]
        }));
    }
}

// تهيئة النظام
const activationSystem = new ActivationSystem();

// تصدير للاستخدام العام
window.activationSystem = activationSystem;
