/* أنماط تسجيل الدخول المميزة */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --shadow-light: 0 10px 30px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 15px 40px rgba(0, 0, 0, 0.2);
    --shadow-heavy: 0 20px 50px rgba(0, 0, 0, 0.3);
    --border-radius: 15px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    overflow: hidden;
    height: 100vh;
    background: var(--gradient-primary);
}

/* الخلفية المتحركة */
.animated-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: var(--gradient-primary);
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 20%;
    right: 15%;
    animation-delay: 1s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    bottom: 30%;
    left: 20%;
    animation-delay: 2s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
}

.shape-5 {
    width: 140px;
    height: 140px;
    top: 50%;
    left: 50%;
    animation-delay: 4s;
    transform: translate(-50%, -50%);
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* حاوي تسجيل الدخول */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

/* بطاقة تسجيل الدخول */
.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    padding: 40px;
    width: 100%;
    max-width: 450px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* هيدر تسجيل الدخول */
.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo-container {
    margin-bottom: 20px;
}

.logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    box-shadow: var(--shadow-medium);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.login-header h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.subtitle {
    color: var(--secondary-color);
    font-size: 1rem;
    margin-bottom: 10px;
}

.version-info {
    background: var(--gradient-secondary);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    display: inline-block;
    font-weight: 500;
}

/* حالة الترخيص */
.license-status {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.status-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    color: #721c24;
    font-weight: 600;
}

.status-indicator i {
    font-size: 1.2rem;
    animation: blink 2s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.5; }
}

/* نموذج تسجيل الدخول */
.login-form {
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-size: 1rem;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.9);
}

.form-group input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    transform: translateY(-2px);
}

.password-input {
    position: relative;
}

.toggle-password {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    font-size: 1.1rem;
    transition: var(--transition);
}

.toggle-password:hover {
    color: var(--secondary-color);
}

/* زر إظهار حقل كود الترخيص */
.license-toggle-container {
    margin-bottom: 20px;
}

.license-toggle-btn {
    width: 100%;
    padding: 15px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    overflow: hidden;
}

.license-toggle-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.license-toggle-btn:active {
    transform: translateY(0);
}

.license-toggle-btn .toggle-icon {
    transition: transform 0.3s ease;
    font-size: 0.9rem;
}

.license-toggle-btn.active .toggle-icon {
    transform: rotate(180deg);
}

.license-toggle-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.license-toggle-btn:hover::before {
    left: 100%;
}

.license-hint {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    padding: 8px 12px;
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 6px;
    color: #5a67d8;
    font-size: 0.85rem;
    animation: pulse-hint 2s infinite;
}

@keyframes pulse-hint {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* حقل كود الترخيص */
.license-field-container {
    animation: slideDown 0.4s ease-out;
    border: 2px solid #667eea;
    border-radius: 12px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    position: relative;
}

.license-field-container::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    z-index: -1;
    opacity: 0.1;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 500px;
    }
}

.license-field-container.hiding {
    animation: slideUp 0.3s ease-in forwards;
}

@keyframes slideUp {
    from {
        opacity: 1;
        transform: translateY(0);
        max-height: 500px;
    }
    to {
        opacity: 0;
        transform: translateY(-20px);
        max-height: 0;
    }
}

.license-input {
    position: relative;
}

.license-input input {
    padding-left: 50px;
    font-family: 'Courier New', monospace;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.validate-license {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #7f8c8d;
    cursor: pointer;
    font-size: 1.1rem;
    transition: var(--transition);
    padding: 5px;
    border-radius: 50%;
}

.validate-license:hover {
    color: var(--secondary-color);
    background: rgba(52, 152, 219, 0.1);
}

.validate-license.validating {
    color: var(--warning-color);
    animation: spin 1s linear infinite;
}

.validate-license.valid {
    color: var(--success-color);
}

.validate-license.invalid {
    color: var(--accent-color);
}

/* مؤشر حالة الترخيص */
.license-status-indicator {
    margin-top: 8px;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    transition: var(--transition);
    background: #f8f9fa;
    border: 1px solid #e9ecef;
}

.license-status-indicator.pending {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.license-status-indicator.valid {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.license-status-indicator.invalid {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.license-status-indicator .status-icon {
    margin-left: 5px;
}

/* تحسين تنسيق كود الترخيص */
#license-code {
    font-size: 1rem;
    font-weight: 600;
}

#license-code::placeholder {
    font-family: inherit;
    font-weight: normal;
    letter-spacing: normal;
    text-transform: none;
}

/* تحسينات إضافية لحقل كود الترخيص */
.license-input input:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.license-input input.valid {
    border-color: var(--success-color);
    background: rgba(46, 204, 113, 0.05);
}

.license-input input.invalid {
    border-color: var(--accent-color);
    background: rgba(231, 76, 60, 0.05);
}

/* تأثيرات متحركة للتحقق */
@keyframes pulse-green {
    0% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(46, 204, 113, 0); }
    100% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0); }
}

@keyframes pulse-red {
    0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
    100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
}

.validate-license.valid {
    animation: pulse-green 1.5s ease-out;
}

.validate-license.invalid {
    animation: pulse-red 1.5s ease-out;
}

/* تحسين شكل مؤشر الحالة */
.license-status-indicator {
    display: flex;
    align-items: center;
    justify-content: space-between;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.license-status-indicator.valid {
    animation: slideIn 0.3s ease-out, pulse-green 1s ease-out 0.3s;
}

.license-status-indicator.invalid {
    animation: slideIn 0.3s ease-out, pulse-red 1s ease-out 0.3s;
}

/* أزرار إجراءات الترخيص */
.license-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(102, 126, 234, 0.2);
    gap: 10px;
}

.btn-link {
    background: none;
    border: none;
    color: #667eea;
    font-size: 0.85rem;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 6px;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 6px;
    text-decoration: none;
}

.btn-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #5a67d8;
    transform: translateY(-1px);
}

.btn-link:active {
    transform: translateY(0);
}

/* تحسينات إضافية للحقل المخفي */
.license-field-container.hidden {
    display: none !important;
}

/* تأثير التركيز المحسن */
.license-field-container input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
    background: white;
}

/* تحسين شكل التسمية */
.license-field-container label {
    color: #667eea;
    font-weight: 700;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.license-field-container label i {
    font-size: 1.1rem;
}

/* تأثير خاص عند عدم وجود ترخيص */
.no-license-warning {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.no-license-warning i {
    font-size: 1.2rem;
}

/* حالة الترخيص المحفوظ */
.saved-license-status {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-medium);
    animation: slideDown 0.4s ease-out;
}

.saved-license-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.saved-license-info i {
    font-size: 2rem;
    opacity: 0.9;
}

.license-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.license-title {
    font-weight: 700;
    font-size: 1.1rem;
}

.license-code {
    font-family: 'Courier New', monospace;
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9rem;
    letter-spacing: 1px;
}

.license-expiry {
    font-size: 0.85rem;
    opacity: 0.9;
}

.change-license-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
}

.change-license-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 480px) {
    .license-actions {
        flex-direction: column;
        gap: 8px;
    }

    .btn-link {
        width: 100%;
        justify-content: center;
    }

    .license-toggle-btn {
        padding: 12px 16px;
        font-size: 0.9rem;
    }

    .saved-license-status {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .saved-license-info {
        flex-direction: column;
        gap: 10px;
    }

    .change-license-btn {
        width: 100%;
        justify-content: center;
    }
}

/* خيارات النموذج */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--primary-color);
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e1e8ed;
    border-radius: 4px;
    position: relative;
    transition: var(--transition);
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
}

.forgot-password {
    color: var(--secondary-color);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.forgot-password:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

/* زر تسجيل الدخول */
.login-btn {
    width: 100%;
    padding: 15px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.login-btn:active {
    transform: translateY(0);
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    display: none;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* أزرار الإجراءات */
.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 25px;
}

.action-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.activation-btn {
    background: var(--gradient-success);
    color: white;
}

.contact-btn {
    background: var(--gradient-secondary);
    color: white;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

/* فوتر تسجيل الدخول */
.login-footer {
    text-align: center;
    border-top: 1px solid #e1e8ed;
    padding-top: 20px;
}

.system-info {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    font-size: 0.8rem;
    color: #7f8c8d;
}

.separator {
    color: #bdc3c7;
}

.copyright {
    font-size: 0.8rem;
    color: #95a5a6;
}

/* النوافذ المنبثقة */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
}

.modal.show {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-heavy);
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px 20px;
    border-bottom: 1px solid #e1e8ed;
}

.modal-header h2 {
    color: var(--primary-color);
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #7f8c8d;
    cursor: pointer;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: #f8f9fa;
    color: var(--accent-color);
}

/* نموذج التفعيل */
.activation-form {
    padding: 30px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.activation-form .form-group {
    margin-bottom: 20px;
}

.activation-form label {
    display: block;
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.activation-form input,
.activation-form select,
.activation-form textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: var(--transition);
}

.activation-form input:focus,
.activation-form select:focus,
.activation-form textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 25px;
}

.submit-btn,
.cancel-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.submit-btn {
    background: var(--gradient-success);
    color: white;
}

.cancel-btn {
    background: #e9ecef;
    color: var(--primary-color);
}

.submit-btn:hover,
.cancel-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

/* معلومات المطور */
.contact-modal-content {
    max-width: 400px;
}

.developer-info {
    padding: 30px;
    text-align: center;
    border-bottom: 1px solid #e1e8ed;
}

.developer-avatar {
    margin-bottom: 20px;
}

.developer-avatar i {
    font-size: 4rem;
    color: var(--secondary-color);
}

.developer-details h3 {
    color: var(--primary-color);
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.title {
    color: var(--secondary-color);
    font-weight: 600;
    margin-bottom: 10px;
}

.description {
    color: #7f8c8d;
    font-size: 0.9rem;
    line-height: 1.5;
}

.contact-methods {
    padding: 20px 30px;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border-radius: 10px;
    text-decoration: none;
    transition: var(--transition);
    margin-bottom: 10px;
}

.contact-method.phone {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.contact-method.whatsapp {
    background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
    color: white;
}

.contact-method:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.contact-method i {
    font-size: 1.5rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
}

.label {
    font-size: 0.8rem;
    opacity: 0.9;
}

.value {
    font-size: 1.1rem;
    font-weight: 600;
}

.contact-note {
    padding: 20px 30px;
    background: #f8f9fa;
    border-top: 1px solid #e1e8ed;
    display: flex;
    align-items: center;
    gap: 10px;
}

.contact-note i {
    color: var(--secondary-color);
}

.contact-note p {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

/* التجاوب */
@media (max-width: 768px) {
    .login-card {
        padding: 30px 20px;
        margin: 10px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .modal-content {
        margin: 10px;
        max-height: 95vh;
    }
}

/* أنماط نموذج تبديل الترخيص */
.change-license-form {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #667eea;
    border-radius: 15px;
    padding: 25px;
    margin-top: 20px;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.change-license-form h4 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.change-license-form .form-group {
    margin-bottom: 20px;
}

.change-license-form .form-group label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    color: #2c3e50;
    font-weight: 600;
}

.change-license-form .license-input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1rem;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    font-family: 'Courier New', monospace;
}

.change-license-form .license-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.change-license-form .validate-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 15px;
}

.change-license-form .validate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.change-license-form .validate-btn.success {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

.change-license-form .validate-btn.error {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
}

.suggested-codes {
    background: white;
    border: 1px solid #e1e8ed;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}

.suggested-codes p {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-weight: 600;
}

.code-suggestions {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.suggestion-btn {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    padding: 12px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: right;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #2c3e50;
}

.suggestion-btn:hover {
    border-color: #667eea;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.suggestion-btn small {
    display: block;
    font-size: 0.7rem;
    opacity: 0.8;
    margin-top: 5px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.save-license-btn, .cancel-change-btn {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.save-license-btn {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
    color: white;
}

.save-license-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.cancel-change-btn {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    color: white;
}

.cancel-change-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(149, 165, 166, 0.3);
}
