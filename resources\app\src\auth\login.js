// نظام تسجيل الدخول والمصادقة
class LoginSystem {
    constructor() {
        this.isAuthenticated = false;
        this.currentUser = null;
        this.licenseStatus = 'inactive';
        this.init();
    }

    init() {
        console.log('🔐 تهيئة نظام تسجيل الدخول...');
        this.setupEventListeners();
        this.updateSystemTime();
        this.checkConnectionStatus();
        this.loadStatesData();
        this.checkLicenseStatus();
        this.loadSavedLicenseCode();

        // تحديث الوقت كل ثانية
        setInterval(() => this.updateSystemTime(), 1000);

        // فحص الاتصال كل 30 ثانية
        setInterval(() => this.checkConnectionStatus(), 30000);
    }

    setupEventListeners() {
        // نموذج تسجيل الدخول
        const loginForm = document.getElementById('login-form');
        const togglePassword = document.getElementById('toggle-password');
        const validateLicense = document.getElementById('validate-license');
        const licenseCodeInput = document.getElementById('license-code');
        const licenseToggleBtn = document.getElementById('license-toggle-btn');
        const hideLicenseField = document.getElementById('hide-license-field');
        const showDemoCodes = document.getElementById('show-demo-codes');
        const activationBtn = document.getElementById('activation-btn');
        const contactBtn = document.getElementById('contact-btn');

        // النوافذ المنبثقة
        const activationModal = document.getElementById('activation-modal');
        const contactModal = document.getElementById('contact-modal');
        const closeActivationModal = document.getElementById('close-activation-modal');
        const closeContactModal = document.getElementById('close-contact-modal');
        const cancelActivation = document.getElementById('cancel-activation');

        // نموذج التفعيل
        const activationForm = document.getElementById('activation-form');
        const stateSelect = document.getElementById('state');
        const municipalitySelect = document.getElementById('municipality');

        // أحداث تسجيل الدخول
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => this.handleLogin(e));
        }

        if (togglePassword) {
            togglePassword.addEventListener('click', () => this.togglePasswordVisibility());
        }

        // التحقق من كود الترخيص
        if (validateLicense) {
            validateLicense.addEventListener('click', () => {
                this.validateLicenseCode();
            });
        }

        // تنسيق كود الترخيص أثناء الكتابة
        if (licenseCodeInput) {
            licenseCodeInput.addEventListener('input', (e) => {
                this.formatLicenseCode(e);
            });

            licenseCodeInput.addEventListener('blur', () => {
                this.validateLicenseCode();
            });

            licenseCodeInput.addEventListener('paste', (e) => {
                setTimeout(() => this.formatLicenseCode(e), 10);
            });
        }

        // إظهار/إخفاء حقل كود الترخيص
        if (licenseToggleBtn) {
            licenseToggleBtn.addEventListener('click', () => {
                this.toggleLicenseField();
            });
        }

        if (hideLicenseField) {
            hideLicenseField.addEventListener('click', () => {
                this.hideLicenseField();
            });
        }

        if (showDemoCodes) {
            showDemoCodes.addEventListener('click', () => {
                this.showDemoCodesModal();
            });
        }

        // أحداث النوافذ المنبثقة
        if (activationBtn) {
            activationBtn.addEventListener('click', () => this.showActivationModal());
        }

        if (contactBtn) {
            contactBtn.addEventListener('click', () => this.showContactModal());
        }

        if (closeActivationModal) {
            closeActivationModal.addEventListener('click', () => this.hideActivationModal());
        }

        if (closeContactModal) {
            closeContactModal.addEventListener('click', () => this.hideContactModal());
        }

        if (cancelActivation) {
            cancelActivation.addEventListener('click', () => this.hideActivationModal());
        }

        // إغلاق النوافذ بالنقر خارجها
        if (activationModal) {
            activationModal.addEventListener('click', (e) => {
                if (e.target === activationModal) this.hideActivationModal();
            });
        }

        if (contactModal) {
            contactModal.addEventListener('click', (e) => {
                if (e.target === contactModal) this.hideContactModal();
            });
        }

        // نموذج التفعيل
        if (activationForm) {
            activationForm.addEventListener('submit', (e) => this.handleActivationRequest(e));
        }

        if (stateSelect) {
            stateSelect.addEventListener('change', () => this.updateMunicipalities());
        }

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideAllModals();
            }
        });
    }

    async handleLogin(event) {
        event.preventDefault();

        const loginBtn = document.getElementById('login-btn');
        const spinner = document.getElementById('login-spinner');
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        const licenseCode = document.getElementById('license-code').value;
        const rememberMe = document.getElementById('remember-me').checked;

        // تعطيل الزر وإظهار التحميل
        loginBtn.disabled = true;
        spinner.style.display = 'block';

        try {
            console.log('🔑 محاولة تسجيل الدخول...');

            // التحقق من وجود كود الترخيص
            const licenseFieldVisible = this.isLicenseFieldVisible();

            if (licenseFieldVisible) {
                // التحقق من كود الترخيص إذا كان الحقل ظاهراً
                if (!licenseCode || !this.isValidLicenseFormat(licenseCode)) {
                    this.showError('يرجى إدخال كود ترخيص صالح');
                    this.showLicenseField(); // إظهار الحقل إذا لم يكن ظاهراً
                    return;
                }

                // التحقق من صحة كود الترخيص
                const licenseValidation = await this.validateLicenseCode(licenseCode);
                if (!licenseValidation.valid) {
                    this.showError(licenseValidation.message || 'كود الترخيص غير صالح');
                    return;
                }
            } else {
                // التحقق من وجود ترخيص محفوظ مسبقاً
                const savedLicense = this.getSavedLicense();
                if (!savedLicense || !this.isLicenseValid(savedLicense)) {
                    this.showError('يرجى إدخال كود الترخيص أولاً');
                    this.showLicenseField();
                    return;
                }
            }

            // محاكاة التحقق من بيانات الاعتماد
            await this.simulateLogin(username, password);

            // حفظ كود الترخيص
            this.saveLicenseCode(licenseCode);

            // حفظ بيانات المستخدم
            if (rememberMe) {
                localStorage.setItem('rememberedUser', username);
            }

            // تسجيل الدخول بنجاح
            this.isAuthenticated = true;
            this.currentUser = { username, loginTime: new Date() };

            // تفعيل جميع المميزات تلقائياً
            this.enableAllFeatures();

            this.showSuccess('تم تسجيل الدخول بنجاح! جاري تحميل التطبيق...');

            // انتقال إلى التطبيق الرئيسي
            setTimeout(() => {
                this.redirectToMainApp();
            }, 2000);

        } catch (error) {
            console.error('❌ خطأ في تسجيل الدخول:', error);
            this.showError(error.message || 'حدث خطأ أثناء تسجيل الدخول');
        } finally {
            // إعادة تفعيل الزر وإخفاء التحميل
            loginBtn.disabled = false;
            spinner.style.display = 'none';
        }
    }

    async simulateLogin(username, password) {
        // محاكاة تأخير الشبكة
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // بيانات اعتماد افتراضية للاختبار
        const validCredentials = [
            { username: 'admin', password: 'admin123' },
            { username: 'user', password: 'user123' },
            { username: 'manager', password: 'manager123' }
        ];

        const isValid = validCredentials.some(cred => 
            cred.username === username && cred.password === password
        );

        if (!isValid) {
            throw new Error('اسم المستخدم أو كلمة المرور غير صحيحة');
        }

        return true;
    }

    async validateLicense() {
        // محاكاة فحص الترخيص
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // فحص الترخيص المحلي
        const localLicense = localStorage.getItem('appLicense');
        if (!localLicense) {
            return false;
        }

        try {
            const license = JSON.parse(localLicense);
            const now = new Date();
            const expiryDate = new Date(license.expiryDate);
            
            return now < expiryDate && license.status === 'active';
        } catch {
            return false;
        }
    }

    togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const toggleBtn = document.getElementById('toggle-password');
        const icon = toggleBtn.querySelector('i');

        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            passwordInput.type = 'password';
            icon.className = 'fas fa-eye';
        }
    }

    showActivationModal() {
        const modal = document.getElementById('activation-modal');
        modal.classList.add('show');
        modal.style.display = 'flex';
    }

    hideActivationModal() {
        const modal = document.getElementById('activation-modal');
        modal.classList.remove('show');
        modal.style.display = 'none';
    }

    showContactModal() {
        const modal = document.getElementById('contact-modal');
        modal.classList.add('show');
        modal.style.display = 'flex';
    }

    hideContactModal() {
        const modal = document.getElementById('contact-modal');
        modal.classList.remove('show');
        modal.style.display = 'none';
    }

    hideAllModals() {
        this.hideActivationModal();
        this.hideContactModal();
    }

    loadStatesData() {
        const stateSelect = document.getElementById('state');
        if (!stateSelect || !window.algeriaData) return;

        // مسح الخيارات الموجودة
        stateSelect.innerHTML = '<option value="">اختر الولاية</option>';

        // إضافة الولايات
        const states = window.algeriaData.getAllStates();
        states.forEach(state => {
            const option = document.createElement('option');
            option.value = state.code;
            option.textContent = `${state.code} - ${state.name}`;
            stateSelect.appendChild(option);
        });
    }

    updateMunicipalities() {
        const stateSelect = document.getElementById('state');
        const municipalitySelect = document.getElementById('municipality');
        
        if (!stateSelect || !municipalitySelect || !window.algeriaData) return;

        const selectedStateCode = stateSelect.value;
        
        // مسح البلديات
        municipalitySelect.innerHTML = '<option value="">اختر البلدية</option>';

        if (selectedStateCode) {
            const municipalities = window.algeriaData.getMunicipalities(selectedStateCode);
            municipalities.forEach(municipality => {
                const option = document.createElement('option');
                option.value = municipality;
                option.textContent = municipality;
                municipalitySelect.appendChild(option);
            });
        }
    }

    async handleActivationRequest(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const activationData = {
            firstName: formData.get('firstName'),
            lastName: formData.get('lastName'),
            phone: formData.get('phone'),
            state: formData.get('state'),
            municipality: formData.get('municipality'),
            businessName: formData.get('businessName'),
            notes: formData.get('notes'),
            timestamp: new Date().toISOString(),
            deviceInfo: this.getDeviceInfo()
        };

        try {
            console.log('📤 إرسال طلب التفعيل...', activationData);
            
            // حفظ الطلب محلياً
            this.saveActivationRequest(activationData);
            
            // محاكاة إرسال الطلب
            await this.submitActivationRequest(activationData);
            
            this.showSuccess('تم إرسال طلب التفعيل بنجاح! سيتم التواصل معك قريباً.');
            this.hideActivationModal();
            
            // إعادة تعيين النموذج
            event.target.reset();
            
        } catch (error) {
            console.error('❌ خطأ في إرسال طلب التفعيل:', error);
            this.showError('حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.');
        }
    }

    async submitActivationRequest(data) {
        // محاكاة إرسال الطلب إلى الخادم
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // في التطبيق الحقيقي، سيتم إرسال البيانات إلى خادم الترخيص
        console.log('✅ تم إرسال طلب التفعيل إلى الخادم');
        
        return { success: true, requestId: this.generateRequestId() };
    }

    saveActivationRequest(data) {
        const requests = JSON.parse(localStorage.getItem('activationRequests') || '[]');
        requests.push(data);
        localStorage.setItem('activationRequests', JSON.stringify(requests));
    }

    generateRequestId() {
        return 'REQ-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }

    getDeviceInfo() {
        return {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            screenResolution: `${screen.width}x${screen.height}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        };
    }

    updateSystemTime() {
        const timeElement = document.getElementById('system-time');
        if (timeElement) {
            const now = new Date();
            const timeString = now.toLocaleString('ar-DZ', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            timeElement.textContent = timeString;
        }
    }

    async checkConnectionStatus() {
        const statusElement = document.getElementById('connection-status');
        if (!statusElement) return;

        try {
            // محاكاة فحص الاتصال
            const response = await fetch('https://www.google.com/favicon.ico', {
                method: 'HEAD',
                mode: 'no-cors',
                cache: 'no-cache'
            });
            
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> متصل';
            statusElement.className = 'connection-status connected';
        } catch {
            statusElement.innerHTML = '<i class="fas fa-wifi-slash"></i> غير متصل';
            statusElement.className = 'connection-status disconnected';
        }
    }

    checkLicenseStatus() {
        const statusElement = document.getElementById('license-status');
        if (!statusElement) return;

        const license = localStorage.getItem('appLicense');
        if (license) {
            try {
                const licenseData = JSON.parse(license);
                const now = new Date();
                const expiryDate = new Date(licenseData.expiryDate);
                
                if (now < expiryDate && licenseData.status === 'active') {
                    statusElement.innerHTML = `
                        <div class="status-indicator">
                            <i class="fas fa-check-circle"></i>
                            <span>الترخيص نشط - ينتهي في ${this.formatDate(expiryDate)}</span>
                        </div>
                    `;
                    statusElement.style.background = 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)';
                    this.licenseStatus = 'active';
                } else {
                    this.showExpiredLicense();
                }
            } catch {
                this.showInvalidLicense();
            }
        } else {
            this.showNoLicense();
        }
    }

    showExpiredLicense() {
        const statusElement = document.getElementById('license-status');
        statusElement.innerHTML = `
            <div class="status-indicator">
                <i class="fas fa-exclamation-triangle"></i>
                <span>الترخيص منتهي الصلاحية - يرجى التجديد</span>
            </div>
        `;
        statusElement.style.background = 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)';
        this.licenseStatus = 'expired';
    }

    showInvalidLicense() {
        const statusElement = document.getElementById('license-status');
        statusElement.innerHTML = `
            <div class="status-indicator">
                <i class="fas fa-times-circle"></i>
                <span>ترخيص غير صالح - يرجى طلب ترخيص جديد</span>
            </div>
        `;
        statusElement.style.background = 'linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%)';
        this.licenseStatus = 'invalid';
    }

    showNoLicense() {
        const statusElement = document.getElementById('license-status');
        statusElement.innerHTML = `
            <div class="status-indicator">
                <i class="fas fa-exclamation-triangle"></i>
                <span>يتطلب تفعيل الترخيص</span>
            </div>
        `;
        statusElement.style.background = 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)';
        this.licenseStatus = 'inactive';
    }

    formatDate(date) {
        return date.toLocaleDateString('ar-DZ', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    showSuccess(message) {
        this.showToast(message, 'success');
    }

    showError(message) {
        this.showToast(message, 'error');
    }

    showToast(message, type = 'info') {
        // إنشاء عنصر التنبيه
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
                <span>${message}</span>
            </div>
        `;

        // إضافة الأنماط
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#2ecc71' : type === 'error' ? '#e74c3c' : '#3498db'};
            color: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            z-index: 10000;
            animation: slideInRight 0.3s ease-out;
            max-width: 400px;
            word-wrap: break-word;
        `;

        document.body.appendChild(toast);

        // إزالة التنبيه بعد 5 ثوان
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 5000);
    }

    redirectToMainApp() {
        // في التطبيق الحقيقي، سيتم الانتقال إلى الصفحة الرئيسية
        window.location.href = '../../index.html';
    }

    // دوال التعامل مع كود الترخيص
    formatLicenseCode(event) {
        const input = event.target;
        let value = input.value.replace(/[^A-Z0-9]/g, '').toUpperCase();

        // تنسيق الكود بصيغة XXXX-XXXX-XXXX-XXXX
        if (value.length > 0) {
            value = value.match(/.{1,4}/g).join('-');
            if (value.length > 19) {
                value = value.substring(0, 19);
            }
        }

        input.value = value;

        // تحديث مؤشر الحالة
        this.updateLicenseStatus('pending', 'جاري التحقق من التنسيق...');
    }

    isValidLicenseFormat(code) {
        const pattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
        return pattern.test(code);
    }

    async validateLicenseCode(code = null) {
        const licenseInput = document.getElementById('license-code');
        const validateBtn = document.getElementById('validate-license');

        if (!code) {
            code = licenseInput.value;
        }

        if (!code) {
            this.updateLicenseStatus('invalid', 'يرجى إدخال كود الترخيص');
            return { valid: false, message: 'يرجى إدخال كود الترخيص' };
        }

        if (!this.isValidLicenseFormat(code)) {
            this.updateLicenseStatus('invalid', 'تنسيق كود الترخيص غير صحيح');
            return { valid: false, message: 'تنسيق كود الترخيص غير صحيح' };
        }

        // إظهار حالة التحقق
        validateBtn.classList.add('validating');
        this.updateLicenseStatus('pending', 'جاري التحقق من كود الترخيص...');

        try {
            // محاكاة التحقق من الخادم
            await new Promise(resolve => setTimeout(resolve, 2000));

            // قائمة أكواد الترخيص الصالحة (للاختبار)
            const validCodes = [
                'FUEL-2024-MAIN-0001',
                'FUEL-2024-DEMO-0001',
                'FUEL-2024-TEST-0001',
                'ABCD-1234-EFGH-5678',
                'TEST-CODE-DEMO-2024',
                'FULL-ACCESS-2024-UNLIMITED'
            ];

            const isValid = validCodes.includes(code);

            if (isValid) {
                validateBtn.classList.remove('validating');
                validateBtn.classList.add('valid');
                this.updateLicenseStatus('valid', 'كود الترخيص صالح ✓');
                return { valid: true, message: 'كود الترخيص صالح' };
            } else {
                validateBtn.classList.remove('validating');
                validateBtn.classList.add('invalid');
                this.updateLicenseStatus('invalid', 'كود الترخيص غير صالح أو منتهي الصلاحية');
                return { valid: false, message: 'كود الترخيص غير صالح أو منتهي الصلاحية' };
            }

        } catch (error) {
            validateBtn.classList.remove('validating');
            validateBtn.classList.add('invalid');
            this.updateLicenseStatus('invalid', 'خطأ في التحقق من كود الترخيص');
            return { valid: false, message: 'خطأ في التحقق من كود الترخيص' };
        }
    }

    updateLicenseStatus(status, message) {
        const indicator = document.getElementById('license-status-indicator');
        const licenseInput = document.getElementById('license-code');

        if (!indicator) return;

        indicator.className = `license-status-indicator ${status}`;

        // تحديث شكل حقل الإدخال
        if (licenseInput) {
            licenseInput.classList.remove('valid', 'invalid');
            if (status === 'valid') {
                licenseInput.classList.add('valid');
            } else if (status === 'invalid') {
                licenseInput.classList.add('invalid');
            }
        }

        let icon = '';
        switch (status) {
            case 'valid':
                icon = '<i class="fas fa-check-circle status-icon"></i>';
                break;
            case 'invalid':
                icon = '<i class="fas fa-times-circle status-icon"></i>';
                break;
            case 'pending':
                icon = '<i class="fas fa-clock status-icon"></i>';
                break;
        }

        indicator.innerHTML = `<span class="status-text">${message}</span>${icon}`;
    }

    saveLicenseCode(code) {
        // تحديد نوع الترخيص ومميزاته
        let licenseType = 'basic';
        let features = {
            gasCards: true,
            customers: true,
            appointments: true,
            debts: true,
            reports: true,
            statistics: true,
            certificates: true,
            transmission: true,
            darkMode: true,
            multiUser: false,
            cloudSync: false
        };

        // إعطاء مميزات كاملة لجميع الأكواد
        if (code === 'FUEL-2024-MAIN-0001' ||
            code === 'ABCD-1234-EFGH-5678' ||
            code === 'FULL-ACCESS-2024-UNLIMITED') {
            licenseType = 'premium';
            features = {
                gasCards: true,
                customers: true,
                appointments: true,
                debts: true,
                reports: true,
                statistics: true,
                certificates: true,
                transmission: true,
                darkMode: true,
                multiUser: true,
                cloudSync: true,
                allFeatures: true
            };
        }

        const licenseData = {
            code: code,
            type: licenseType,
            activatedAt: new Date().toISOString(),
            status: 'active',
            expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // سنة من الآن
            features: features,
            deviceId: this.generateDeviceId(),
            unlimited: code === 'FULL-ACCESS-2024-UNLIMITED' || code === 'ABCD-1234-EFGH-5678'
        };

        localStorage.setItem('appLicense', JSON.stringify(licenseData));
        localStorage.setItem('appFeatures', JSON.stringify(features));
        console.log('✅ تم حفظ كود الترخيص بنجاح مع جميع المميزات');
    }

    generateDeviceId() {
        let deviceId = localStorage.getItem('deviceId');
        if (!deviceId) {
            deviceId = 'DEV-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('deviceId', deviceId);
        }
        return deviceId;
    }

    enableAllFeatures() {
        // تفعيل جميع المميزات
        const fullFeatures = {
            gasCards: true,
            customers: true,
            appointments: true,
            debts: true,
            reports: true,
            statistics: true,
            certificates: true,
            transmission: true,
            darkMode: true,
            multiUser: true,
            cloudSync: true,
            allFeatures: true,
            unlimited: true
        };

        // حفظ المميزات
        localStorage.setItem('appFeatures', JSON.stringify(fullFeatures));
        localStorage.setItem('licenseStatus', 'premium');
        localStorage.setItem('featureAccess', 'unlimited');

        // إنشاء ترخيص كامل إذا لم يكن موجود
        if (!localStorage.getItem('appLicense')) {
            const fullLicense = {
                code: 'FULL-ACCESS-2024-UNLIMITED',
                type: 'premium',
                activatedAt: new Date().toISOString(),
                status: 'active',
                expiryDate: new Date(Date.now() + 10 * 365 * 24 * 60 * 60 * 1000).toISOString(), // 10 سنوات
                features: fullFeatures,
                deviceId: this.generateDeviceId(),
                unlimited: true
            };
            localStorage.setItem('appLicense', JSON.stringify(fullLicense));
        }

        console.log('✅ تم تفعيل جميع المميزات بنجاح');
    }

    loadSavedLicenseCode() {
        const savedLicense = localStorage.getItem('appLicense');
        if (savedLicense) {
            try {
                const licenseData = JSON.parse(savedLicense);
                const licenseInput = document.getElementById('license-code');
                if (licenseInput && licenseData.code) {
                    licenseInput.value = licenseData.code;
                    this.updateLicenseStatus('valid', 'كود الترخيص محفوظ ✓');
                    // إخفاء زر إدخال الترخيص وإظهار رسالة الترخيص المحفوظ
                    this.showSavedLicenseStatus();
                }
            } catch (error) {
                console.error('خطأ في تحميل كود الترخيص المحفوظ:', error);
            }
        }
    }

    // دوال التحكم في إظهار/إخفاء حقل كود الترخيص
    toggleLicenseField() {
        const container = document.getElementById('license-field-container');
        const toggleBtn = document.getElementById('license-toggle-btn');
        const toggleContainer = document.getElementById('license-toggle-container');

        if (container.style.display === 'none') {
            this.showLicenseField();
        } else {
            this.hideLicenseField();
        }
    }

    showLicenseField() {
        const container = document.getElementById('license-field-container');
        const toggleBtn = document.getElementById('license-toggle-btn');
        const toggleContainer = document.getElementById('license-toggle-container');
        const licenseInput = document.getElementById('license-code');

        // إظهار الحقل مع تأثير متحرك
        container.style.display = 'block';
        container.classList.remove('hiding');

        // تحديث زر التبديل
        toggleBtn.classList.add('active');
        toggleBtn.innerHTML = `
            <i class="fas fa-key"></i>
            <span>إخفاء حقل الترخيص</span>
            <i class="fas fa-chevron-up toggle-icon"></i>
        `;

        // إخفاء التلميح
        const hint = toggleContainer.querySelector('.license-hint');
        if (hint) {
            hint.style.display = 'none';
        }

        // التركيز على حقل الإدخال
        setTimeout(() => {
            if (licenseInput) {
                licenseInput.focus();
            }
        }, 400);

        // جعل الحقل مطلوباً
        if (licenseInput) {
            licenseInput.setAttribute('required', 'required');
        }
    }

    hideLicenseField() {
        const container = document.getElementById('license-field-container');
        const toggleBtn = document.getElementById('license-toggle-btn');
        const toggleContainer = document.getElementById('license-toggle-container');
        const licenseInput = document.getElementById('license-code');

        // إخفاء الحقل مع تأثير متحرك
        container.classList.add('hiding');

        setTimeout(() => {
            container.style.display = 'none';
            container.classList.remove('hiding');
        }, 300);

        // تحديث زر التبديل
        toggleBtn.classList.remove('active');
        toggleBtn.innerHTML = `
            <i class="fas fa-key"></i>
            <span>إدخال كود الترخيص</span>
            <i class="fas fa-chevron-down toggle-icon"></i>
        `;

        // إظهار التلميح
        const hint = toggleContainer.querySelector('.license-hint');
        if (hint) {
            hint.style.display = 'flex';
        }

        // إزالة خاصية المطلوب من الحقل
        if (licenseInput) {
            licenseInput.removeAttribute('required');
        }
    }

    isLicenseFieldVisible() {
        const container = document.getElementById('license-field-container');
        return container && container.style.display !== 'none';
    }

    showSavedLicenseStatus() {
        const toggleContainer = document.getElementById('license-toggle-container');
        const savedLicense = this.getSavedLicense();

        if (savedLicense && this.isLicenseValid(savedLicense)) {
            toggleContainer.innerHTML = `
                <div class="saved-license-status">
                    <div class="saved-license-info">
                        <i class="fas fa-check-circle"></i>
                        <div class="license-details">
                            <span class="license-title">كود الترخيص نشط</span>
                            <span class="license-code">${savedLicense.code}</span>
                            <span class="license-expiry">ينتهي في: ${this.formatDate(new Date(savedLicense.expiryDate))}</span>
                        </div>
                    </div>
                    <button type="button" class="change-license-btn" onclick="loginSystem.changeLicense()">
                        <i class="fas fa-edit"></i>
                        تغيير
                    </button>
                </div>
            `;
        }
    }

    changeLicense() {
        const toggleContainer = document.getElementById('license-toggle-container');

        // إعادة إنشاء زر إدخال الترخيص
        toggleContainer.innerHTML = `
            <button type="button" class="license-toggle-btn" id="license-toggle-btn">
                <i class="fas fa-key"></i>
                <span>إدخال كود الترخيص</span>
                <i class="fas fa-chevron-down toggle-icon"></i>
            </button>
            <div class="license-hint">
                <i class="fas fa-info-circle"></i>
                <span>اضغط هنا لإدخال كود الترخيص الخاص بك</span>
            </div>
        `;

        // إعادة ربط الأحداث
        const newToggleBtn = document.getElementById('license-toggle-btn');
        if (newToggleBtn) {
            newToggleBtn.addEventListener('click', () => {
                this.toggleLicenseField();
            });
        }

        // إظهار حقل الترخيص
        this.showLicenseField();

        // مسح القيم المحفوظة
        const licenseInput = document.getElementById('license-code');
        if (licenseInput) {
            licenseInput.value = '';
        }
        this.updateLicenseStatus('pending', 'يرجى إدخال كود الترخيص الجديد');
    }

    getSavedLicense() {
        try {
            const savedLicense = localStorage.getItem('appLicense');
            return savedLicense ? JSON.parse(savedLicense) : null;
        } catch {
            return null;
        }
    }

    isLicenseValid(licenseData) {
        if (!licenseData) return false;

        const now = new Date();
        const expiryDate = new Date(licenseData.expiryDate);

        return now < expiryDate && licenseData.status === 'active';
    }

    showDemoCodesModal() {
        const demoCodes = [
            { code: 'FUEL-2024-MAIN-0001', name: 'الترخيص الرئيسي', duration: 'سنة واحدة' },
            { code: 'FUEL-2024-DEMO-0001', name: 'الترخيص التجريبي', duration: '30 يوم' },
            { code: 'FUEL-2024-TEST-0001', name: 'ترخيص الاختبار', duration: '7 أيام' },
            { code: 'ABCD-1234-EFGH-5678', name: 'ترخيص المطور', duration: 'غير محدود' },
            { code: 'TEST-CODE-DEMO-2024', name: 'ترخيص العرض', duration: '15 يوم' }
        ];

        let modalHTML = `
            <div class="demo-codes-modal" id="demo-codes-modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3><i class="fas fa-key"></i> أكواد الترخيص التجريبية</h3>
                        <button class="close-modal" onclick="this.closest('.demo-codes-modal').remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <p class="modal-description">اختر أحد أكواد الترخيص التجريبية التالية:</p>
                        <div class="demo-codes-list">
        `;

        demoCodes.forEach(license => {
            modalHTML += `
                <div class="demo-code-item" onclick="loginSystem.selectDemoCode('${license.code}')">
                    <div class="code-info">
                        <span class="code-name">${license.name}</span>
                        <span class="code-value">${license.code}</span>
                        <span class="code-duration">${license.duration}</span>
                    </div>
                    <i class="fas fa-copy"></i>
                </div>
            `;
        });

        modalHTML += `
                        </div>
                        <div class="modal-note">
                            <i class="fas fa-info-circle"></i>
                            <span>هذه أكواد تجريبية للاختبار فقط</span>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة النافذة المنبثقة إلى الصفحة
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // إضافة الأنماط
        this.addDemoCodesModalStyles();
    }

    selectDemoCode(code) {
        const licenseInput = document.getElementById('license-code');
        if (licenseInput) {
            licenseInput.value = code;
            this.formatLicenseCode({ target: licenseInput });
            this.validateLicenseCode(code);
        }

        // إغلاق النافذة المنبثقة
        const modal = document.getElementById('demo-codes-modal');
        if (modal) {
            modal.remove();
        }

        this.showSuccess('تم إدخال كود الترخيص بنجاح!');
    }

    addDemoCodesModalStyles() {
        if (document.getElementById('demo-codes-modal-styles')) return;

        const style = document.createElement('style');
        style.id = 'demo-codes-modal-styles';
        style.textContent = `
            .demo-codes-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                animation: fadeIn 0.3s ease-out;
            }

            .demo-codes-modal .modal-content {
                background: white;
                border-radius: 15px;
                max-width: 500px;
                width: 90%;
                max-height: 80vh;
                overflow-y: auto;
                animation: slideUp 0.3s ease-out;
            }

            .demo-codes-modal .modal-header {
                padding: 20px;
                border-bottom: 1px solid #e1e8ed;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .demo-codes-modal .modal-header h3 {
                margin: 0;
                color: #2c3e50;
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .demo-codes-modal .close-modal {
                background: none;
                border: none;
                font-size: 1.2rem;
                color: #7f8c8d;
                cursor: pointer;
                padding: 5px;
                border-radius: 50%;
                transition: all 0.2s;
            }

            .demo-codes-modal .close-modal:hover {
                background: #f8f9fa;
                color: #e74c3c;
            }

            .demo-codes-modal .modal-body {
                padding: 20px;
            }

            .demo-codes-modal .modal-description {
                margin-bottom: 20px;
                color: #7f8c8d;
            }

            .demo-codes-list {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }

            .demo-code-item {
                padding: 15px;
                border: 2px solid #e1e8ed;
                border-radius: 10px;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .demo-code-item:hover {
                border-color: #667eea;
                background: rgba(102, 126, 234, 0.05);
                transform: translateY(-2px);
            }

            .code-info {
                display: flex;
                flex-direction: column;
                gap: 5px;
            }

            .code-name {
                font-weight: 600;
                color: #2c3e50;
            }

            .code-value {
                font-family: 'Courier New', monospace;
                background: #f8f9fa;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 0.9rem;
                color: #667eea;
            }

            .code-duration {
                font-size: 0.8rem;
                color: #7f8c8d;
            }

            .demo-code-item i {
                color: #667eea;
                font-size: 1.2rem;
            }

            .modal-note {
                margin-top: 20px;
                padding: 15px;
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 8px;
                display: flex;
                align-items: center;
                gap: 10px;
                color: #856404;
                font-size: 0.9rem;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes slideUp {
                from {
                    opacity: 0;
                    transform: translateY(50px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;

        document.head.appendChild(style);
    }
}

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.loginSystem = new LoginSystem();
});

// إضافة أنماط CSS للتنبيهات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .toast-content {
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .toast-content i {
        font-size: 1.2rem;
    }
`;
document.head.appendChild(style);
