<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام متعدد اللغات</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="src/styles/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    
    <style>
        .test-container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
            background: var(--card-bg-color);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
        }
        
        .test-section h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            border-bottom: 2px solid var(--secondary-color);
            padding-bottom: 0.5rem;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .test-card {
            padding: 1rem;
            background: var(--hover-bg-color);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }
        
        .language-info {
            background: var(--secondary-color);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .btn-group {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
            flex-wrap: wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-left: 0.5rem;
        }
        
        .status-indicator.active {
            background: var(--success-color);
        }
        
        .status-indicator.inactive {
            background: var(--danger-color);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <!-- معلومات اللغة الحالية -->
        <div class="language-info">
            <h2>🌐 اختبار النظام متعدد اللغات</h2>
            <p>اللغة الحالية: <strong id="current-language-display">العربية</strong></p>
            <p>الاتجاه: <strong id="current-direction-display">من اليمين لليسار</strong></p>
        </div>
        
        <!-- محدد اللغة -->
        <div class="test-section">
            <h3>🔄 تغيير اللغة</h3>
            <div class="language-selector">
                <button type="button" id="language-toggle" class="btn">
                    <i class="fas fa-language"></i>
                    <span id="current-lang">عربي</span>
                </button>
                <div class="language-dropdown" id="language-dropdown">
                    <button type="button" class="lang-option" data-lang="ar">
                        <i class="fas fa-flag"></i> العربية
                    </button>
                    <button type="button" class="lang-option" data-lang="fr">
                        <i class="fas fa-flag"></i> Français
                    </button>
                </div>
            </div>
        </div>
        
        <!-- اختبار القائمة الرئيسية -->
        <div class="test-section">
            <h3 data-translate="nav.dashboard">🏠 القائمة الرئيسية</h3>
            <div class="test-grid">
                <div class="test-card">
                    <h4><i class="fas fa-home"></i> <span data-translate="nav.dashboard">الرئيسية</span></h4>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-id-card"></i> <span data-translate="nav.gasCards">بطاقات الغاز</span></h4>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-calendar-alt"></i> <span data-translate="nav.appointments">المواعيد</span></h4>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-users"></i> <span data-translate="nav.customers">الزبائن</span></h4>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-certificate"></i> <span data-translate="nav.certificates">الشهادات</span></h4>
                </div>
                <div class="test-card">
                    <h4><i class="fas fa-cog"></i> <span data-translate="nav.settings">الإعدادات</span></h4>
                </div>
            </div>
        </div>
        
        <!-- اختبار الأزرار -->
        <div class="test-section">
            <h3>🔘 الأزرار</h3>
            <div class="btn-group">
                <button class="btn primary">
                    <i class="fas fa-plus"></i> <span data-translate="buttons.add">إضافة</span>
                </button>
                <button class="btn secondary">
                    <i class="fas fa-edit"></i> <span data-translate="buttons.edit">تعديل</span>
                </button>
                <button class="btn danger">
                    <i class="fas fa-trash"></i> <span data-translate="buttons.delete">حذف</span>
                </button>
                <button class="btn success">
                    <i class="fas fa-save"></i> <span data-translate="buttons.save">حفظ</span>
                </button>
                <button class="btn">
                    <i class="fas fa-search"></i> <span data-translate="buttons.search">بحث</span>
                </button>
                <button class="btn">
                    <i class="fas fa-print"></i> <span data-translate="buttons.print">طباعة</span>
                </button>
            </div>
        </div>
        
        <!-- اختبار النماذج -->
        <div class="test-section">
            <h3>📝 النماذج</h3>
            <div class="test-grid">
                <div class="form-group">
                    <label data-translate="forms.name">الاسم</label>
                    <input type="text" data-translate-placeholder="forms.name" placeholder="الاسم">
                </div>
                <div class="form-group">
                    <label data-translate="forms.email">البريد الإلكتروني</label>
                    <input type="email" data-translate-placeholder="forms.email" placeholder="البريد الإلكتروني">
                </div>
                <div class="form-group">
                    <label data-translate="forms.phone">رقم الهاتف</label>
                    <input type="tel" data-translate-placeholder="forms.phone" placeholder="رقم الهاتف">
                </div>
                <div class="form-group">
                    <label data-translate="forms.address">العنوان</label>
                    <textarea data-translate-placeholder="forms.address" placeholder="العنوان"></textarea>
                </div>
            </div>
        </div>
        
        <!-- اختبار الحالات -->
        <div class="test-section">
            <h3>📊 الحالات</h3>
            <div class="test-grid">
                <div class="test-card">
                    <span class="status-indicator active"></span>
                    <span data-translate="status.active">نشط</span>
                </div>
                <div class="test-card">
                    <span class="status-indicator inactive"></span>
                    <span data-translate="status.inactive">غير نشط</span>
                </div>
                <div class="test-card">
                    <span class="status-indicator active"></span>
                    <span data-translate="status.completed">مكتمل</span>
                </div>
                <div class="test-card">
                    <span class="status-indicator inactive"></span>
                    <span data-translate="status.cancelled">ملغي</span>
                </div>
            </div>
        </div>
        
        <!-- اختبار الرسائل -->
        <div class="test-section">
            <h3>💬 الرسائل</h3>
            <div class="test-grid">
                <div class="alert success">
                    <i class="fas fa-check-circle"></i>
                    <span data-translate="messages.success">تم بنجاح</span>
                </div>
                <div class="alert danger">
                    <i class="fas fa-exclamation-circle"></i>
                    <span data-translate="messages.error">حدث خطأ</span>
                </div>
                <div class="alert warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span data-translate="messages.warning">تحذير</span>
                </div>
                <div class="alert info">
                    <i class="fas fa-info-circle"></i>
                    <span data-translate="messages.info">معلومات</span>
                </div>
            </div>
        </div>
        
        <!-- معلومات تقنية -->
        <div class="test-section">
            <h3>🔧 معلومات تقنية</h3>
            <div class="test-grid">
                <div class="test-card">
                    <strong>اللغة المحفوظة:</strong>
                    <span id="stored-language">-</span>
                </div>
                <div class="test-card">
                    <strong>اتجاه HTML:</strong>
                    <span id="html-direction">-</span>
                </div>
                <div class="test-card">
                    <strong>خاصية lang:</strong>
                    <span id="html-lang">-</span>
                </div>
                <div class="test-card">
                    <strong>حالة نظام الترجمة:</strong>
                    <span id="translation-status">-</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="scripts/translations.js"></script>
    <script src="scripts/language-manager.js"></script>
    
    <script>
        // تحديث المعلومات التقنية
        function updateTechnicalInfo() {
            document.getElementById('stored-language').textContent = localStorage.getItem('language') || 'ar';
            document.getElementById('html-direction').textContent = document.documentElement.getAttribute('dir') || 'rtl';
            document.getElementById('html-lang').textContent = document.documentElement.getAttribute('lang') || 'ar';
            document.getElementById('translation-status').textContent = window.t ? 'متوفر' : 'غير متوفر';
            
            // تحديث عرض اللغة الحالية
            const currentLang = window.languageManager ? window.languageManager.getCurrentLanguage() : 'ar';
            const langNames = {
                ar: 'العربية',
                fr: 'الفرنسية'
            };
            const dirNames = {
                ar: 'من اليمين لليسار (RTL)',
                fr: 'من اليسار لليمين (LTR)'
            };
            
            document.getElementById('current-language-display').textContent = langNames[currentLang];
            document.getElementById('current-direction-display').textContent = dirNames[currentLang];
        }
        
        // تحديث المعلومات عند تغيير اللغة
        document.addEventListener('languageChanged', updateTechnicalInfo);
        
        // تحديث المعلومات عند التحميل
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(updateTechnicalInfo, 500);
        });
        
        // تحديث دوري للمعلومات
        setInterval(updateTechnicalInfo, 2000);
    </script>
</body>
</html>
