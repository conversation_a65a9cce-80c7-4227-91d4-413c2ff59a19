========================================
    أكواد الترخيص التجريبية
    مؤسسة وقود المستقبل
========================================

📋 أكواد الترخيص الصالحة للاختبار:

1. FUEL-2024-MAIN-0001
   - النوع: ترخيص رئيسي
   - الصلاحية: سنة واحدة
   - الميزات: جميع الميزات متاحة

2. FUEL-2024-DEMO-0001
   - النوع: ترخيص تجريبي
   - الصلاحية: 30 يوم
   - الميزات: ميزات محدودة

3. FUEL-2024-TEST-0001
   - النوع: ترخيص اختبار
   - الصلاحية: 7 أيام
   - الميزات: للاختبار فقط

4. ABCD-1234-EFGH-5678
   - النوع: ترخيص مطور
   - الصلاحية: غير محدودة
   - الميزات: جميع الميزات + أدوات المطور

5. TEST-CODE-DEMO-2024
   - النوع: ترخيص عرض توضيحي
   - الصلاحية: 15 يوم
   - الميزات: للعرض التوضيحي

========================================

🔧 كيفية استخدام أكواد الترخيص:

1. افتح صفحة تسجيل الدخول
2. أدخل اسم المستخدم وكلمة المرور
3. أدخل أحد أكواد الترخيص أعلاه في حقل "كود الترخيص"
4. اضغط على زر التحقق أو اتركه يتحقق تلقائياً
5. عند ظهور علامة ✓ الخضراء، اضغط "تسجيل الدخول"

========================================

⚠️ ملاحظات مهمة:

- هذه أكواد تجريبية للاختبار فقط
- في النسخة النهائية، ستحتاج لكود ترخيص حقيقي
- يتم حفظ كود الترخيص تلقائياً بعد التحقق
- يمكن تغيير كود الترخيص في أي وقت

========================================

🔐 بيانات تسجيل الدخول الافتراضية:

اسم المستخدم: admin
كلمة المرور: admin123

أو

اسم المستخدم: user
كلمة المرور: user123

أو

اسم المستخدم: manager
كلمة المرور: manager123

========================================

📞 للحصول على ترخيص حقيقي:

- البريد الإلكتروني: <EMAIL>
- الهاتف: متوفر في قسم "الاتصال بالمطور"
- الموقع: https://github.com/future-fuel/gas-shop-management

========================================
© 2024 مؤسسة وقود المستقبل
جميع الحقوق محفوظة
========================================
