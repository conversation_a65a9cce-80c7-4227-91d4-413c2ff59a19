@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    تثبيت نظام إدارة بطاقات الغاز
echo ========================================
echo.

REM التحقق من صلاحيات المدير
net session >nul 2>&1
if %errorLevel% == 0 (
    echo تم التحقق من صلاحيات المدير ✓
) else (
    echo خطأ: يتطلب هذا التثبيت صلاحيات المدير
    echo يرجى تشغيل هذا الملف كمدير
    pause
    exit /b 1
)

REM تحديد مسار التثبيت
set "INSTALL_PATH=%ProgramFiles%\CFGPLProgram"
echo مسار التثبيت: %INSTALL_PATH%
echo.

REM إنشاء مجلد التثبيت
if not exist "%INSTALL_PATH%" (
    echo إنشاء مجلد التثبيت...
    mkdir "%INSTALL_PATH%"
)

REM نسخ الملفات الأساسية
echo نسخ الملفات الأساسية...
copy "-.exe" "%INSTALL_PATH%\CFGPLProgram.exe"
copy "*.dll" "%INSTALL_PATH%\" 2>nul
copy "*.pak" "%INSTALL_PATH%\" 2>nul
copy "*.dat" "%INSTALL_PATH%\" 2>nul
copy "*.bin" "%INSTALL_PATH%\" 2>nul
copy "*.json" "%INSTALL_PATH%\" 2>nul
copy "version" "%INSTALL_PATH%\" 2>nul
copy "LICENSE" "%INSTALL_PATH%\" 2>nul
copy "LICENSES.chromium.html" "%INSTALL_PATH%\" 2>nul

REM نسخ المجلدات
echo نسخ مجلد الموارد...
xcopy "resources" "%INSTALL_PATH%\resources\" /e /i /h /y

echo نسخ مجلد اللغات...
xcopy "locales" "%INSTALL_PATH%\locales\" /e /i /h /y

REM إنشاء اختصار سطح المكتب
echo إنشاء اختصار سطح المكتب...
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\نظام إدارة بطاقات الغاز.lnk'); $Shortcut.TargetPath = '%INSTALL_PATH%\CFGPLProgram.exe'; $Shortcut.WorkingDirectory = '%INSTALL_PATH%'; $Shortcut.IconLocation = '%INSTALL_PATH%\resources\app\assets\icon.ico'; $Shortcut.Description = 'نظام إدارة بطاقات الغاز الطبيعي المضغوط'; $Shortcut.Save()}" 2>nul

REM إنشاء اختصار قائمة ابدأ
echo إنشاء اختصار قائمة ابدأ...
powershell -Command "& {$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%ProgramData%\Microsoft\Windows\Start Menu\Programs\نظام إدارة بطاقات الغاز.lnk'); $Shortcut.TargetPath = '%INSTALL_PATH%\CFGPLProgram.exe'; $Shortcut.WorkingDirectory = '%INSTALL_PATH%'; $Shortcut.IconLocation = '%INSTALL_PATH%\resources\app\assets\icon.ico'; $Shortcut.Description = 'نظام إدارة بطاقات الغاز الطبيعي المضغوط'; $Shortcut.Save()}" 2>nul

REM تسجيل في Registry
echo تسجيل التطبيق في النظام...
reg add "HKLM\SOFTWARE\CFGPLProgram" /v "InstallPath" /t REG_SZ /d "%INSTALL_PATH%" /f >nul
reg add "HKLM\SOFTWARE\CFGPLProgram" /v "Version" /t REG_SZ /d "1.0.0" /f >nul
reg add "HKLM\SOFTWARE\CFGPLProgram" /v "InstallDate" /t REG_SZ /d "%DATE%" /f >nul

REM إنشاء أداة إلغاء التثبيت
echo إنشاء أداة إلغاء التثبيت...
echo @echo off > "%INSTALL_PATH%\Uninstall.bat"
echo chcp 65001 ^>nul >> "%INSTALL_PATH%\Uninstall.bat"
echo echo إلغاء تثبيت نظام إدارة بطاقات الغاز... >> "%INSTALL_PATH%\Uninstall.bat"
echo echo. >> "%INSTALL_PATH%\Uninstall.bat"
echo REM حذف الاختصارات >> "%INSTALL_PATH%\Uninstall.bat"
echo del "%%USERPROFILE%%\Desktop\نظام إدارة بطاقات الغاز.lnk" 2^>nul >> "%INSTALL_PATH%\Uninstall.bat"
echo del "%%ProgramData%%\Microsoft\Windows\Start Menu\Programs\نظام إدارة بطاقات الغاز.lnk" 2^>nul >> "%INSTALL_PATH%\Uninstall.bat"
echo echo. >> "%INSTALL_PATH%\Uninstall.bat"
echo REM حذف مفاتيح Registry >> "%INSTALL_PATH%\Uninstall.bat"
echo reg delete "HKLM\SOFTWARE\CFGPLProgram" /f 2^>nul >> "%INSTALL_PATH%\Uninstall.bat"
echo echo. >> "%INSTALL_PATH%\Uninstall.bat"
echo echo تم إلغاء التثبيت بنجاح >> "%INSTALL_PATH%\Uninstall.bat"
echo echo يمكنك الآن حذف مجلد التثبيت يدوياً إذا أردت >> "%INSTALL_PATH%\Uninstall.bat"
echo pause >> "%INSTALL_PATH%\Uninstall.bat"

echo.
echo ========================================
echo تم التثبيت بنجاح!
echo ========================================
echo.
echo مسار التثبيت: %INSTALL_PATH%
echo تم إنشاء اختصارات على سطح المكتب وقائمة ابدأ
echo لإلغاء التثبيت: تشغيل Uninstall.bat من مجلد التثبيت
echo.

set /p run_now="هل تريد تشغيل التطبيق الآن؟ (y/n): "
if /i "%run_now%"=="y" (
    start "" "%INSTALL_PATH%\CFGPLProgram.exe"
)

echo.
pause
